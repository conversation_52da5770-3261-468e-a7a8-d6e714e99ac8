/**
 * 🎨 CREATIVE PAINTING V3 - JOGO DE PINTURA CRIATIVA COM MÚLTIPLAS ATIVIDADES
 * Portal Betina V3 - Jogo educativo com 6 atividades diversificadas
 */

import React, { useState, useEffect, useCallback, useRef, useContext } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { SystemContext } from '../../components/context/SystemContext.jsx'
import { useAccessibilityContext } from '../../components/context/AccessibilityContext'
import { v4 as uuidv4 } from 'uuid';

// Importa configurações e métricas específicas do jogo
import PAINTING_CONFIG from './CreativePaintingConfig.js'

// Importa o componente padrão de tela de dificuldade
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx'

// 🧠 Integração com sistema unificado de métricas
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic'

// 🎨 Importar coletores avançados de pintura criativa
import { CreativePaintingCollectorsHub } from './collectors/index.js'
// 🔄 Importar hook multissensorial
import { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js'
// 🎯 Importar hook orquestrador terapêutico
import { useTherapeuticOrchestrator } from '../../hooks/useTherapeuticOrchestrator.js'

// Importa estilos modulares baseados no preview
import styles from './CreativePainting.module.css'

// 🎯 SISTEMA DE ATIVIDADES FUNCIONAIS REDESENHADO V3 - CREATIVE PAINTING
// 3 atividades práticas e funcionais conforme solicitado
const ACTIVITY_TYPES = {
  FREE_PAINTING: {
    id: 'free_painting',
    name: 'Pintura Livre',
    icon: '🎨',
    description: 'Desenhe livremente o que quiser no canvas',
    therapeuticFocus: 'creative_expression',
    metricsCollected: ['stroke_count', 'color_usage', 'canvas_coverage', 'drawing_time'],
    cognitiveFunction: 'creative_motor_coordination',
    component: 'FreePaintingActivity'
  },
  ASSISTED_PAINTING: {
    id: 'assisted_painting',
    name: 'Pintura Assistida',
    icon: '🖍️',
    description: 'Clique nas áreas destacadas para colorir desenhos pré-definidos',
    therapeuticFocus: 'guided_motor_skills',
    metricsCollected: ['area_completion', 'color_accuracy', 'click_precision', 'completion_time'],
    cognitiveFunction: 'guided_fine_motor_skills',
    component: 'AssistedPaintingActivity'
  },
  CANVAS_PAINTING: {
    id: 'canvas_painting',
    name: 'Canvas de Pintura',
    icon: '🖼️',
    description: 'Use ferramentas de pintura avançadas no canvas digital',
    therapeuticFocus: 'advanced_motor_skills',
    metricsCollected: ['tool_usage', 'brush_control', 'layer_management', 'artistic_complexity'],
    cognitiveFunction: 'advanced_creative_coordination',
    component: 'CanvasPaintingActivity'
  },
  PATTERN_PAINTING: {
    id: 'pattern_painting',
    name: 'Pintura de Padrões',
    icon: '🔷',
    description: 'Complete padrões visuais usando cores e formas específicas',
    therapeuticFocus: 'pattern_recognition_motor',
    metricsCollected: ['pattern_accuracy', 'color_matching', 'completion_rate', 'motor_precision'],
    cognitiveFunction: 'visual_motor_integration',
    component: 'PatternPaintingActivity'
  },
  COLLABORATIVE_PAINTING: {
    id: 'collaborative_painting',
    name: 'Pintura Colaborativa',
    icon: '👥',
    description: 'Trabalhe em conjunto para criar uma obra de arte compartilhada',
    therapeuticFocus: 'social_creative_skills',
    metricsCollected: ['collaboration_level', 'turn_taking', 'creative_contribution', 'social_interaction'],
    cognitiveFunction: 'social_motor_coordination',
    component: 'CollaborativePaintingActivity'
  }
};

function CreativePaintingGame({ onBack }) {
  const { user, sessionId, ttsEnabled = true } = useContext(SystemContext);
  const { settings } = useAccessibilityContext();

  // =====================================================
  // 🔊 SISTEMA DE TEXT-TO-SPEECH (TTS) PADRONIZADO
  // =====================================================

  // Estado do TTS com persistência
  const [ttsActive, setTtsActive] = useState(() => {
    const saved = localStorage.getItem('creativePainting_ttsActive');
    return saved !== null ? JSON.parse(saved) : true;
  });

  // Função para alternar TTS
  const toggleTTS = useCallback(() => {
    setTtsActive(prev => {
      const newState = !prev;
      localStorage.setItem('creativePainting_ttsActive', JSON.stringify(newState));

      // Cancelar qualquer fala em andamento se desabilitando
      if (!newState && 'speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }

      return newState;
    });
  }, []);

  // Função TTS padronizada
  const speak = useCallback((text, options = {}) => {
    // Verificar se TTS está ativo
    if (!ttsActive || !('speechSynthesis' in window)) {
      return;
    }

    // Cancelar qualquer fala anterior
    window.speechSynthesis.cancel();

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'pt-BR';
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;

    window.speechSynthesis.speak(utterance);
  }, [ttsActive]);

  // Estados de tela - COM TELA DE DIFICULDADE PADRONIZADA
  const [showStartScreen, setShowStartScreen] = useState(true);
  const [currentDifficulty, setCurrentDifficulty] = useState('easy');

  // Referência para métricas
  const metricsRef = useRef(null);

  // 🎯 ESTADO TERAPÊUTICO REDESENHADO V3 - MÉTRICAS DIRECIONADAS
  const [gameState, setGameState] = useState({
    status: 'start', // 'start', 'playing', 'paused', 'finished'
    score: 0,
    round: 1,
    totalRounds: 10,
    difficulty: 'easy',
    accuracy: 100,
    roundStartTime: null,

    // 🎨 SISTEMA DE MÉTRICAS TERAPÊUTICAS ESPECÍFICAS
    therapeuticMetrics: {
      // Métricas de cores - cada cor usada é um parâmetro
      colorMetrics: {
        colorFrequency: {}, // Frequência de uso de cada cor
        colorCombinations: [], // Combinações de cores utilizadas
        emotionalColorMapping: {}, // Mapeamento cor-emoção
        colorTransitionPatterns: [], // Padrões de transição entre cores
        dominantColors: [], // Cores dominantes por sessão
        colorHarmony: 0 // Índice de harmonia cromática
      },

      // Métricas motoras - cada movimento é um parâmetro
      motorMetrics: {
        strokePrecision: [], // Precisão de cada traço
        handSteadiness: [], // Estabilidade da mão por movimento
        pressureVariation: [], // Variação de pressão aplicada
        movementFluency: [], // Fluidez dos movimentos
        coordinationLevel: 0, // Nível de coordenação geral
        motorConsistency: 0 // Consistência motora
      },

      // Métricas espaciais - cada posicionamento é um parâmetro
      spatialMetrics: {
        spatialDistribution: [], // Distribuição espacial dos elementos
        compositionBalance: 0, // Equilíbrio da composição
        areaUtilization: 0, // Utilização da área disponível
        spatialPlanning: [], // Evidências de planejamento espacial
        symmetryPatterns: [], // Padrões de simetria
        spatialOrganization: 0 // Nível de organização espacial
      },

      // Métricas criativas - cada escolha é um parâmetro
      creativityMetrics: {
        originalityScore: 0, // Pontuação de originalidade
        complexityPatterns: [], // Padrões de complexidade
        innovationFrequency: [], // Frequência de inovações
        creativeConsistency: 0, // Consistência criativa
        abstractThinking: [], // Evidências de pensamento abstrato
        creativeConfidence: 0 // Confiança criativa
      },

      // Métricas atencionais - cada foco é um parâmetro
      attentionMetrics: {
        attentionDuration: [], // Duração de cada período de atenção
        focusConsistency: 0, // Consistência do foco
        distractionPatterns: [], // Padrões de distração
        taskCompletionRate: 0, // Taxa de conclusão de tarefas
        concentrationLevel: [], // Níveis de concentração
        attentionSustainability: 0 // Sustentabilidade da atenção
      }
    },

    // 🎯 DADOS DE SESSÃO PARA ANÁLISE
    sessionData: {
      startTime: null,
      endTime: null,
      totalInteractions: 0,
      uniqueActions: [],
      behavioralPatterns: [],
      therapeuticInsights: []
    },

    strokes: [], // 🎨 Array de pinceladas para rastreamento detalhado
    colorsUsed: new Set(['#ff6b6b']), // 🎨 Conjunto de cores utilizadas

    // 🎯 Sistema de atividades funcionais redesenhado
    currentActivity: ACTIVITY_TYPES.FREE_PAINTING.id,
    activityCycle: [
      ACTIVITY_TYPES.FREE_PAINTING.id,
      ACTIVITY_TYPES.ASSISTED_PAINTING.id,
      ACTIVITY_TYPES.CANVAS_PAINTING.id
    ],
    activityIndex: 0,
    roundsPerActivity: 3, // Menos rounds, mais foco na qualidade das métricas
    activityRoundCount: 0,
    activitiesCompleted: [],

    // 🎯 Dados específicos das atividades funcionais
    activityData: {
      free_painting: {
        canvas: null,
        brushSize: 5,
        currentColor: '#000000',
        availableColors: ['#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF', '#FFA500', '#800080', '#000000', '#FFFFFF'],
        strokes: [],
        isDrawing: false,
        lastPoint: null
      },
      assisted_painting: {
        currentTemplate: 'house', // casa, árvore, sol, etc.
        availableTemplates: [
          { id: 'house', name: 'Casa', emoji: '🏠', areas: [] },
          { id: 'tree', name: 'Árvore', emoji: '🌳', areas: [] },
          { id: 'sun', name: 'Sol', emoji: '☀️', areas: [] },
          { id: 'flower', name: 'Flor', emoji: '🌸', areas: [] },
          { id: 'car', name: 'Carro', emoji: '🚗', areas: [] }
        ],
        completedAreas: [],
        currentColor: '#FF0000',
        totalAreas: 0,
        completionPercentage: 0
      },
      canvas_painting: {
        brushSize: 10,
        brushType: 'round', // round, square, spray
        currentColor: '#000000',
        availableTools: ['brush', 'eraser', 'fill', 'line', 'circle', 'rectangle'],
        currentTool: 'brush',
        layers: [{ id: 'layer1', visible: true, opacity: 1 }],
        currentLayer: 'layer1',
        canvasHistory: [],
        historyIndex: -1
      }
    },

    // 🎯 Feedback e animações
    showFeedback: false,
    feedbackType: null,
    feedbackMessage: '',
    showCelebration: false,

    // 🎯 Métricas comportamentais
    responseTime: 0,
    hesitationCount: 0,
    helpUsed: false,
    consecutiveCorrect: 0,
    totalAttempts: 0,
    correctAttempts: 0
  });

  // 🧠 Integração com sistema unificado de métricas
  const {
    collectMetrics,
    processGameSession,
    startUnifiedSession,
    processAdvancedMetrics, // Para análise de criatividade e expressão
    sessionId: unifiedSessionId,
    isSessionActive,
    recordInteraction,
    endUnifiedSession
  } = useUnifiedGameLogic('CreativePainting')

  // 🎨 Inicializar coletores avançados de pintura criativa
  const [collectorsHub] = useState(() => new CreativePaintingCollectorsHub())

  // 🔄 Hook multissensorial integrado
  const {
    initializeSession: initMultisensory,
    recordInteraction: recordMultisensoryInteraction,
    finalizeSession: finalizeMultisensory,
    updateData: updateMultisensoryData,
    multisensoryData,
    isInitialized: multisensoryInitialized
  } = useMultisensoryIntegration(sessionId, {
    gameType: 'creative-painting',
    sensorTypes: {
      visual: true,
      haptic: true,
      tts: ttsEnabled,
      gestural: true,
      biometric: true
    },
    adaptiveMode: true,
    autoUpdate: true,
    enablePatternAnalysis: true,
    logLevel: 'info',
    learningStyle: user?.profile?.learningStyle || 'visual'
  });

  // 🎯 Hook orquestrador terapêutico integrado
  const therapeuticOrchestrator = useTherapeuticOrchestrator({ 
    gameType: 'creative-painting',
    collectorsHub,
    recordMultisensoryInteraction,
    autoUpdate: true,
    logLevel: 'info'
  });

  // Estados para métricas avançadas de criatividade
  const [sessionStartTime, setSessionStartTime] = useState(null)
  const [brushStrokes, setBrushStrokes] = useState([])
  const [colorTransitions, setColorTransitions] = useState([])
  const [creativityMetrics, setCreativityMetrics] = useState({
    originalityScore: 0,
    complexityScore: 0,
    expressiveRange: 0,
    spatialUtilization: 0,
    totalStrokes: 0,
    lastStrokeTime: null
  })

  // Referências
  const canvasRef = useRef(null)
  const strokesContainerRef = useRef(null)
  const drawingRef = useRef({ isDrawing: false, lastPoint: null })

  // Função para inicializar o jogo baseado na dificuldade
  const initializeGame = useCallback((difficulty) => {
    setCurrentDifficulty(difficulty)

    // Iniciar sessão unificada quando usuário escolher dificuldade
    startUnifiedSession(difficulty, {
      gameType: 'CreativePainting',
      timestamp: new Date().toISOString()
    })

    // Configurar brushes baseado na dificuldade
    const difficultyConfig = PAINTING_CONFIG.difficulties[difficulty]
    const defaultBrushSize = difficultyConfig?.defaultBrush || 10

    setGameState(prev => ({
      ...prev,
      brushSize: defaultBrushSize,
      startTime: Date.now(),
      strokes: [],
      undoStack: [],
      redoStack: [],
      colorsUsed: new Set(['#ff6b6b']),
      savedCount: 0,
      showPlaceholder: true
    }))

    setShowStartScreen(false)
  }, [startUnifiedSession])

  // Inicializar sessão (sem inicializar jogo automaticamente)
  useEffect(() => {
    if (!sessionStartTime) {
      setSessionStartTime(Date.now())
      // Sessão será iniciada apenas quando usuário escolher dificuldade
    }
  }, [sessionStartTime])

  // Timer para atualizar estatísticas
  useEffect(() => {
    const timer = setInterval(() => {
      setGameState(prev => ({ ...prev })) // Force re-render para atualizar tempo
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // Cleanup do TTS quando componente é desmontado
  useEffect(() => {
    return () => {
      // Cancelar qualquer TTS ativo quando sair do jogo
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, []);

  // Calcular tempo decorrido
  const getElapsedTime = useCallback(() => {
    const elapsed = Math.floor((Date.now() - gameState.startTime) / 1000)
    const minutes = Math.floor(elapsed / 60)
    const seconds = elapsed % 60
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }, [gameState.startTime])

  // =====================================================
  // 🎯 SISTEMA DE COLETA DE MÉTRICAS TERAPÊUTICAS EM TEMPO REAL
  // =====================================================

  // 🎨 COLETA DE MÉTRICAS DE CORES - Cada cor é um parâmetro
  const collectColorMetrics = useCallback((color, action, timestamp = Date.now()) => {
    setGameState(prev => {
      const newColorMetrics = { ...prev.therapeuticMetrics.colorMetrics };

      // Frequência de cores
      newColorMetrics.colorFrequency[color] = (newColorMetrics.colorFrequency[color] || 0) + 1;

      // Transições de cores
      if (prev.therapeuticMetrics.colorMetrics.lastColor && prev.therapeuticMetrics.colorMetrics.lastColor !== color) {
        const transition = `${prev.therapeuticMetrics.colorMetrics.lastColor}->${color}`;
        newColorMetrics.colorTransitionPatterns.push({
          from: prev.therapeuticMetrics.colorMetrics.lastColor,
          to: color,
          timestamp,
          action
        });
      }

      newColorMetrics.lastColor = color;

      // Coletar para processadores
      collectMetrics({
        type: 'color_usage',
        color,
        action,
        timestamp,
        frequency: newColorMetrics.colorFrequency[color],
        sessionData: prev.sessionData
      });

      return {
        ...prev,
        therapeuticMetrics: {
          ...prev.therapeuticMetrics,
          colorMetrics: newColorMetrics
        }
      };
    });
  }, [collectMetrics]);

  // ✏️ COLETA DE MÉTRICAS MOTORAS - Cada movimento é um parâmetro
  const collectMotorMetrics = useCallback((strokeData) => {
    setGameState(prev => {
      const newMotorMetrics = { ...prev.therapeuticMetrics.motorMetrics };

      // Precisão do traço
      const precision = calculateStrokePrecision(strokeData);
      newMotorMetrics.strokePrecision.push(precision);

      // Estabilidade da mão
      const steadiness = calculateHandSteadiness(strokeData.points);
      newMotorMetrics.handSteadiness.push(steadiness);

      // Variação de pressão
      if (strokeData.pressure) {
        newMotorMetrics.pressureVariation.push(strokeData.pressure);
      }

      // Fluidez do movimento
      const fluency = calculateMovementFluency(strokeData.points);
      newMotorMetrics.movementFluency.push(fluency);

      // Coletar para processadores
      collectMetrics({
        type: 'motor_skills',
        precision,
        steadiness,
        fluency,
        pressure: strokeData.pressure,
        timestamp: strokeData.timestamp,
        sessionData: prev.sessionData
      });

      return {
        ...prev,
        therapeuticMetrics: {
          ...prev.therapeuticMetrics,
          motorMetrics: newMotorMetrics
        }
      };
    });
  }, [collectMetrics]);

  // 🗺️ COLETA DE MÉTRICAS ESPACIAIS - Cada posicionamento é um parâmetro
  const collectSpatialMetrics = useCallback((position, element, action) => {
    setGameState(prev => {
      const newSpatialMetrics = { ...prev.therapeuticMetrics.spatialMetrics };

      // Distribuição espacial
      newSpatialMetrics.spatialDistribution.push({
        x: position.x,
        y: position.y,
        element,
        action,
        timestamp: Date.now()
      });

      // Planejamento espacial (evidências)
      if (action === 'planned_placement') {
        newSpatialMetrics.spatialPlanning.push({
          position,
          element,
          timestamp: Date.now()
        });
      }

      // Coletar para processadores
      collectMetrics({
        type: 'spatial_organization',
        position,
        element,
        action,
        distribution: newSpatialMetrics.spatialDistribution.length,
        timestamp: Date.now(),
        sessionData: prev.sessionData
      });

      return {
        ...prev,
        therapeuticMetrics: {
          ...prev.therapeuticMetrics,
          spatialMetrics: newSpatialMetrics
        }
      };
    });
  }, [collectMetrics]);

  // 🌟 COLETA DE MÉTRICAS CRIATIVAS - Cada escolha é um parâmetro
  const collectCreativityMetrics = useCallback((creativeAction) => {
    setGameState(prev => {
      const newCreativityMetrics = { ...prev.therapeuticMetrics.creativityMetrics };

      // Padrões de complexidade
      if (creativeAction.complexity) {
        newCreativityMetrics.complexityPatterns.push({
          level: creativeAction.complexity,
          timestamp: Date.now(),
          context: creativeAction.context
        });
      }

      // Frequência de inovação
      if (creativeAction.isInnovative) {
        newCreativityMetrics.innovationFrequency.push({
          type: creativeAction.type,
          timestamp: Date.now(),
          description: creativeAction.description
        });
      }

      // Pensamento abstrato
      if (creativeAction.isAbstract) {
        newCreativityMetrics.abstractThinking.push({
          level: creativeAction.abstractLevel,
          timestamp: Date.now(),
          manifestation: creativeAction.manifestation
        });
      }

      // Coletar para processadores
      collectMetrics({
        type: 'creativity_expression',
        action: creativeAction,
        timestamp: Date.now(),
        sessionData: prev.sessionData
      });

      return {
        ...prev,
        therapeuticMetrics: {
          ...prev.therapeuticMetrics,
          creativityMetrics: newCreativityMetrics
        }
      };
    });
  }, [collectMetrics]);

  // 🎯 COLETA DE MÉTRICAS ATENCIONAIS - Cada foco é um parâmetro
  const collectAttentionMetrics = useCallback((attentionEvent) => {
    setGameState(prev => {
      const newAttentionMetrics = { ...prev.therapeuticMetrics.attentionMetrics };

      // Duração da atenção
      if (attentionEvent.type === 'focus_duration') {
        newAttentionMetrics.attentionDuration.push({
          duration: attentionEvent.duration,
          task: attentionEvent.task,
          timestamp: Date.now()
        });
      }

      // Padrões de distração
      if (attentionEvent.type === 'distraction') {
        newAttentionMetrics.distractionPatterns.push({
          cause: attentionEvent.cause,
          duration: attentionEvent.duration,
          recovery: attentionEvent.recovery,
          timestamp: Date.now()
        });
      }

      // Níveis de concentração
      if (attentionEvent.type === 'concentration_level') {
        newAttentionMetrics.concentrationLevel.push({
          level: attentionEvent.level,
          task: attentionEvent.task,
          timestamp: Date.now()
        });
      }

      // Coletar para processadores
      collectMetrics({
        type: 'attention_focus',
        event: attentionEvent,
        timestamp: Date.now(),
        sessionData: prev.sessionData
      });

      return {
        ...prev,
        therapeuticMetrics: {
          ...prev.therapeuticMetrics,
          attentionMetrics: newAttentionMetrics
        }
      };
    });
  }, [collectMetrics]);

  // =====================================================
  // 🧮 FUNÇÕES AUXILIARES DE CÁLCULO DE MÉTRICAS
  // =====================================================

  // Calcular precisão do traço
  const calculateStrokePrecision = useCallback((strokeData) => {
    if (!strokeData.points || strokeData.points.length < 2) return 0;

    let totalDeviation = 0;
    let targetPath = strokeData.targetPath || [];

    if (targetPath.length === 0) {
      // Se não há caminho alvo, calcular suavidade do traço
      for (let i = 1; i < strokeData.points.length - 1; i++) {
        const prev = strokeData.points[i - 1];
        const curr = strokeData.points[i];
        const next = strokeData.points[i + 1];

        const angle1 = Math.atan2(curr.y - prev.y, curr.x - prev.x);
        const angle2 = Math.atan2(next.y - curr.y, next.x - curr.x);
        const angleDiff = Math.abs(angle1 - angle2);

        totalDeviation += angleDiff;
      }

      return Math.max(0, 1 - (totalDeviation / strokeData.points.length));
    }

    // Calcular desvio do caminho alvo
    strokeData.points.forEach((point, index) => {
      if (targetPath[index]) {
        const distance = Math.sqrt(
          Math.pow(point.x - targetPath[index].x, 2) +
          Math.pow(point.y - targetPath[index].y, 2)
        );
        totalDeviation += distance;
      }
    });

    const averageDeviation = totalDeviation / strokeData.points.length;
    return Math.max(0, 1 - (averageDeviation / 100)); // Normalizar para 0-1
  }, []);

  // Calcular estabilidade da mão
  const calculateHandSteadiness = useCallback((points) => {
    if (!points || points.length < 3) return 0;

    let totalJitter = 0;
    for (let i = 1; i < points.length - 1; i++) {
      const prev = points[i - 1];
      const curr = points[i];
      const next = points[i + 1];

      // Calcular jitter como desvio da linha reta
      const expectedX = (prev.x + next.x) / 2;
      const expectedY = (prev.y + next.y) / 2;

      const jitter = Math.sqrt(
        Math.pow(curr.x - expectedX, 2) +
        Math.pow(curr.y - expectedY, 2)
      );

      totalJitter += jitter;
    }

    const averageJitter = totalJitter / (points.length - 2);
    return Math.max(0, 1 - (averageJitter / 50)); // Normalizar para 0-1
  }, []);

  // Calcular fluidez do movimento
  const calculateMovementFluency = useCallback((points) => {
    if (!points || points.length < 2) return 0;

    let totalSpeed = 0;
    let speedVariations = 0;
    let previousSpeed = 0;

    for (let i = 1; i < points.length; i++) {
      const prev = points[i - 1];
      const curr = points[i];

      const distance = Math.sqrt(
        Math.pow(curr.x - prev.x, 2) +
        Math.pow(curr.y - prev.y, 2)
      );

      const timeDiff = (curr.timestamp || i) - (prev.timestamp || i - 1);
      const speed = distance / Math.max(timeDiff, 1);

      totalSpeed += speed;

      if (i > 1) {
        speedVariations += Math.abs(speed - previousSpeed);
      }

      previousSpeed = speed;
    }

    const averageSpeed = totalSpeed / (points.length - 1);
    const speedConsistency = 1 - (speedVariations / totalSpeed);

    return Math.max(0, Math.min(1, speedConsistency));
  }, []);

  // =====================================================
  // 🎯 SISTEMA DE ATIVIDADES TERAPÊUTICAS
  // =====================================================

  // Função para trocar de atividade terapêutica
  const switchActivity = useCallback((activityId) => {
    const activity = Object.values(ACTIVITY_TYPES).find(a => a.id === activityId);
    if (!activity) return;

    setGameState(prev => ({
      ...prev,
      currentActivity: activityId,
      // Resetar dados específicos da atividade
      activityData: {
        ...prev.activityData,
        [activityId]: prev.activityData[activityId] || {}
      }
    }));

    // Coletar métrica de mudança de atividade
    collectMetrics({
      type: 'activity_switch',
      from: gameState.currentActivity,
      to: activityId,
      therapeuticFocus: activity.therapeuticFocus,
      timestamp: Date.now()
    });

    console.log(`🎯 Atividade alterada para: ${activity.name}`);
  }, [gameState.currentActivity, collectMetrics]);

  // Função para renderizar a atividade atual
  const renderCurrentActivity = useCallback(() => {
    const activity = Object.values(ACTIVITY_TYPES).find(a => a.id === gameState.currentActivity);
    if (!activity) return <div>Atividade não encontrada</div>;

    switch (gameState.currentActivity) {
      case ACTIVITY_TYPES.FREE_PAINTING.id:
        return renderFreePainting();
      case ACTIVITY_TYPES.ASSISTED_PAINTING.id:
        return renderAssistedPainting();
      case ACTIVITY_TYPES.CANVAS_PAINTING.id:
        return renderCanvasPainting();
      case ACTIVITY_TYPES.PATTERN_PAINTING.id:
        return renderPatternPainting();
      case ACTIVITY_TYPES.COLLABORATIVE_PAINTING.id:
        return renderCollaborativePainting();
      default:
        return renderFreePainting();
    }
  }, [gameState.currentActivity]);

  // 🎨 RENDERIZAÇÃO: Pintura Livre
  const renderFreePainting = useCallback(() => {
    const activityData = gameState.activityData.free_painting || {};

    return (
      <div className={styles.paintingActivity}>
        <div className={styles.activityHeader}>
          <h3>🎨 Pintura Livre</h3>
          <p>Desenhe livremente o que quiser no canvas</p>
        </div>

        <div className={styles.freePaintingArea}>
          {/* Paleta de cores */}
          <div className={styles.colorPalette}>
            <h4>Cores</h4>
            <div className={styles.colorGrid}>
              {activityData.availableColors?.map((color, index) => (
                <motion.div
                  key={color}
                  className={`${styles.colorBtn} ${activityData.currentColor === color ? styles.active : ''}`}
                  style={{ backgroundColor: color }}
                  onClick={() => handleColorChange(color)}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                />
              ))}
            </div>
          </div>

          {/* Controles de pincel */}
          <div className={styles.brushControls}>
            <h4>Pincel</h4>
            <div className={styles.brushSizeControl}>
              <label>Tamanho: {activityData.brushSize}px</label>
              <input
                type="range"
                min="1"
                max="20"
                value={activityData.brushSize}
                onChange={(e) => handleBrushSizeChange(parseInt(e.target.value))}
                className={styles.brushSlider}
              />
            </div>
          </div>

          {/* Canvas de pintura livre */}
          <div className={styles.canvasContainer}>
            <canvas
              ref={canvasRef}
              width={600}
              height={400}
              className={styles.paintingCanvas}
              onMouseDown={handleCanvasMouseDown}
              onMouseMove={handleCanvasMouseMove}
              onMouseUp={handleCanvasMouseUp}
              onTouchStart={handleCanvasTouchStart}
              onTouchMove={handleCanvasTouchMove}
              onTouchEnd={handleCanvasTouchEnd}
            />
          </div>

          {/* Controles de ação */}
          <div className={styles.actionControls}>
            <button className={styles.clearBtn} onClick={handleClearCanvas}>
              🗑️ Limpar
            </button>
            <button className={styles.saveBtn} onClick={handleSaveDrawing}>
              💾 Salvar
            </button>
          </div>
        </div>
      </div>
    );
  }, [gameState.activityData]);

  // 🖍️ RENDERIZAÇÃO: Pintura Assistida
  const renderAssistedPainting = useCallback(() => {
    const activityData = gameState.activityData.assisted_painting || {};
    const currentTemplate = activityData.availableTemplates?.find(t => t.id === activityData.currentTemplate);

    return (
      <div className={styles.paintingActivity}>
        <div className={styles.activityHeader}>
          <h3>🖍️ Pintura Assistida</h3>
          <p>Clique nas áreas destacadas para colorir o desenho</p>
        </div>

        <div className={styles.assistedPaintingArea}>
          {/* Seletor de templates */}
          <div className={styles.templateSelector}>
            <h4>Escolha um desenho</h4>
            <div className={styles.templateGrid}>
              {activityData.availableTemplates?.map((template) => (
                <motion.div
                  key={template.id}
                  className={`${styles.templateBtn} ${activityData.currentTemplate === template.id ? styles.active : ''}`}
                  onClick={() => handleTemplateChange(template.id)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <div className={styles.templateIcon}>{template.emoji}</div>
                  <div className={styles.templateName}>{template.name}</div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Paleta de cores para pintura assistida */}
          <div className={styles.colorPalette}>
            <h4>Cores</h4>
            <div className={styles.colorGrid}>
              {gameState.activityData.free_painting?.availableColors?.map((color, index) => (
                <motion.div
                  key={color}
                  className={`${styles.colorBtn} ${activityData.currentColor === color ? styles.active : ''}`}
                  style={{ backgroundColor: color }}
                  onClick={() => handleAssistedColorChange(color)}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                />
              ))}
            </div>
          </div>

          {/* Canvas de pintura assistida */}
          <div className={styles.canvasContainer}>
            <div className={styles.assistedCanvas}>
              <canvas
                ref={canvasRef}
                width={600}
                height={400}
                className={styles.paintingCanvas}
                onClick={handleAssistedCanvasClick}
              />
              {/* Overlay com áreas clicáveis */}
              <div className={styles.clickableAreas}>
                {renderClickableAreas(currentTemplate)}
              </div>
            </div>
          </div>

          {/* Progresso da pintura */}
          <div className={styles.progressSection}>
            <h4>Progresso: {activityData.completionPercentage}%</h4>
            <div className={styles.progressBar}>
              <div
                className={styles.progressFill}
                style={{ width: `${activityData.completionPercentage}%` }}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }, [gameState.activityData]);

  // 🖼️ RENDERIZAÇÃO: Canvas de Pintura
  const renderCanvasPainting = useCallback(() => {
    const activityData = gameState.activityData.canvas_painting || {};

    return (
      <div className={styles.paintingActivity}>
        <div className={styles.activityHeader}>
          <h3>🖼️ Canvas de Pintura</h3>
          <p>Use ferramentas avançadas de pintura no canvas digital</p>
        </div>

        <div className={styles.canvasPaintingArea}>
          {/* Barra de ferramentas */}
          <div className={styles.toolBar}>
            <h4>Ferramentas</h4>
            <div className={styles.toolGrid}>
              {activityData.availableTools?.map((tool) => (
                <motion.div
                  key={tool}
                  className={`${styles.toolBtn} ${activityData.currentTool === tool ? styles.active : ''}`}
                  onClick={() => handleToolChange(tool)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  title={getToolName(tool)}
                >
                  {getToolIcon(tool)}
                </motion.div>
              ))}
            </div>
          </div>

          {/* Controles avançados */}
          <div className={styles.advancedControls}>
            <div className={styles.brushControls}>
              <h4>Pincel</h4>
              <div className={styles.controlGroup}>
                <label>Tamanho: {activityData.brushSize}px</label>
                <input
                  type="range"
                  min="1"
                  max="50"
                  value={activityData.brushSize}
                  onChange={(e) => handleCanvasBrushSizeChange(parseInt(e.target.value))}
                  className={styles.brushSlider}
                />
              </div>
              <div className={styles.controlGroup}>
                <label>Tipo:</label>
                <select
                  value={activityData.brushType}
                  onChange={(e) => handleBrushTypeChange(e.target.value)}
                  className={styles.brushTypeSelect}
                >
                  <option value="round">Redondo</option>
                  <option value="square">Quadrado</option>
                  <option value="spray">Spray</option>
                </select>
              </div>
            </div>

            {/* Paleta de cores */}
            <div className={styles.colorPalette}>
              <h4>Cores</h4>
              <div className={styles.colorGrid}>
                {gameState.activityData.free_painting?.availableColors?.map((color, index) => (
                  <motion.div
                    key={color}
                    className={`${styles.colorBtn} ${activityData.currentColor === color ? styles.active : ''}`}
                    style={{ backgroundColor: color }}
                    onClick={() => handleCanvasColorChange(color)}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  />
                ))}
              </div>
            </div>
          </div>

          {/* Canvas avançado */}
          <div className={styles.canvasContainer}>
            <canvas
              ref={canvasRef}
              width={800}
              height={600}
              className={styles.advancedCanvas}
              onMouseDown={handleCanvasMouseDown}
              onMouseMove={handleCanvasMouseMove}
              onMouseUp={handleCanvasMouseUp}
              onTouchStart={handleCanvasTouchStart}
              onTouchMove={handleCanvasTouchMove}
              onTouchEnd={handleCanvasTouchEnd}
            />
          </div>

          {/* Controles de ação avançados */}
          <div className={styles.actionControls}>
            <button className={styles.undoBtn} onClick={handleUndo} disabled={activityData.historyIndex <= 0}>
              ↶ Desfazer
            </button>
            <button className={styles.redoBtn} onClick={handleRedo} disabled={activityData.historyIndex >= activityData.canvasHistory?.length - 1}>
              ↷ Refazer
            </button>
            <button className={styles.clearBtn} onClick={handleClearCanvas}>
              🗑️ Limpar
            </button>
            <button className={styles.saveBtn} onClick={handleSaveDrawing}>
              💾 Salvar
            </button>
          </div>
        </div>
      </div>
    );
  }, [gameState.activityData]);

  // 🔷 RENDERIZAÇÃO: Pintura de Padrões
  const renderPatternPainting = useCallback(() => {
    const activityData = gameState.activityData.pattern_painting || {};

    return (
      <div className={styles.paintingActivity}>
        <div className={styles.activityHeader}>
          <h3>🔷 Pintura de Padrões</h3>
          <p>Complete padrões visuais usando cores e formas específicas</p>
        </div>

        <div className={styles.patternPaintingArea}>
          {/* Padrão a ser completado */}
          <div className={styles.patternTemplate}>
            <h4>Padrão para Completar</h4>
            <div className={styles.patternGrid}>
              {activityData.currentPattern?.map((row, rowIndex) => (
                <div key={rowIndex} className={styles.patternRow}>
                  {row.map((cell, cellIndex) => (
                    <div
                      key={`${rowIndex}-${cellIndex}`}
                      className={`${styles.patternCell} ${cell.completed ? styles.completed : ''}`}
                      style={{ backgroundColor: cell.targetColor }}
                      onClick={() => handlePatternCellClick(rowIndex, cellIndex)}
                    >
                      {cell.completed ? '✓' : ''}
                    </div>
                  ))}
                </div>
              ))}
            </div>
          </div>

          {/* Paleta de cores para padrões */}
          <div className={styles.patternColorPalette}>
            <h4>Cores Disponíveis</h4>
            <div className={styles.colorGrid}>
              {activityData.availableColors?.map((color) => (
                <div
                  key={color}
                  className={`${styles.colorOption} ${activityData.selectedColor === color ? styles.selected : ''}`}
                  style={{ backgroundColor: color }}
                  onClick={() => handlePatternColorSelect(color)}
                />
              ))}
            </div>
          </div>

          {/* Progresso do padrão */}
          <div className={styles.patternProgress}>
            <div className={styles.progressBar}>
              <div
                className={styles.progressFill}
                style={{ width: `${activityData.completionPercentage || 0}%` }}
              />
            </div>
            <span>{activityData.completionPercentage || 0}% Completo</span>
          </div>
        </div>
      </div>
    );
  }, [gameState.activityData]);

  // 👥 RENDERIZAÇÃO: Pintura Colaborativa
  const renderCollaborativePainting = useCallback(() => {
    const activityData = gameState.activityData.collaborative_painting || {};

    return (
      <div className={styles.paintingActivity}>
        <div className={styles.activityHeader}>
          <h3>👥 Pintura Colaborativa</h3>
          <p>Trabalhe em conjunto para criar uma obra de arte compartilhada</p>
        </div>

        <div className={styles.collaborativePaintingArea}>
          {/* Canvas compartilhado */}
          <div className={styles.sharedCanvas}>
            <canvas
              ref={canvasRef}
              width={600}
              height={400}
              className={styles.collaborativeCanvas}
              onMouseDown={handleCanvasMouseDown}
              onMouseMove={handleCanvasMouseMove}
              onMouseUp={handleCanvasMouseUp}
            />
          </div>

          {/* Informações de colaboração */}
          <div className={styles.collaborationInfo}>
            <div className={styles.participantsList}>
              <h4>Participantes</h4>
              {activityData.participants?.map((participant) => (
                <div key={participant.id} className={styles.participant}>
                  <div
                    className={styles.participantColor}
                    style={{ backgroundColor: participant.color }}
                  />
                  <span>{participant.name}</span>
                  <span className={styles.contributionLevel}>
                    {participant.contributionPercentage}%
                  </span>
                </div>
              ))}
            </div>

            <div className={styles.turnIndicator}>
              <h4>Vez de:</h4>
              <div className={styles.currentTurn}>
                {activityData.currentTurn?.name || 'Aguardando...'}
              </div>
            </div>
          </div>

          {/* Ferramentas colaborativas */}
          <div className={styles.collaborativeTools}>
            <div className={styles.toolSection}>
              <h4>Sua Cor</h4>
              <div
                className={styles.myColor}
                style={{ backgroundColor: activityData.myColor }}
              />
            </div>

            <div className={styles.toolSection}>
              <h4>Tamanho do Pincel</h4>
              <input
                type="range"
                min="1"
                max="20"
                value={activityData.brushSize || 5}
                onChange={(e) => handleCollaborativeBrushSizeChange(e.target.value)}
                className={styles.brushSizeSlider}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }, [gameState.activityData]);

  // 🌟 RENDERIZAÇÃO: Perfil de Expressão Criativa
  const renderCreativeExpressionProfiling = useCallback(() => {
    return (
      <div className={styles.therapeuticActivity}>
        <div className={styles.activityHeader}>
          <h3>🌟 Perfil de Expressão Criativa</h3>
          <p>Expresse sua criatividade livremente para análise de originalidade</p>
        </div>

        <div className={styles.creativeProfilingArea}>
          <div className={styles.creativeCanvas}>
            <canvas
              ref={canvasRef}
              width={600}
              height={400}
              className={styles.therapeuticCanvas}
              onMouseDown={handleCanvasMouseDown}
              onMouseMove={handleCanvasMouseMove}
              onMouseUp={handleCanvasMouseUp}
            />
          </div>

          <div className={styles.creativityMetrics}>
            <div className={styles.metricCard}>
              <span>Originalidade</span>
              <span>{(gameState.therapeuticMetrics.creativityMetrics.originalityScore * 100).toFixed(0)}%</span>
            </div>
            <div className={styles.metricCard}>
              <span>Complexidade</span>
              <span>{gameState.therapeuticMetrics.creativityMetrics.complexityPatterns.length}</span>
            </div>
            <div className={styles.metricCard}>
              <span>Inovação</span>
              <span>{gameState.therapeuticMetrics.creativityMetrics.innovationFrequency.length}</span>
            </div>
          </div>
        </div>
      </div>
    );
  }, [gameState.therapeuticMetrics]);

  // 🎯 RENDERIZAÇÃO: Medição de Foco Atencional
  const renderAttentionFocusMeasurement = useCallback(() => {
    return (
      <div className={styles.therapeuticActivity}>
        <div className={styles.activityHeader}>
          <h3>🎯 Medição de Foco Atencional</h3>
          <p>Complete tarefas dirigidas para medir sua capacidade de atenção sustentada</p>
        </div>

        <div className={styles.attentionMeasurementArea}>
          <div className={styles.focusTask}>
            <div className={styles.taskInstruction}>
              Pinte apenas dentro das áreas destacadas
            </div>
            <canvas
              ref={canvasRef}
              width={600}
              height={400}
              className={styles.therapeuticCanvas}
              onMouseDown={handleCanvasMouseDown}
              onMouseMove={handleCanvasMouseMove}
              onMouseUp={handleCanvasMouseUp}
            />
          </div>

          <div className={styles.attentionMetrics}>
            <div className={styles.metricCard}>
              <span>Duração Foco</span>
              <span>{gameState.therapeuticMetrics.attentionMetrics.attentionDuration.length > 0
                ? `${(gameState.therapeuticMetrics.attentionMetrics.attentionDuration.slice(-1)[0].duration / 1000).toFixed(1)}s`
                : '0s'}</span>
            </div>
            <div className={styles.metricCard}>
              <span>Consistência</span>
              <span>{(gameState.therapeuticMetrics.attentionMetrics.focusConsistency * 100).toFixed(0)}%</span>
            </div>
            <div className={styles.metricCard}>
              <span>Distrações</span>
              <span>{gameState.therapeuticMetrics.attentionMetrics.distractionPatterns.length}</span>
            </div>
          </div>
        </div>
      </div>
    );
  }, [gameState.therapeuticMetrics]);

  // =====================================================
  // 🎯 HANDLERS DE EVENTOS FUNCIONAIS
  // =====================================================

  // 🎨 HANDLERS PARA PINTURA LIVRE
  const handleColorChange = useCallback((color) => {
    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        free_painting: {
          ...prev.activityData.free_painting,
          currentColor: color
        }
      }
    }));

    console.log(`🎨 Cor alterada para: ${color}`);
  }, []);

  const handleBrushSizeChange = useCallback((size) => {
    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        free_painting: {
          ...prev.activityData.free_painting,
          brushSize: size
        }
      }
    }));

    console.log(`🖌️ Tamanho do pincel alterado para: ${size}px`);
  }, []);

  const handleClearCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Limpar strokes
      setGameState(prev => ({
        ...prev,
        strokes: [],
        activityData: {
          ...prev.activityData,
          free_painting: {
            ...prev.activityData.free_painting,
            strokes: []
          }
        }
      }));

      console.log('🗑️ Canvas limpo');
    }
  }, []);

  const handleSaveDrawing = useCallback(() => {
    const canvas = canvasRef.current;
    if (canvas) {
      const dataURL = canvas.toDataURL('image/png');
      const link = document.createElement('a');
      link.download = `pintura_${Date.now()}.png`;
      link.href = dataURL;
      link.click();

      console.log('💾 Desenho salvo');
    }
  }, []);

  // 🖍️ HANDLERS PARA PINTURA ASSISTIDA
  const handleTemplateChange = useCallback((templateId) => {
    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        assisted_painting: {
          ...prev.activityData.assisted_painting,
          currentTemplate: templateId,
          completedAreas: [],
          completionPercentage: 0
        }
      }
    }));

    // Redesenhar template no canvas
    redrawAssistedTemplate(templateId);

    console.log(`🖍️ Template alterado para: ${templateId}`);
  }, []);

  const handleAssistedColorChange = useCallback((color) => {
    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        assisted_painting: {
          ...prev.activityData.assisted_painting,
          currentColor: color
        }
      }
    }));

    console.log(`🖍️ Cor da pintura assistida alterada para: ${color}`);
  }, []);

  const handleAssistedCanvasClick = useCallback((event) => {
    const rect = event.target.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Verificar se clicou em uma área válida
    const clickedArea = findClickableArea(x, y);
    if (clickedArea && !gameState.activityData.assisted_painting.completedAreas.includes(clickedArea.id)) {
      fillArea(clickedArea, gameState.activityData.assisted_painting.currentColor);

      setGameState(prev => {
        const newCompletedAreas = [...prev.activityData.assisted_painting.completedAreas, clickedArea.id];
        const totalAreas = prev.activityData.assisted_painting.totalAreas || 10;
        const completionPercentage = Math.round((newCompletedAreas.length / totalAreas) * 100);

        return {
          ...prev,
          activityData: {
            ...prev.activityData,
            assisted_painting: {
              ...prev.activityData.assisted_painting,
              completedAreas: newCompletedAreas,
              completionPercentage
            }
          }
        };
      });

      console.log(`🖍️ Área ${clickedArea.id} colorida`);
    }
  }, [gameState.activityData.assisted_painting]);

  // 🖼️ HANDLERS PARA CANVAS DE PINTURA
  const handleToolChange = useCallback((tool) => {
    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        canvas_painting: {
          ...prev.activityData.canvas_painting,
          currentTool: tool
        }
      }
    }));

    console.log(`🛠️ Ferramenta alterada para: ${tool}`);
  }, []);

  const handleCanvasBrushSizeChange = useCallback((size) => {
    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        canvas_painting: {
          ...prev.activityData.canvas_painting,
          brushSize: size
        }
      }
    }));

    console.log(`🖌️ Tamanho do pincel do canvas alterado para: ${size}px`);
  }, []);

  const handleBrushTypeChange = useCallback((type) => {
    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        canvas_painting: {
          ...prev.activityData.canvas_painting,
          brushType: type
        }
      }
    }));

    console.log(`🖌️ Tipo do pincel alterado para: ${type}`);
  }, []);

  const handleCanvasColorChange = useCallback((color) => {
    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        canvas_painting: {
          ...prev.activityData.canvas_painting,
          currentColor: color
        }
      }
    }));

    console.log(`🎨 Cor do canvas alterada para: ${color}`);
  }, []);

  const handleUndo = useCallback(() => {
    setGameState(prev => {
      const canvasData = prev.activityData.canvas_painting;
      if (canvasData.historyIndex > 0) {
        const newIndex = canvasData.historyIndex - 1;
        const imageData = canvasData.canvasHistory[newIndex];

        // Restaurar canvas
        const canvas = canvasRef.current;
        if (canvas && imageData) {
          const ctx = canvas.getContext('2d');
          const img = new Image();
          img.onload = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, 0, 0);
          };
          img.src = imageData;
        }

        return {
          ...prev,
          activityData: {
            ...prev.activityData,
            canvas_painting: {
              ...canvasData,
              historyIndex: newIndex
            }
          }
        };
      }
      return prev;
    });

    console.log('↶ Desfazer ação');
  }, []);

  const handleRedo = useCallback(() => {
    setGameState(prev => {
      const canvasData = prev.activityData.canvas_painting;
      if (canvasData.historyIndex < canvasData.canvasHistory.length - 1) {
        const newIndex = canvasData.historyIndex + 1;
        const imageData = canvasData.canvasHistory[newIndex];

        // Restaurar canvas
        const canvas = canvasRef.current;
        if (canvas && imageData) {
          const ctx = canvas.getContext('2d');
          const img = new Image();
          img.onload = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, 0, 0);
          };
          img.src = imageData;
        }

        return {
          ...prev,
          activityData: {
            ...prev.activityData,
            canvas_painting: {
              ...canvasData,
              historyIndex: newIndex
            }
          }
        };
      }
      return prev;
    });

    console.log('↷ Refazer ação');
  }, []);

  // 🔷 HANDLERS PARA PINTURA DE PADRÕES
  const handlePatternCellClick = useCallback((rowIndex, cellIndex) => {
    setGameState(prev => {
      const patternData = prev.activityData.pattern_painting || {};
      const currentPattern = patternData.currentPattern || [];

      if (currentPattern[rowIndex] && currentPattern[rowIndex][cellIndex]) {
        const newPattern = [...currentPattern];
        newPattern[rowIndex] = [...newPattern[rowIndex]];
        newPattern[rowIndex][cellIndex] = {
          ...newPattern[rowIndex][cellIndex],
          completed: true,
          userColor: patternData.selectedColor
        };

        // Calcular progresso
        const totalCells = currentPattern.flat().length;
        const completedCells = newPattern.flat().filter(cell => cell.completed).length;
        const completionPercentage = Math.round((completedCells / totalCells) * 100);

        return {
          ...prev,
          activityData: {
            ...prev.activityData,
            pattern_painting: {
              ...patternData,
              currentPattern: newPattern,
              completionPercentage
            }
          }
        };
      }
      return prev;
    });

    console.log(`🔷 Célula do padrão [${rowIndex}, ${cellIndex}] preenchida`);
  }, []);

  const handlePatternColorSelect = useCallback((color) => {
    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        pattern_painting: {
          ...prev.activityData.pattern_painting,
          selectedColor: color
        }
      }
    }));

    console.log(`🔷 Cor do padrão selecionada: ${color}`);
  }, []);

  // 👥 HANDLERS PARA PINTURA COLABORATIVA
  const handleCollaborativeBrushSizeChange = useCallback((size) => {
    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        collaborative_painting: {
          ...prev.activityData.collaborative_painting,
          brushSize: parseInt(size)
        }
      }
    }));

    console.log(`👥 Tamanho do pincel alterado para: ${size}`);
  }, []);

  // =====================================================
  // 🛠️ FUNÇÕES AUXILIARES
  // =====================================================

  // Função para obter nome da ferramenta
  const getToolName = useCallback((tool) => {
    const toolNames = {
      brush: 'Pincel',
      eraser: 'Borracha',
      fill: 'Balde de Tinta',
      line: 'Linha',
      circle: 'Círculo',
      rectangle: 'Retângulo'
    };
    return toolNames[tool] || tool;
  }, []);

  // Função para obter ícone da ferramenta
  const getToolIcon = useCallback((tool) => {
    const toolIcons = {
      brush: '🖌️',
      eraser: '🧽',
      fill: '🪣',
      line: '📏',
      circle: '⭕',
      rectangle: '⬜'
    };
    return toolIcons[tool] || '🛠️';
  }, []);

  // Função para redesenhar template assistido
  const redrawAssistedTemplate = useCallback((templateId) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Desenhar template baseado no ID
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 2;

    switch (templateId) {
      case 'house':
        drawHouseTemplate(ctx);
        break;
      case 'tree':
        drawTreeTemplate(ctx);
        break;
      case 'sun':
        drawSunTemplate(ctx);
        break;
      case 'flower':
        drawFlowerTemplate(ctx);
        break;
      case 'car':
        drawCarTemplate(ctx);
        break;
      default:
        drawHouseTemplate(ctx);
    }

    console.log(`🖍️ Template ${templateId} redesenhado`);
  }, []);

  // Função para encontrar área clicável
  const findClickableArea = useCallback((x, y) => {
    // Implementação simplificada - retorna área baseada na posição
    const areas = [
      { id: 'area1', x: 100, y: 100, width: 100, height: 100 },
      { id: 'area2', x: 250, y: 100, width: 100, height: 100 },
      { id: 'area3', x: 400, y: 100, width: 100, height: 100 },
      // Adicionar mais áreas conforme necessário
    ];

    return areas.find(area =>
      x >= area.x && x <= area.x + area.width &&
      y >= area.y && y <= area.y + area.height
    );
  }, []);

  // Função para preencher área
  const fillArea = useCallback((area, color) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    ctx.fillStyle = color;
    ctx.fillRect(area.x, area.y, area.width, area.height);

    console.log(`🎨 Área ${area.id} preenchida com ${color}`);
  }, []);

  // Função para renderizar áreas clicáveis
  const renderClickableAreas = useCallback((template) => {
    if (!template) return null;

    const areas = [
      { id: 'area1', x: 100, y: 100, width: 100, height: 100 },
      { id: 'area2', x: 250, y: 100, width: 100, height: 100 },
      { id: 'area3', x: 400, y: 100, width: 100, height: 100 },
    ];

    return areas.map(area => (
      <div
        key={area.id}
        className={styles.clickableArea}
        style={{
          position: 'absolute',
          left: area.x,
          top: area.y,
          width: area.width,
          height: area.height,
          border: '2px dashed rgba(255, 255, 255, 0.5)',
          cursor: 'pointer'
        }}
      />
    ));
  }, []);

  // =====================================================
  // 🎨 FUNÇÕES DE DESENHO DE TEMPLATES
  // =====================================================

  // Desenhar template de casa
  const drawHouseTemplate = useCallback((ctx) => {
    // Base da casa
    ctx.strokeRect(200, 250, 200, 150);

    // Telhado
    ctx.beginPath();
    ctx.moveTo(180, 250);
    ctx.lineTo(300, 180);
    ctx.lineTo(420, 250);
    ctx.closePath();
    ctx.stroke();

    // Porta
    ctx.strokeRect(270, 320, 60, 80);

    // Janelas
    ctx.strokeRect(220, 280, 40, 40);
    ctx.strokeRect(340, 280, 40, 40);

    console.log('🏠 Template de casa desenhado');
  }, []);

  // Desenhar template de árvore
  const drawTreeTemplate = useCallback((ctx) => {
    // Tronco
    ctx.strokeRect(290, 300, 20, 100);

    // Copa da árvore
    ctx.beginPath();
    ctx.arc(300, 250, 80, 0, 2 * Math.PI);
    ctx.stroke();

    // Galhos
    ctx.beginPath();
    ctx.moveTo(250, 220);
    ctx.lineTo(280, 240);
    ctx.moveTo(350, 220);
    ctx.lineTo(320, 240);
    ctx.stroke();

    console.log('🌳 Template de árvore desenhado');
  }, []);

  // Desenhar template de sol
  const drawSunTemplate = useCallback((ctx) => {
    // Círculo do sol
    ctx.beginPath();
    ctx.arc(300, 200, 60, 0, 2 * Math.PI);
    ctx.stroke();

    // Raios do sol
    const rayLength = 30;
    for (let i = 0; i < 8; i++) {
      const angle = (i * Math.PI) / 4;
      const startX = 300 + Math.cos(angle) * 70;
      const startY = 200 + Math.sin(angle) * 70;
      const endX = 300 + Math.cos(angle) * (70 + rayLength);
      const endY = 200 + Math.sin(angle) * (70 + rayLength);

      ctx.beginPath();
      ctx.moveTo(startX, startY);
      ctx.lineTo(endX, endY);
      ctx.stroke();
    }

    console.log('☀️ Template de sol desenhado');
  }, []);

  // Desenhar template de flor
  const drawFlowerTemplate = useCallback((ctx) => {
    // Caule
    ctx.strokeRect(295, 300, 10, 100);

    // Centro da flor
    ctx.beginPath();
    ctx.arc(300, 250, 20, 0, 2 * Math.PI);
    ctx.stroke();

    // Pétalas
    const petalCount = 6;
    for (let i = 0; i < petalCount; i++) {
      const angle = (i * 2 * Math.PI) / petalCount;
      const petalX = 300 + Math.cos(angle) * 40;
      const petalY = 250 + Math.sin(angle) * 40;

      ctx.beginPath();
      ctx.arc(petalX, petalY, 15, 0, 2 * Math.PI);
      ctx.stroke();
    }

    // Folhas
    ctx.beginPath();
    ctx.ellipse(280, 320, 15, 25, -Math.PI / 4, 0, 2 * Math.PI);
    ctx.stroke();

    ctx.beginPath();
    ctx.ellipse(320, 320, 15, 25, Math.PI / 4, 0, 2 * Math.PI);
    ctx.stroke();

    console.log('🌸 Template de flor desenhado');
  }, []);

  // Desenhar template de carro
  const drawCarTemplate = useCallback((ctx) => {
    // Corpo do carro
    ctx.strokeRect(150, 280, 300, 80);

    // Teto do carro
    ctx.strokeRect(200, 240, 200, 40);

    // Rodas
    ctx.beginPath();
    ctx.arc(200, 380, 30, 0, 2 * Math.PI);
    ctx.stroke();

    ctx.beginPath();
    ctx.arc(400, 380, 30, 0, 2 * Math.PI);
    ctx.stroke();

    // Janelas
    ctx.strokeRect(220, 250, 60, 25);
    ctx.strokeRect(320, 250, 60, 25);

    // Faróis
    ctx.beginPath();
    ctx.arc(460, 300, 15, 0, 2 * Math.PI);
    ctx.stroke();

    ctx.beginPath();
    ctx.arc(460, 340, 15, 0, 2 * Math.PI);
    ctx.stroke();

    console.log('🚗 Template de carro desenhado');
  }, []);

  // =====================================================
  // 🎨 HANDLERS DE CANVAS TERAPÊUTICOS
  // =====================================================

  // Handler para mouse down no canvas
  const handleCanvasMouseDown = useCallback((event) => {
    const rect = event.target.getBoundingClientRect();
    const point = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top,
      timestamp: Date.now(),
      pressure: event.pressure || 0.5
    };

    drawingRef.current.isDrawing = true;
    drawingRef.current.lastPoint = point;

    // Obter configurações baseadas na atividade atual
    let currentColor, brushSize;

    switch (gameState.currentActivity) {
      case ACTIVITY_TYPES.FREE_PAINTING.id:
        currentColor = gameState.activityData.free_painting?.currentColor || '#000000';
        brushSize = gameState.activityData.free_painting?.brushSize || 5;
        break;
      case ACTIVITY_TYPES.CANVAS_PAINTING.id:
        currentColor = gameState.activityData.canvas_painting?.currentColor || '#000000';
        brushSize = gameState.activityData.canvas_painting?.brushSize || 10;
        break;
      default:
        currentColor = '#000000';
        brushSize = 5;
    }

    // Iniciar novo traço
    const newStroke = {
      id: uuidv4(),
      points: [point],
      color: currentColor,
      brushSize: brushSize,
      startTime: Date.now(),
      activity: gameState.currentActivity
    };

    setGameState(prev => ({
      ...prev,
      strokes: [...prev.strokes, newStroke]
    }));

    console.log('🎨 Início do traço:', point);
  }, [gameState.currentActivity, gameState.activityData]);

  // Handler para mouse move no canvas
  const handleCanvasMouseMove = useCallback((event) => {
    if (!drawingRef.current.isDrawing) return;

    const rect = event.target.getBoundingClientRect();
    const point = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top,
      timestamp: Date.now(),
      pressure: event.pressure || 0.5
    };

    // Obter configurações baseadas na atividade atual
    let currentColor, brushSize, brushType;

    switch (gameState.currentActivity) {
      case ACTIVITY_TYPES.FREE_PAINTING.id:
        currentColor = gameState.activityData.free_painting?.currentColor || '#000000';
        brushSize = gameState.activityData.free_painting?.brushSize || 5;
        brushType = 'round';
        break;
      case ACTIVITY_TYPES.CANVAS_PAINTING.id:
        currentColor = gameState.activityData.canvas_painting?.currentColor || '#000000';
        brushSize = gameState.activityData.canvas_painting?.brushSize || 10;
        brushType = gameState.activityData.canvas_painting?.brushType || 'round';
        break;
      default:
        currentColor = '#000000';
        brushSize = 5;
        brushType = 'round';
    }

    // Atualizar último traço
    setGameState(prev => {
      const strokes = [...prev.strokes];
      const lastStroke = strokes[strokes.length - 1];
      if (lastStroke) {
        lastStroke.points.push(point);
      }
      return { ...prev, strokes };
    });

    // Desenhar no canvas
    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      ctx.strokeStyle = currentColor;
      ctx.lineWidth = brushSize;
      ctx.lineCap = brushType === 'round' ? 'round' : 'square';
      ctx.lineJoin = 'round';

      if (drawingRef.current.lastPoint) {
        ctx.beginPath();
        ctx.moveTo(drawingRef.current.lastPoint.x, drawingRef.current.lastPoint.y);
        ctx.lineTo(point.x, point.y);
        ctx.stroke();
      }
    }

    drawingRef.current.lastPoint = point;
  }, [gameState.currentActivity, gameState.activityData]);

  // Handler para mouse up no canvas
  const handleCanvasMouseUp = useCallback(() => {
    if (!drawingRef.current.isDrawing) return;

    drawingRef.current.isDrawing = false;
    drawingRef.current.lastPoint = null;

    // Finalizar traço e coletar métricas
    const lastStroke = gameState.strokes && gameState.strokes.length > 0 ? gameState.strokes[gameState.strokes.length - 1] : null;
    if (lastStroke) {
      const strokeData = {
        ...lastStroke,
        endTime: Date.now(),
        duration: Date.now() - lastStroke.startTime
      };

      // Salvar no histórico para canvas avançado
      if (gameState.currentActivity === ACTIVITY_TYPES.CANVAS_PAINTING.id) {
        const canvas = canvasRef.current;
        if (canvas) {
          const dataURL = canvas.toDataURL();
          setGameState(prev => {
            const canvasData = prev.activityData.canvas_painting;
            const newHistory = [...canvasData.canvasHistory.slice(0, canvasData.historyIndex + 1), dataURL];

            return {
              ...prev,
              activityData: {
                ...prev.activityData,
                canvas_painting: {
                  ...canvasData,
                  canvasHistory: newHistory,
                  historyIndex: newHistory.length - 1
                }
              }
            };
          });
        }
      }

      // Coletar métricas básicas
      collectMetrics({
        type: 'stroke_completed',
        activity: gameState.currentActivity,
        duration: strokeData.duration,
        points: strokeData.points.length,
        timestamp: Date.now()
      });
    }

    console.log('🎨 Fim do traço');
  }, [gameState.strokes, gameState.currentActivity, collectMetrics]);

  // Handlers para touch (mobile)
  const handleCanvasTouchStart = useCallback((event) => {
    event.preventDefault();
    const touch = event.touches[0];
    const mouseEvent = new MouseEvent('mousedown', {
      clientX: touch.clientX,
      clientY: touch.clientY
    });
    handleCanvasMouseDown(mouseEvent);
  }, [handleCanvasMouseDown]);

  const handleCanvasTouchMove = useCallback((event) => {
    event.preventDefault();
    const touch = event.touches[0];
    const mouseEvent = new MouseEvent('mousemove', {
      clientX: touch.clientX,
      clientY: touch.clientY
    });
    handleCanvasMouseMove(mouseEvent);
  }, [handleCanvasMouseMove]);

  const handleCanvasTouchEnd = useCallback((event) => {
    event.preventDefault();
    handleCanvasMouseUp();
  }, [handleCanvasMouseUp]);

  // Função para voltar à tela inicial
  const backToStart = useCallback(() => {
    setShowStartScreen(true)
  }, [])

  // Funções de seleção
  const selectColor = useCallback((color) => {
    setGameState(prev => ({
      ...prev,
      currentColor: color,
      colorsUsed: new Set([...prev.colorsUsed, color])
    }))
  }, [])

  const selectBrush = useCallback((brush) => {
    setGameState(prev => ({ ...prev, currentBrush: brush }))
  }, [])

  const updateBrushSize = useCallback((size) => {
    setGameState(prev => ({ ...prev, brushSize: parseInt(size) }))
  }, [])

  const selectTemplate = useCallback((template) => {
    setGameState(prev => ({ ...prev, selectedTemplate: template }))
    if (template === 'blank') {
      clearCanvas()
    } else {
      // Aqui seria implementada a lógica de carregar templates pré-definidos
      console.log(`Template selecionado: ${template}`)
    }
  }, [])

  // 🎨 Função para registrar pinceladas para análise multissensorial
  const recordBrushStroke = useCallback(async (strokeData) => {
    try {
      if (!recordMultisensoryInteraction || !collectorsHub) return;

      // Registrar interação com dados específicos de pintura
      await recordMultisensoryInteraction('brush_stroke', {
        interactionType: 'creative_action',
        gameSpecificData: {
          ...strokeData,
          sessionId: `creativePainting_${Date.now()}`,
          timestamp: Date.now(),
          activityType: 'painting',
          difficulty: currentDifficulty,
          brushMetrics: {
            color: gameState.currentColor,
            size: gameState.brushSize,
            brush: gameState.currentBrush
          }
        },
        multisensoryProcessing: {
          visualProcessing: { 
            colorPerception: 0.8, 
            spatialAwareness: 0.7, 
            visualAttention: 0.9 
          },
          motorProcessing: { 
            fineMotoSkills: 0.8, 
            handEyeCoordination: 0.9, 
            movementPrecision: 0.7 
          },
          cognitiveProcessing: { 
            creativity: 0.9, 
            decisionMaking: 0.8, 
            patternRecognition: 0.6 
          }
        }
      });

      // Atualizar métricas de criatividade
      setCreativityMetrics(prev => ({
        ...prev,
        totalStrokes: prev.totalStrokes + 1,
        lastStrokeTime: Date.now()
      }));

      console.log('🎨 Pincelada registrada:', strokeData);
      
    } catch (error) {
      console.warn('⚠️ Erro ao registrar pincelada:', error);
    }
  }, [recordMultisensoryInteraction, collectorsHub, currentDifficulty, gameState.currentColor, gameState.brushSize, gameState.currentBrush]);

  // Funções de desenho
  const startDrawing = useCallback((event) => {
    const rect = canvasRef.current.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top
    
    drawingRef.current.isDrawing = true
    drawingRef.current.lastPoint = { x, y }
    
    // Salvar estado para undo
    setGameState(prev => ({
      ...prev,
      undoStack: [...prev.undoStack, [...prev.strokes]],
      redoStack: [], // Limpar redo stack
      showPlaceholder: false
    }))
    
    // 🎨 Iniciar nova pincelada para métricas
    const strokeStart = {
      startPoint: { x, y },
      startTime: Date.now(),
      path: [{ x, y }]
    }
    
    // Criar primeira stroke
    createStroke(x, y, strokeStart)
  }, [])

  const draw = useCallback((event) => {
    if (!drawingRef.current.isDrawing) return
    
    const rect = canvasRef.current.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top
    
    // 🎨 Adicionar ponto à pincelada atual para análise
    const currentStroke = {
      path: [{ x, y }],
      velocity: calculateVelocity(drawingRef.current.lastPoint, { x, y }),
      direction: calculateDirection(drawingRef.current.lastPoint, { x, y })
    }
    
    createStroke(x, y, currentStroke)
    drawingRef.current.lastPoint = { x, y }
  }, [])

  const stopDrawing = useCallback(() => {
    if (drawingRef.current.isDrawing) {
      // 🎨 Finalizar pincelada e registrar métricas
      const strokeData = {
        endTime: Date.now(),
        path: [], // Path seria coletado durante o desenho
        pressure: 1, // Simplificado para mouse
        velocity: 0,
        direction: 0
      }
      
      recordBrushStroke(strokeData)
    }
    
    drawingRef.current.isDrawing = false
    drawingRef.current.lastPoint = null
  }, [recordBrushStroke])

  const createStroke = useCallback((x, y, metricsData = {}) => {
    const strokeData = {
      x,
      y,
      color: gameState.currentColor,
      size: gameState.brushSize,
      brush: gameState.currentBrush,
      id: Date.now() + Math.random()
    }
    
    setGameState(prev => ({
      ...prev,
      strokes: [...prev.strokes, strokeData]
    }))
  }, [gameState.currentColor, gameState.brushSize, gameState.currentBrush])

  // Funções de undo/redo
  const undoStroke = useCallback(() => {
    setGameState(prev => {
      if (prev.undoStack.length === 0) return prev
      
      const previousState = prev.undoStack[prev.undoStack.length - 1]
      return {
        ...prev,
        strokes: previousState,
        undoStack: prev.undoStack.slice(0, -1),
        redoStack: [...prev.redoStack, prev.strokes],
        showPlaceholder: previousState.length === 0
      }
    })
  }, [])

  const redoStroke = useCallback(() => {
    setGameState(prev => {
      if (prev.redoStack.length === 0) return prev
      
      const nextState = prev.redoStack[prev.redoStack.length - 1]
      return {
        ...prev,
        strokes: nextState,
        redoStack: prev.redoStack.slice(0, -1),
        undoStack: [...prev.undoStack, prev.strokes],
        showPlaceholder: nextState.length === 0
      }
    })
  }, [])

  const clearCanvas = useCallback(() => {
    setGameState(prev => ({
      ...prev,
      strokes: [],
      undoStack: [...prev.undoStack, prev.strokes],
      redoStack: [],
      showPlaceholder: true
    }))
  }, [])

  // Função para coletar métricas criativas
  const collectCreativeMetrics = useCallback(async () => {
    try {
      const currentTime = Date.now()
      const sessionDuration = sessionStartTime ? currentTime - sessionStartTime : 0
      
      // Calcular métricas avançadas de criatividade
      const colorCount = new Set(colorTransitions.map(t => t.color)).size
      const strokeComplexity = brushStrokes.length > 0 
        ? brushStrokes.reduce((acc, stroke) => acc + stroke.points?.length || 0, 0) / brushStrokes.length 
        : 0
      
      const creativeData = {
        sessionDuration,
        totalStrokes: creativityMetrics.totalStrokes || brushStrokes.length,
        colorVariety: colorCount,
        strokeComplexity,
        difficulty: currentDifficulty,
        originalityScore: creativityMetrics.originalityScore,
        complexityScore: creativityMetrics.complexityScore,
        expressiveRange: creativityMetrics.expressiveRange,
        spatialUtilization: creativityMetrics.spatialUtilization,
        brushTypes: gameState.brushTypes?.length || 0,
        canvasUtilization: Math.min(100, (brushStrokes.length / 50) * 100),
        timestamp: currentTime
      }
      
      // Processar com sistema unificado
      await processAdvancedMetrics(creativeData)
      await collectMetrics('creative_painting', creativeData)
      
      console.log('🎨 Métricas criativas processadas:', creativeData)
      return creativeData
      
    } catch (error) {
      console.error('❌ Erro ao coletar métricas criativas:', error)
      throw error
    }
  }, [sessionStartTime, colorTransitions, brushStrokes, creativityMetrics, currentDifficulty, gameState, processAdvancedMetrics, collectMetrics])

  // Funções do footer
  const saveDrawing = useCallback(async () => {
    setGameState(prev => ({ ...prev, savedCount: prev.savedCount + 1 }))
    console.log('🎨 Desenho salvo!')
    
    // 🎨 Coletar métricas ao salvar obra
    try {
      await collectCreativeMetrics()
      console.log('📊 Métricas criativas coletadas ao salvar!')
    } catch (error) {
      console.error('❌ Erro ao coletar métricas:', error)
    }
    
    // Mostrar notificação
    alert('💾 Obra salva com sucesso!')
  }, [collectCreativeMetrics])

  const shareDrawing = useCallback(() => {
    console.log('Compartilhando desenho...')
    alert('📤 Funcionalidade de compartilhamento será implementada na versão final!')
  }, [])

  // Função para finalizar sessão criativa e voltar ao menu
  const handleCreativeSessionEnd = useCallback(async () => {
    try {
      console.log('🎨 Finalizando sessão criativa...')
      
      // Coletar métricas finais se houver desenhos
      if (gameState.strokes && gameState.strokes.length > 0) {
        await collectCreativeMetrics()
        console.log('📊 Métricas finais coletadas')
      }
      
      // Finalizar sessão dos coletores se inicializados
      if (collectorsHub && collectorsHub.initialized) {
        console.log('🔄 Finalizando coletores...')
      }
      
      // Voltar ao menu principal
      if (onBack) {
        onBack()
      }
      
    } catch (error) {
      console.error('❌ Erro ao finalizar sessão criativa:', error)
      // Mesmo com erro, permitir voltar ao menu
      if (onBack) {
        onBack()
      }
    }
  }, [gameState.strokes, collectCreativeMetrics, collectorsHub, onBack])

  const printDrawing = useCallback(() => {
    console.log('Imprimindo desenho...')
    alert('🖨️ Funcionalidade de impressão será implementada na versão final!')
  }, [])

  const newDrawing = useCallback(() => {
    if (gameState.strokes && gameState.strokes.length > 0) {
      if (confirm('🎨 Tem certeza que deseja criar uma nova obra?\n\nO desenho atual será perdido se não foi salvo.')) {
        clearCanvas()
        selectTemplate('blank')
      }
    }
  }, [gameState.strokes ? gameState.strokes.length : 0, clearCanvas, selectTemplate])

  // Suporte touch
  const handleTouchStart = useCallback((event) => {
    event.preventDefault()
    const touch = event.touches[0]
    const mouseEvent = new MouseEvent('mousedown', {
      clientX: touch.clientX,
      clientY: touch.clientY
    })
    startDrawing(mouseEvent)
  }, [startDrawing])

  const handleTouchMove = useCallback((event) => {
    event.preventDefault()
    const touch = event.touches[0]
    const mouseEvent = new MouseEvent('mousemove', {
      clientX: touch.clientX,
      clientY: touch.clientY
    })
    draw(mouseEvent)
  }, [draw])

  const handleTouchEnd = useCallback((event) => {
    event.preventDefault()
    stopDrawing()
  }, [stopDrawing])

  // Tela inicial profissional com seleção de dificuldade
  if (showStartScreen) {
    return (
      <GameStartScreen
        gameTitle="Pintura Criativa"
        gameDescription="Expresse sua criatividade com ferramentas de pintura digital"
        gameIcon="🎨"
        difficulties={[
          {
            id: 'easy',
            name: 'Fácil',
            description: 'Ferramentas básicas\nIdeal para iniciantes',
            icon: '😊'
          },
          {
            id: 'medium',
            name: 'Médio',
            description: 'Mais opções\nDesafio equilibrado',
            icon: '🎯'
          },
          {
            id: 'hard',
            name: 'Avançado',
            description: 'Todas as ferramentas\nPara especialistas',
            icon: '🚀'
          }
        ]}
        onStart={(difficulty) => initializeGame(difficulty)}
        onBack={onBack}
      />
    )
  }

  // INTERFACE PRINCIPAL - PADRÃO LETTERRECOGNITION EXATO
  return (
    <div className={styles.creativePaintingGame}>
      <div className={styles.gameContent}>
        {/* Header do jogo - PADRÃO LETTERRECOGNITION EXATO */}
        <div className={styles.gameHeader}>
          <h1 className={styles.gameTitle}>
            🎨 Pintura Criativa V3
            <div style={{ fontSize: '0.7rem', opacity: 0.8, marginTop: '0.25rem' }}>
              {ACTIVITY_TYPES[gameState.currentActivity.toUpperCase()]?.name || 'Pintura Livre'}
            </div>
          </h1>
          <button
            className={`${styles.headerTtsButton} ${ttsActive ? styles.ttsActive : ''}`}
            onClick={toggleTTS}
            title={ttsActive ? 'Desativar TTS' : 'Ativar TTS'}
            aria-label={ttsActive ? 'Desativar TTS' : 'Ativar TTS'}
          >
            {ttsActive ? '🔊' : '🔇'}
          </button>
        </div>

        {/* Header com estatísticas - PADRÃO LETTERRECOGNITION EXATO */}
        <div className={styles.gameStats}>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.strokes ? gameState.strokes.length : 0}</div>
            <div className={styles.statLabel}>Pinceladas</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.colorsUsed ? gameState.colorsUsed.size : 0}</div>
            <div className={styles.statLabel}>Cores</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{getElapsedTime()}</div>
            <div className={styles.statLabel}>Tempo</div>
          </div>
        </div>

        {/* Menu de atividades - PADRÃO LETTERRECOGNITION EXATO */}
        <div className={styles.activityMenu}>
          {Object.values(ACTIVITY_TYPES).map((activity) => (
            <button
              key={activity.id}
              className={`${styles.activityButton} ${
                gameState.currentActivity === activity.id ? styles.active : ''
              }`}
              onClick={() => switchActivity(activity.id)}
            >
              <span>{activity.icon}</span>
              <span>{activity.name}</span>
            </button>
          ))}
        </div>

        {/* Renderização da atividade atual - PADRÃO LETTERRECOGNITION EXATO */}
        {gameState.currentActivity === ACTIVITY_TYPES.FREE_PAINTING.id && renderFreePainting()}
        {gameState.currentActivity === ACTIVITY_TYPES.ASSISTED_PAINTING.id && renderAssistedPainting()}
        {gameState.currentActivity === ACTIVITY_TYPES.CANVAS_PAINTING.id && renderCanvasPainting()}
        {gameState.currentActivity === ACTIVITY_TYPES.PATTERN_PAINTING.id && renderPatternPainting()}
        {gameState.currentActivity === ACTIVITY_TYPES.COLLABORATIVE_PAINTING.id && renderCollaborativePainting()}

        {/* Controles do jogo - CENTRALIZADOS E SEPARADOS */}
        <div className={styles.gameControls}>
          <div className={styles.controlsGroup}>
            <button className={styles.controlButton} onClick={() => speak('Pintura criativa. Use cores e pincéis para expressar sua criatividade e desenvolver coordenação motora.')}>
              🔊 Explicar
            </button>
            <button className={styles.controlButton} onClick={() => speak('Repita as instruções da atividade atual.')}>
              🔄 Repetir
            </button>
          </div>
          
          <div className={styles.controlsGroup}>
            <button className={styles.controlButton} onClick={backToStart}>
              🔄 Reiniciar
            </button>
            <button className={styles.controlButton} onClick={onBack}>
              ⬅️ Voltar
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default CreativePaintingGame;

// Funções auxiliares para cálculos de métricas durante o desenho
const calculateVelocity = (point1, point2) => {
  if (!point1 || !point2) return 0
  
  const distance = Math.sqrt(
    Math.pow(point2.x - point1.x, 2) + Math.pow(point2.y - point1.y, 2)
  )
  
  // Assumir tempo fixo entre pontos para simplificar
  return distance / 16 // Aproximadamente 16ms entre eventos
}

const calculateDirection = (point1, point2) => {
  if (!point1 || !point2) return 0
  
  return Math.atan2(point2.y - point1.y, point2.x - point1.x) * (180 / Math.PI)
}
