#!/usr/bin/env node

/**
 * @file count-metrics-per-game.js
 * @description Conta quantas métricas são salvas por cada jogo
 * @version 1.0.0
 */

import fs from 'fs';
import path from 'path';

console.log('📊 CONTAGEM DE MÉTRICAS POR JOGO - Portal Betina V3');
console.log('=' .repeat(70));

const games = [
  'LetterRecognition',
  'ContagemNumeros', 
  'MemoryGame',
  'ColorMatch',
  'ImageAssociation',
  'QuebraCabeca',
  'CreativePainting',
  'MusicalSequence',
  'PadroesVisuais'
];

// Função para contar métricas em um arquivo de jogo
function countMetricsInGame(gameName) {
  const gameFile = `src/games/${gameName}/${gameName}Game.jsx`;
  
  if (!fs.existsSync(gameFile)) {
    return { error: 'Arquivo não encontrado', metrics: [] };
  }

  const content = fs.readFileSync(gameFile, 'utf8');
  
  // Procurar por chamadas de métricas
  const metricPatterns = [
    // useGameMetrics calls
    /addMetric\s*\(\s*['"`]([^'"`]+)['"`]/g,
    /recordAction\s*\(/g,
    /recordScore\s*\(/g,
    /recordError\s*\(/g,
    /recordGameStart\s*\(/g,
    /recordGameEnd\s*\(/g,
    
    // useUnifiedGameLogic calls
    /recordInteraction\s*\(/g,
    /startUnifiedSession\s*\(/g,
    /endUnifiedSession\s*\(/g,
    
    // useTherapeuticOrchestrator calls
    /processGameMetrics\s*\(/g,
    /recordTherapeuticEvent\s*\(/g,
    
    // useMultisensoryIntegration calls
    /recordMultisensoryInteraction\s*\(/g,
    /recordSensorData\s*\(/g,
    
    // Collectors calls
    /collectMetrics\s*\(/g,
    /recordData\s*\(/g,
    /logInteraction\s*\(/g,
    
    // Direct metric recording
    /metrics\.push\s*\(/g,
    /setMetrics\s*\(/g,
    /updateMetrics\s*\(/g
  ];

  const foundMetrics = [];
  let totalCalls = 0;

  // Contar cada padrão
  metricPatterns.forEach((pattern, index) => {
    const matches = [...content.matchAll(pattern)];
    if (matches.length > 0) {
      const patternName = [
        'addMetric calls',
        'recordAction calls', 
        'recordScore calls',
        'recordError calls',
        'recordGameStart calls',
        'recordGameEnd calls',
        'recordInteraction calls',
        'startUnifiedSession calls',
        'endUnifiedSession calls',
        'processGameMetrics calls',
        'recordTherapeuticEvent calls',
        'recordMultisensoryInteraction calls',
        'recordSensorData calls',
        'collectMetrics calls',
        'recordData calls',
        'logInteraction calls',
        'metrics.push calls',
        'setMetrics calls',
        'updateMetrics calls'
      ][index];
      
      foundMetrics.push({
        type: patternName,
        count: matches.length,
        examples: matches.slice(0, 3).map(m => m[0])
      });
      totalCalls += matches.length;
    }
  });

  // Procurar por tipos específicos de métricas mencionadas
  const metricTypes = [
    'GAME_START', 'GAME_END', 'USER_ACTION', 'SCORE_UPDATE', 'ERROR',
    'CLICK', 'TOUCH', 'DRAG', 'DROP', 'HOVER', 'FOCUS', 'BLUR',
    'CORRECT_ANSWER', 'WRONG_ANSWER', 'HINT_USED', 'TIME_SPENT',
    'LEVEL_COMPLETE', 'DIFFICULTY_CHANGE', 'PAUSE', 'RESUME',
    'BEHAVIORAL_ANALYSIS', 'COGNITIVE_ANALYSIS', 'AUTISM_ANALYSIS',
    'PERFORMANCE_METRICS', 'ENGAGEMENT_METRICS', 'ATTENTION_METRICS'
  ];

  const mentionedTypes = [];
  metricTypes.forEach(type => {
    if (content.includes(`'${type}'`) || content.includes(`"${type}"`)) {
      mentionedTypes.push(type);
    }
  });

  // Verificar se usa hooks de métricas
  const usesHooks = {
    useGameMetrics: content.includes('useGameMetrics'),
    useUnifiedGameLogic: content.includes('useUnifiedGameLogic'),
    useTherapeuticOrchestrator: content.includes('useTherapeuticOrchestrator'),
    useMultisensoryIntegration: content.includes('useMultisensoryIntegration')
  };

  return {
    totalCalls,
    foundMetrics,
    mentionedTypes,
    usesHooks,
    fileSize: content.length
  };
}

// Analisar cada jogo
console.log('\n🎮 Analisando métricas por jogo...\n');

const results = {};
let totalMetricsAcrossGames = 0;

games.forEach(gameName => {
  console.log(`📋 ${gameName}:`);
  console.log('-'.repeat(50));
  
  const analysis = countMetricsInGame(gameName);
  results[gameName] = analysis;
  
  if (analysis.error) {
    console.log(`❌ ${analysis.error}`);
    return;
  }

  console.log(`📊 Total de chamadas de métricas: ${analysis.totalCalls}`);
  console.log(`🎯 Tipos de métricas mencionados: ${analysis.mentionedTypes.length}`);
  
  // Mostrar hooks usados
  const hooksUsed = Object.entries(analysis.usesHooks)
    .filter(([_, used]) => used)
    .map(([hook, _]) => hook);
  console.log(`🎣 Hooks de métricas: ${hooksUsed.join(', ') || 'Nenhum'}`);
  
  // Mostrar métricas mais comuns
  if (analysis.foundMetrics.length > 0) {
    console.log('📈 Principais tipos de chamadas:');
    analysis.foundMetrics
      .sort((a, b) => b.count - a.count)
      .slice(0, 5)
      .forEach(metric => {
        console.log(`   • ${metric.type}: ${metric.count}x`);
      });
  }

  // Mostrar alguns tipos de métricas
  if (analysis.mentionedTypes.length > 0) {
    console.log('🏷️ Tipos de métricas encontrados:');
    console.log(`   ${analysis.mentionedTypes.slice(0, 8).join(', ')}${analysis.mentionedTypes.length > 8 ? '...' : ''}`);
  }

  totalMetricsAcrossGames += analysis.totalCalls;
  console.log('');
});

// Resumo final
console.log('='.repeat(70));
console.log('📊 RESUMO FINAL');
console.log('='.repeat(70));

console.log(`🎮 Jogos analisados: ${games.length}`);
console.log(`📈 Total de chamadas de métricas: ${totalMetricsAcrossGames}`);
console.log(`📊 Média por jogo: ${(totalMetricsAcrossGames / games.length).toFixed(1)} chamadas`);

// Ranking dos jogos por quantidade de métricas
console.log('\n🏆 RANKING POR QUANTIDADE DE MÉTRICAS:');
const ranking = Object.entries(results)
  .filter(([_, analysis]) => !analysis.error)
  .sort(([_, a], [__, b]) => b.totalCalls - a.totalCalls);

ranking.forEach(([gameName, analysis], index) => {
  const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '📊';
  console.log(`${medal} ${index + 1}. ${gameName}: ${analysis.totalCalls} métricas`);
});

// Análise de hooks
console.log('\n🎣 USO DE HOOKS DE MÉTRICAS:');
const hookUsage = {
  useGameMetrics: 0,
  useUnifiedGameLogic: 0,
  useTherapeuticOrchestrator: 0,
  useMultisensoryIntegration: 0
};

Object.values(results).forEach(analysis => {
  if (!analysis.error) {
    Object.entries(analysis.usesHooks).forEach(([hook, used]) => {
      if (used) hookUsage[hook]++;
    });
  }
});

Object.entries(hookUsage).forEach(([hook, count]) => {
  const percentage = ((count / games.length) * 100).toFixed(1);
  console.log(`📊 ${hook}: ${count}/${games.length} jogos (${percentage}%)`);
});

// Estimativa de métricas por sessão de jogo
console.log('\n🎯 ESTIMATIVA DE MÉTRICAS POR SESSÃO:');
console.log('(Baseado em uma sessão típica de 5 minutos)');

ranking.slice(0, 5).forEach(([gameName, analysis]) => {
  // Estimar métricas por sessão baseado no número de chamadas no código
  const estimatedPerSession = Math.ceil(analysis.totalCalls * 0.3); // 30% das chamadas são executadas por sessão
  console.log(`🎮 ${gameName}: ~${estimatedPerSession} métricas por sessão`);
});

console.log('\n✅ ANÁLISE COMPLETA FINALIZADA!');
console.log('\n💡 NOTA: Estes números representam chamadas de métricas no código.');
console.log('   O número real de métricas salvas depende do login no dashboard de métricas.');
