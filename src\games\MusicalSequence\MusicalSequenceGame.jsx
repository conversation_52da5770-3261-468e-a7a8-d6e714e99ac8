/**
 * 🎵 MUSICAL SEQUENCE V3 - JOGO DE SEQUÊNCIA MUSICAL COM MÚLTIPLAS ATIVIDADES
 * Portal Betina V3 - Jogo educativo com 6 atividades diversificadas
 */

import React, { useState, useEffect, useRef, useCallback, useContext } from 'react';
import { SystemContext } from '../../components/context/SystemContext.jsx';
import { useAccessibilityContext } from '../../components/context/AccessibilityContext';
import { v4 as uuidv4 } from 'uuid';
import styles from './MusicalSequence.module.css';
import { MusicalSequenceConfig } from './MusicalSequenceConfig';
import { MusicalSequenceMetrics } from './MusicalSequenceMetrics';
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';
import { MusicalSequenceCollectorsHub } from './collectors/index.js';
import { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js';
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic.js';
import { useTherapeuticOrchestrator } from '../../hooks/useTherapeuticOrchestrator.js';

// 🎯 SISTEMA DE ATIVIDADES REDESENHADO V3 - MUSICAL SEQUENCE
// Cada atividade testa diferentes funções cognitivas com layouts únicos - NOMES REDUZIDOS
const ACTIVITY_TYPES = {
  SEQUENCE_REPRODUCTION: {
    id: 'sequence_reproduction',
    name: 'Reprodução',
    icon: '🔄',
    description: 'Teste de memória auditiva sequencial',
    cognitiveFunction: 'auditory_sequential_memory',
    component: 'SequenceReproductionActivity'
  },
  RHYTHM_TIMING: {
    id: 'rhythm_timing',
    name: 'Ritmo',
    icon: '⏱️',
    description: 'Teste de coordenação temporal e atenção sustentada',
    cognitiveFunction: 'temporal_coordination_attention',
    component: 'RhythmTimingActivity'
  },
  PITCH_DISCRIMINATION: {
    id: 'pitch_discrimination',
    name: 'Altura',
    icon: '🎵',
    description: 'Teste de processamento auditivo e discriminação fina',
    cognitiveFunction: 'auditory_processing_discrimination',
    component: 'PitchDiscriminationActivity'
  },
  PATTERN_PREDICTION: {
    id: 'pattern_prediction',
    name: 'Padrões',
    icon: '🔮',
    description: 'Teste de raciocínio lógico e reconhecimento de padrões',
    cognitiveFunction: 'logical_reasoning_pattern_recognition',
    component: 'PatternPredictionActivity'
  }
};

const MusicalSequenceGame = ({ onBack, onComplete }) => {
  const { user, ttsEnabled = true } = useContext(SystemContext);
  const { settings } = useAccessibilityContext();

  // Estados TTS
  const [ttsActive, setTtsActive] = useState(true);

  // Referência para métricas
  const metricsRef = useRef(null);

  // 🎵 Inicializar coletores avançados de sequência musical
  const [collectorsHub] = useState(() => new MusicalSequenceCollectorsHub());

  // 🎯 ESTADO EXPANDIDO COM SISTEMA DE ATIVIDADES V3
  const [gameState, setGameState] = useState({
    status: 'start', // 'start', 'playing', 'paused', 'finished'
    score: 0,
    round: 1,
    totalRounds: 7, // Padrão 4-7 rodadas como outros jogos
    difficulty: 'easy',
    accuracy: 100,
    roundStartTime: null,

    // 🎯 Sistema de atividades redesenhado (COMEÇAR COM RITMO POR PADRÃO)
    currentActivity: ACTIVITY_TYPES.RHYTHM_TIMING.id, // Começar diretamente com ritmo
    activityCycle: [
      ACTIVITY_TYPES.RHYTHM_TIMING.id, // Ritmo como primeira atividade
      ACTIVITY_TYPES.SEQUENCE_REPRODUCTION.id,
      ACTIVITY_TYPES.PITCH_DISCRIMINATION.id,
      ACTIVITY_TYPES.PATTERN_PREDICTION.id
    ],
    activityIndex: 0,
    roundsPerActivity: 4, // Mínimo 4 rodadas por atividade
    activityRoundCount: 0,
    activitiesCompleted: [],

    // 🎯 Dados específicos de atividades
    activityData: {
      sequenceReproduction: {
        sequence: [],
        userSequence: [],
        isPlaying: false
      },
      rhythmPatterns: {
        pattern: [],
        userPattern: [],
        tempo: 120
      },
      melodyCompletion: {
        melody: [],
        missingNotes: [],
        userCompletion: []
      },
      instrumentRecognition: {
        currentInstrument: null,
        options: [],
        selectedInstrument: null
      },
      musicalMemory: {
        sequences: [],
        currentSequence: 0,
        userRecall: []
      },
      creativeComposition: {
        userComposition: [],
        availableNotes: [],
        isRecording: false
      }
    },

    // 🎯 Feedback e animações
    showFeedback: false,
    feedbackType: null,
    feedbackMessage: '',
    showCelebration: false,

    // 🎯 Métricas comportamentais
    responseTime: 0,
    hesitationCount: 0,
    helpUsed: false,
    consecutiveCorrect: 0,
    totalAttempts: 0,
    correctAttempts: 0
  });

  // Integração com o sistema unificado Portal Betina V3
  const {
    startUnifiedSession,
    recordInteraction,
    endUnifiedSession,
    updateMetrics,
    portalReady,
    sessionId,
    isSessionActive,
    gameState: unifiedGameState,
    sessionMetrics
  } = useUnifiedGameLogic('musical_sequence');

  // 🔄 Hook multissensorial integrado
  const {
    initializeSession: initMultisensory,
    recordInteraction: recordMultisensoryInteraction,
    finalizeSession: finalizeMultisensory,
    updateData: updateMultisensoryData,
    multisensoryData,
    isInitialized: multisensoryInitialized
  } = useMultisensoryIntegration(sessionId, {
    gameType: 'musical_sequence',
    collectorsHub,
    sensorTypes: {
      visual: true,
      haptic: true,
      tts: ttsEnabled,
      gestural: true,
      biometric: true,
      audio: true
    },
    adaptiveMode: true,
    learningStyle: user?.profile?.learningStyle || 'auditory'
  });

  // 🎯 Hook orquestrador terapêutico
  const {
    processGameMetrics: recordTherapeuticActivity,
    getRecommendations: getTherapeuticRecommendations,
    setUserContext: setTherapeuticContext
  } = useTherapeuticOrchestrator({ userId: user?.id });

  // Estados principais (definidos cedo para evitar erro de inicialização)
  const [showStartScreen, setShowStartScreen] = useState(true);
  const [difficulty, setDifficulty] = useState('easy');
  const [sequence, setSequence] = useState([]);
  const [playerSequence, setPlayerSequence] = useState([]);
  const [currentLevel, setCurrentLevel] = useState(1);
  const [currentRound, setCurrentRound] = useState(1);

  // Definição dos instrumentos (movida para cima para evitar erro de inicialização)
  const instruments = [
    { id: 'piano', name: 'Piano', emoji: '🎹', color: '#FF6B6B', sound: 'piano' },
    { id: 'guitar', name: 'Violão', emoji: '🎸', color: '#4ECDC4', sound: 'guitar' },
    { id: 'drum', name: 'Bateria', emoji: '🥁', color: '#45B7D1', sound: 'drum' },
    { id: 'flute', name: 'Flauta', emoji: '🎵', color: '#96CEB4', sound: 'flute' },
    { id: 'violin', name: 'Violino', emoji: '🎻', color: '#A8E6CF', sound: 'violin' }
  ];

  // Estados adicionais (movidos para cima para evitar erro de inicialização)
  const [score, setScore] = useState(0);
  const [streak, setStreak] = useState(0);
  const [totalAttempts, setTotalAttempts] = useState(0);
  const [correctAttempts, setCorrectAttempts] = useState(0);
  const [activeInstrument, setActiveInstrument] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [feedback, setFeedback] = useState({ show: false, type: '', message: '' });
  const [gameStartTime, setGameStartTime] = useState(null);

  // Conectar métricas ao backend após inicialização
  useEffect(() => {
    if (metricsRef.current && recordInteraction && updateMetrics) {
      metricsRef.current.connectToBackend({
        recordInteraction,
        updateMetrics
      });
    }
  }, [recordInteraction, updateMetrics]);

  // Função TTS padronizada
  const speak = useCallback((text, options = {}) => {
    if (!ttsActive || !('speechSynthesis' in window)) {
      return;
    }

    window.speechSynthesis.cancel();

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'pt-BR';
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;

    window.speechSynthesis.speak(utterance);
  }, [ttsActive]);

  // Toggle TTS
  const toggleTTS = useCallback(() => {
    const newTtsState = !ttsActive;
    setTtsActive(newTtsState);

    // Cancelar qualquer fala em andamento se desabilitando
    if (!newTtsState && 'speechSynthesis' in window) {
      window.speechSynthesis.cancel();
    } else if (newTtsState) {
      speak('TTS ativado');
    }
  }, [ttsActive, speak]);

  // � FUNÇÕES DE ÁUDIO (movidas para cima para evitar erro de inicialização)
  
  // Função para inicializar AudioContext
  const initAudioContext = useCallback(() => {
    if (!audioContextRef.current || audioContextRef.current.state === 'closed') {
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
    }
  }, []);

  // Função para tocar um som específico com suporte melhorado para bateria
  const playSound = useCallback(async (note, duration = 500) => {
    if (!audioContextRef.current) {
      console.warn('AudioContext não iniciado. Iniciando automaticamente...');
      await initAudioContext();
    }

    try {
      const audioContext = audioContextRef.current;
      
      // Som especial para bateria/percussão usando ruído branco
      if (note === 'DRUM' || note === 'BEAT' || note === 'TAP') {
        // Criar ruído branco para efeito de bateria
        const bufferSize = audioContext.sampleRate * (duration / 1000);
        const buffer = audioContext.createBuffer(1, bufferSize, audioContext.sampleRate);
        const data = buffer.getChannelData(0);
        
        // Gerar ruído branco com envelope para simular bateria
        for (let i = 0; i < bufferSize; i++) {
          const envelope = Math.pow(1 - (i / bufferSize), 2); // Envelope decrescente
          data[i] = (Math.random() * 2 - 1) * envelope * 0.3;
        }
        
        const source = audioContext.createBufferSource();
        const gainNode = audioContext.createGain();
        
        source.buffer = buffer;
        source.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        gainNode.gain.setValueAtTime(0.4, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration / 1000);
        
        source.start(audioContext.currentTime);
        
        return new Promise(resolve => {
          source.onended = resolve;
          setTimeout(resolve, duration);
        });
      }
      
      // Sons normais para notas musicais
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      const frequencies = {
        'C': 261.63, 'D': 293.66, 'E': 329.63, 'F': 349.23,
        'G': 392.00, 'A': 440.00, 'B': 493.88
      };
      
      oscillator.frequency.setValueAtTime(frequencies[note] || 440, audioContext.currentTime);
      oscillator.type = 'sine';
      
      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000);
      
      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + duration / 1000);
      
      return new Promise(resolve => {
        oscillator.onended = resolve;
      });
    } catch (error) {
      console.error('Erro ao tocar som:', error);
    }
  }, [initAudioContext]);

  // �🎯 FUNÇÕES DE CONTROLE DO JOGO PADRONIZADAS

  // Inicializar métricas
  useEffect(() => {
    if (!metricsRef.current) {
      metricsRef.current = MusicalSequenceMetrics;
    }
  }, []);

  // Função para iniciar o jogo
  const startGame = useCallback(async (selectedDifficulty) => {
    try {
      // Esconder tela inicial primeiro
      setShowStartScreen(false);
      
      // Definir dificuldade e ajustar rodadas
      setDifficulty(selectedDifficulty);
      const maxRounds = getRoundsForDifficulty(selectedDifficulty);
      
      setGameState(prev => ({
        ...prev,
        status: 'playing',
        difficulty: selectedDifficulty,
        totalRounds: maxRounds,
        roundStartTime: Date.now()
      }));

      // Inicializar sessão unificada
      if (startUnifiedSession) {
        await startUnifiedSession({
          gameType: 'musical_sequence',
          difficulty: selectedDifficulty,
          userId: user?.id || 'anonymous'
        });
      }

      // Inicializar sessões dos hooks
      await initMultisensory();

      // Configurar contexto terapêutico se usuário válido
      if (user?.id && user.id !== 'anonymous' && user.id !== '') {
        setTherapeuticContext(user.id);
      }

      // Gerar primeira atividade
      generateNewRound();

      speak('Jogo iniciado! Vamos começar com sequências musicais.');
    } catch (error) {
      console.error('Erro ao iniciar jogo:', error);
    }
  }, [startUnifiedSession, initMultisensory, setTherapeuticContext, user, speak, setShowStartScreen]);

  // Função para trocar atividade - LIVRE PARA TROCAR A QUALQUER MOMENTO
  const changeActivity = useCallback((activityId) => {
    console.log('🎯 Changing musical activity to:', activityId);

    setGameState(prev => {
      console.log('🔄 Current musical state before change:', {
        currentActivity: prev.currentActivity,
        targetActivity: activityId
      });

      // Resetar estado para nova atividade
      const newState = {
        ...prev,
        currentActivity: activityId,
        activityRoundCount: 0,
        activityIndex: prev.activityCycle.indexOf(activityId),
        showFeedback: false,
        roundStartTime: Date.now()
      };

      console.log('✅ New musical state after change:', newState);
      return newState;
    });

    // Resetar sequências
    setSequence([]);
    setPlayerSequence([]);
    setActiveInstrument(null);
    setIsPlaying(false);

    const activity = Object.values(ACTIVITY_TYPES).find(a => a.id === activityId);
    speak(`Mudando para: ${activity?.name}`);

    // Gerar nova sequência para a nova atividade após um pequeno delay
    setTimeout(() => {
      generateNewRound(activityId);
    }, 500);
  }, [speak]);

  // Função para gerar nova rodada
  const generateNewRound = useCallback((activityId = null) => {
    const currentActivity = activityId || gameState.currentActivity;
    console.log('🎵 Generating new round for activity:', currentActivity);

    // FORÇAR que permaneça na atividade de ritmo uma vez que começou
    const finalActivity = currentActivity === ACTIVITY_TYPES.RHYTHM_TIMING.id ? 
                         ACTIVITY_TYPES.RHYTHM_TIMING.id : currentActivity;

    console.log('🎯 Atividade final selecionada:', finalActivity);

    setGameState(prev => ({
      ...prev,
      currentActivity: finalActivity, // Garantir que o estado mantenha a atividade
      roundStartTime: Date.now(),
      showFeedback: false,
      feedbackType: null,
      feedbackMessage: ''
    }));

    // Gerar dados específicos da atividade redesenhada
    switch (finalActivity) {
      case ACTIVITY_TYPES.SEQUENCE_REPRODUCTION.id:
        generateSequenceReproduction();
        break;
      case ACTIVITY_TYPES.RHYTHM_TIMING.id:
        generateRhythmTiming();
        break;
      case ACTIVITY_TYPES.PITCH_DISCRIMINATION.id:
        generatePitchDiscrimination();
        break;
      case ACTIVITY_TYPES.PATTERN_PREDICTION.id:
        generatePatternPrediction();
        break;
      default:
        generateRhythmTiming(); // Padrão para ritmo
    }
  }, [gameState.currentActivity]);

  // =====================================================
  // 🎯 FUNÇÕES DE GERAÇÃO REDESENHADAS - ATIVIDADES DISTINTAS
  // =====================================================

  // 🔄 REPRODUÇÃO DE SEQUÊNCIA - Memória auditiva sequencial
  const generateSequenceReproduction = useCallback(() => {
    console.log('🔄 Generating sequence reproduction activity');

    const difficultyMap = { 'easy': 3, 'medium': 4, 'hard': 5 };
    const sequenceLength = difficultyMap[difficulty] || 3;
    const newSequence = [];

    // Gerar sequência de instrumentos únicos (sem repetição)
    const availableInstruments = [...instruments];
    for (let i = 0; i < sequenceLength; i++) {
      const randomIndex = Math.floor(Math.random() * availableInstruments.length);
      const selectedInstrument = availableInstruments.splice(randomIndex, 1)[0];
      newSequence.push(selectedInstrument.id);
    }

    setSequence(newSequence);
    setPlayerSequence([]);

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        sequenceReproduction: {
          sequence: newSequence,
          userSequence: [],
          currentStep: 0,
          showingSequence: true,
          allowInput: false
        }
      }
    }));

    speak(`Atividade: Reprodução de Sequência. Memorize a ordem de ${sequenceLength} instrumentos diferentes.`);
  }, [difficulty, speak]);

  // ⏱️ JOGO DE RITMO COMPLETAMENTE NOVO - Sistema de metronomo interativo
  const generateRhythmTiming = useCallback(() => {
    console.log('⏱️ Generating rhythm timing activity - NOVO SISTEMA COM BATERIA');

    // Sistema completamente diferente: seguir um metrônomo
    const bpmLevels = {
      'easy': 80,    // 80 BPM - ritmo lento e confortável
      'medium': 120, // 120 BPM - ritmo médio padrão
      'hard': 160    // 160 BPM - ritmo rápido e desafiador
    };

    const targetBPM = bpmLevels[difficulty] || 80;
    const totalBeats = 8; // Sempre 8 batidas para seguir
    const beatInterval = (60 / targetBPM) * 1000; // Calcular intervalo correto em ms
    
    console.log(`🥁 Configurando ritmo: ${targetBPM} BPM, Intervalo: ${beatInterval}ms`);
    
    setSequence([]); // Não usa sequência de instrumentos
    setPlayerSequence([]);

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        rhythmTiming: {
          targetBPM: targetBPM,
          totalBeats: totalBeats,
          currentBeat: 0,
          userTaps: [],
          metronomeActive: false,
          gamePhase: 'waiting', // 'waiting', 'listening', 'playing', 'finished'
          accuracy: 0,
          perfectHits: 0,
          startTime: null,
          beatInterval: beatInterval, // Intervalo em ms entre batidas
          isRecording: false
        }
      }
    }));

    speak(`Novo Jogo de Ritmo com Bateria! Siga o metrônomo tocando ${totalBeats} batidas no ritmo de ${targetBPM} BPM. Primeiro ouça, depois toque junto!`);
  }, [difficulty, speak]);

  // 🎵 DISCRIMINAÇÃO DE ALTURA - Processamento auditivo fino MELHORADO
  const generatePitchDiscrimination = useCallback(() => {
    console.log('🎵 Generating enhanced pitch discrimination activity');

    // Sistema melhorado de frequências com mais variedade
    const pitchSystems = {
      'easy': {
        reference: 440, // Lá padrão
        options: [
          { freq: 440, name: 'Lá (440Hz)', note: 'A4' },
          { freq: 493.88, name: 'Si (494Hz)', note: 'B4' },
          { freq: 523.25, name: 'Dó (523Hz)', note: 'C5' },
          { freq: 587.33, name: 'Ré (587Hz)', note: 'D5' }
        ],
        tolerance: 50 // Diferenças grandes
      },
      'medium': {
        reference: 440,
        options: [
          { freq: 440, name: 'Lá (440Hz)', note: 'A4' },
          { freq: 466.16, name: 'Lá# (466Hz)', note: 'A#4' },
          { freq: 493.88, name: 'Si (494Hz)', note: 'B4' },
          { freq: 523.25, name: 'Dó (523Hz)', note: 'C5' }
        ],
        tolerance: 25 // Diferenças médias
      },
      'hard': {
        reference: 440,
        options: [
          { freq: 440, name: 'Lá (440Hz)', note: 'A4' },
          { freq: 445, name: 'Lá+ (445Hz)', note: 'A4+' },
          { freq: 450, name: 'Lá++ (450Hz)', note: 'A4++' },
          { freq: 435, name: 'Lá- (435Hz)', note: 'A4-' }
        ],
        tolerance: 10 // Diferenças pequenas (microtons)
      }
    };

    const currentSystem = pitchSystems[difficulty] || pitchSystems['easy'];
    const referencePitch = currentSystem.reference;

    // Escolher tom de teste aleatório (diferente da referência)
    const testOptions = currentSystem.options.filter(opt => opt.freq !== referencePitch);
    const targetPitchObj = testOptions[Math.floor(Math.random() * testOptions.length)];
    const targetPitch = targetPitchObj.freq;

    // Determinar relação entre tons
    const pitchRelation = targetPitch > referencePitch ? 'higher' :
                         targetPitch < referencePitch ? 'lower' : 'same';

    setSequence([]); // Não usa sequência de instrumentos
    setPlayerSequence([]);

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        pitchDiscrimination: {
          referencePitch: referencePitch,
          targetPitch: targetPitch,
          pitchSystem: currentSystem,
          targetPitchObj: targetPitchObj,
          correctAnswer: pitchRelation,
          currentPhase: 'instruction', // 'instruction', 'reference', 'test', 'comparison', 'answer'
          userAnswer: null,
          showVisualAid: true,
          hasPlayedReference: false,
          hasPlayedTest: false,
          canAnswer: false,
          round: (prev.activityData?.pitchDiscrimination?.round || 0) + 1,
          difficulty: difficulty,
          frequencyDifference: Math.abs(targetPitch - referencePitch)
        }
      }
    }));

    const difficultyText = difficulty === 'easy' ? 'grandes diferenças' :
                          difficulty === 'medium' ? 'diferenças médias' : 'diferenças sutis';

    speak(`Atividade: Discriminação de Altura Musical. Nível ${difficulty} com ${difficultyText}. Ouça os dois tons e compare suas alturas.`);
  }, [difficulty, speak]);

  // 🔮 PREDIÇÃO DE PADRÕES - Raciocínio lógico e reconhecimento de padrões
  const generatePatternPrediction = useCallback(() => {
    console.log('🔮 Generating pattern prediction activity');

    // Gerar padrões lógicos musicais mais complexos baseados na dificuldade
    const patternTypes = {
      'easy': {
        'ascending': [0, 1, 2, 3], // Sequência crescente
        'descending': [3, 2, 1, 0], // Sequência decrescente
        'repeating': [0, 1, 0, 1] // Padrão repetitivo
      },
      'medium': {
        'alternating': [0, 2, 1, 3], // Padrão alternado
        'skip_one': [0, 2, 4, 1], // Pular um
        'double_pattern': [0, 0, 1, 1] // Padrão duplo
      },
      'hard': {
        'fibonacci': [0, 1, 1, 2], // Sequência fibonacci
        'arithmetic': [0, 2, 4, 1], // Progressão aritmética
        'complex_alternating': [0, 3, 1, 4, 2] // Alternância complexa
      }
    };

    const difficultyPatterns = patternTypes[difficulty] || patternTypes['easy'];
    const patternNames = Object.keys(difficultyPatterns);
    const selectedPattern = patternNames[Math.floor(Math.random() * patternNames.length)];
    const basePattern = difficultyPatterns[selectedPattern];

    // Mapear números para instrumentos
    const patternInstruments = basePattern.map(index => instruments[index % instruments.length].id);

    // Mostrar padrão incompleto (remover último elemento)
    const incompletePattern = patternInstruments.slice(0, -1);
    const correctAnswer = patternInstruments[patternInstruments.length - 1];

    // Gerar opções incluindo a resposta correta e distrações
    const allOptions = [...instruments.map(inst => inst.id)];
    const distractors = allOptions.filter(id => id !== correctAnswer);
    const shuffledDistractors = distractors.sort(() => Math.random() - 0.5);
    const options = [correctAnswer, ...shuffledDistractors.slice(0, 3)].sort(() => Math.random() - 0.5);

    setSequence(incompletePattern);
    setPlayerSequence([]);

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        patternPrediction: {
          incompletePattern: incompletePattern,
          correctAnswer: correctAnswer,
          patternType: selectedPattern,
          options: options,
          userAnswer: null,
          showPattern: true,
          difficulty: difficulty,
          patternExplanation: getPatternExplanation(selectedPattern)
        }
      }
    }));

    speak(`Atividade: Predição de Padrões. Analise o padrão musical ${selectedPattern} e preveja o próximo elemento.`);
  }, [difficulty, speak]);

  // Função para explicar o padrão
  const getPatternExplanation = useCallback((patternType) => {
    const explanations = {
      'ascending': 'Sequência crescente: cada elemento é maior que o anterior',
      'descending': 'Sequência decrescente: cada elemento é menor que o anterior',
      'repeating': 'Padrão repetitivo: elementos se repetem em ciclos',
      'alternating': 'Padrão alternado: elementos alternam entre posições',
      'skip_one': 'Pular um: sequência pula um elemento a cada passo',
      'double_pattern': 'Padrão duplo: cada elemento aparece duas vezes',
      'fibonacci': 'Sequência Fibonacci: cada elemento é a soma dos dois anteriores',
      'arithmetic': 'Progressão aritmética: diferença constante entre elementos',
      'complex_alternating': 'Alternância complexa: padrão de alternância avançado'
    };

    return explanations[patternType] || 'Analise a sequência para identificar o padrão';
  }, []);

  // Função para verificar resposta do padrão
  const checkPatternAnswer = useCallback((selectedAnswer) => {
    const activityData = gameState.activityData?.patternPrediction || {};
    const { correctAnswer, patternType } = activityData;

    const isCorrect = selectedAnswer === correctAnswer;
    const selectedInstrument = instruments.find(inst => inst.id === selectedAnswer);
    const correctInstrument = instruments.find(inst => inst.id === correctAnswer);

    if (isCorrect) {
      speak(`Perfeito! ${selectedInstrument?.name} completa o padrão ${patternType} corretamente! ${getPatternExplanation(patternType)}`);

      // Mostrar sequência completa
      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          patternPrediction: {
            ...prev.activityData.patternPrediction,
            showComplete: true,
            isCorrect: true
          }
        }
      }));

      // Avançar para próxima rodada após delay
      setTimeout(() => {
        generateNewRound();
      }, 3000);

    } else {
      speak(`Não é bem assim. O padrão ${patternType} seria completado por ${correctInstrument?.name}. ${getPatternExplanation(patternType)}`);

      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          patternPrediction: {
            ...prev.activityData.patternPrediction,
            showComplete: true,
            isCorrect: false,
            showExplanation: true
          }
        }
      }));

      // Permitir nova tentativa após delay
      setTimeout(() => {
        setGameState(prev => ({
          ...prev,
          activityData: {
            ...prev.activityData,
            patternPrediction: {
              ...prev.activityData.patternPrediction,
              userAnswer: null,
              showComplete: false,
              showExplanation: false
            }
          }
        }));
      }, 4000);
    }
  }, [gameState.activityData?.patternPrediction, instruments, speak, getPatternExplanation, generateNewRound]);

  // 🎨 EXPRESSÃO CRIATIVA - Flexibilidade cognitiva e criatividade
  const generateCreativeExpression = useCallback(() => {
    console.log('🎨 Generating creative expression activity');

    // Desafios criativos com restrições específicas e mais variados
    const creativePrompts = {
      'easy': [
        {
          constraint: 'use_3_instruments',
          description: 'Crie uma melodia usando exatamente 3 instrumentos diferentes',
          minLength: 4,
          maxLength: 6,
          theme: 'variety'
        },
        {
          constraint: 'simple_pattern',
          description: 'Crie uma melodia simples que conte uma história musical',
          minLength: 4,
          maxLength: 6,
          theme: 'storytelling'
        }
      ],
      'medium': [
        {
          constraint: 'emotional_theme',
          description: 'Crie uma melodia que expresse alegria usando 4-5 instrumentos',
          minLength: 5,
          maxLength: 8,
          theme: 'joy'
        },
        {
          constraint: 'call_response',
          description: 'Crie uma melodia com padrão pergunta-resposta musical',
          minLength: 6,
          maxLength: 8,
          theme: 'dialogue'
        }
      ],
      'hard': [
        {
          constraint: 'rhythmic_pattern',
          description: 'Crie uma composição com padrão rítmico específico: forte-fraco-forte-fraco',
          minLength: 6,
          maxLength: 10,
          theme: 'rhythm'
        },
        {
          constraint: 'modal_composition',
          description: 'Crie uma composição usando todos os instrumentos pelo menos uma vez',
          minLength: 8,
          maxLength: 12,
          theme: 'completeness'
        }
      ]
    };

    const difficultyPrompts = creativePrompts[difficulty] || creativePrompts['easy'];
    const prompt = difficultyPrompts[Math.floor(Math.random() * difficultyPrompts.length)];

    setSequence([]); // Começar vazio para criação livre
    setPlayerSequence([]);

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        creativeExpression: {
          prompt: prompt,
          userComposition: [],
          isRecording: false,
          canPlayback: false,
          creativityScore: 0,
          uniquenessScore: 0,
          difficulty: difficulty,
          startTime: Date.now(),
          compositionHistory: []
        }
      }
    }));

    speak(`Atividade: Expressão Criativa. ${prompt.description}`);
  }, [difficulty, speak]);

  // Função para avaliar criatividade da composição
  const evaluateCreativity = useCallback((composition) => {
    if (!composition || composition.length === 0) return { creativity: 0, uniqueness: 0, feedback: 'Composição vazia' };

    // Calcular variedade de instrumentos
    const uniqueInstruments = [...new Set(composition)];
    const varietyScore = (uniqueInstruments.length / instruments.length) * 100;

    // Calcular padrões interessantes
    let patternScore = 0;

    // Verificar se há repetições interessantes
    const hasRepeats = composition.some((inst, i) =>
      composition.indexOf(inst, i + 1) !== -1
    );
    if (hasRepeats) patternScore += 20;

    // Verificar se há progressão
    const hasProgression = composition.length >= 3;
    if (hasProgression) patternScore += 20;

    // Verificar se usa instrumentos de forma equilibrada
    const instrumentCounts = {};
    composition.forEach(inst => {
      instrumentCounts[inst] = (instrumentCounts[inst] || 0) + 1;
    });

    const isBalanced = Object.values(instrumentCounts).every(count => count <= 3);
    if (isBalanced) patternScore += 20;

    // Calcular pontuação final
    const creativityScore = Math.min(100, Math.round((varietyScore + patternScore) / 2));
    const uniquenessScore = Math.min(100, Math.round(varietyScore + (composition.length >= 6 ? 20 : 0)));

    // Gerar feedback
    let feedback = '';
    if (creativityScore >= 80) {
      feedback = 'Composição muito criativa! Excelente uso dos instrumentos.';
    } else if (creativityScore >= 60) {
      feedback = 'Boa composição! Tente variar mais os instrumentos.';
    } else {
      feedback = 'Continue explorando! Tente usar mais instrumentos diferentes.';
    }

    return { creativity: creativityScore, uniqueness: uniquenessScore, feedback };
  }, [instruments]);

  // Função para tocar sequência com array
  const playSequenceWithArray = useCallback(async (sequenceArray) => {
    if (isPlaying) return;
    
    setIsPlaying(true);
    console.log('🎵 Reproduzindo sequência:', sequenceArray);
    
    for (let i = 0; i < sequenceArray.length; i++) {
      const note = sequenceArray[i];
      setActiveInstrument(note);
      
      await playSound(note, 600);
      await new Promise(resolve => setTimeout(resolve, 100));
      
      setActiveInstrument(null);
      await new Promise(resolve => setTimeout(resolve, 200));
    }
    
    setIsPlaying(false);
    
    // TTS automático após reproduzir a sequência
    if (ttsEnabled) {
      setTimeout(() => {
        speak(`Sequência reproduzida! Agora é sua vez de repetir ${sequenceArray.length} notas.`, {
          rate: 0.9,
          onEnd: () => console.log('Instrução pós-sequência anunciada')
        });
      }, 500);
    }
  }, [isPlaying, setActiveInstrument, playSound, setIsPlaying, setGameState, ttsEnabled, speak]);

  // Função para finalizar composição criativa
  const finishCreativeComposition = useCallback(() => {
    const activityData = gameState.activityData?.creativeExpression || {};
    const { userComposition, prompt } = activityData;

    if (!userComposition || userComposition.length < (prompt?.minLength || 3)) {
      speak(`Sua composição precisa ter pelo menos ${prompt?.minLength || 3} notas. Continue criando!`);
      return;
    }

    // Avaliar criatividade
    const evaluation = evaluateCreativity(userComposition);

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        creativeExpression: {
          ...prev.activityData.creativeExpression,
          creativityScore: evaluation.creativity,
          uniquenessScore: evaluation.uniqueness,
          isFinished: true,
          evaluation: evaluation
        }
      }
    }));

    // Dar feedback detalhado
    speak(`Composição finalizada! ${evaluation.feedback} Criatividade: ${evaluation.creativity}%. Originalidade: ${evaluation.uniqueness}%.`);

    // Tocar a composição final
    setTimeout(() => {
      playSequenceWithArray(userComposition);
    }, 2000);

    // Avançar para próxima rodada após delay
    setTimeout(() => {
      generateNewRound();
    }, 5000);

  }, [gameState.activityData?.creativeExpression, evaluateCreativity, speak, playSequenceWithArray, generateNewRound]);

  // Função para limpar composição
  const clearCreativeComposition = useCallback(() => {
    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        creativeExpression: {
          ...prev.activityData.creativeExpression,
          userComposition: [],
          canPlayback: false
        }
      }
    }));

    speak('Composição limpa. Comece uma nova criação!');
  }, [speak]);

  // Função para alternar TTS (compatibilidade)
  const toggleTTSOld = useCallback(() => {
    setTtsActive(prev => {
      const newState = !prev;
      localStorage.setItem('musicalSequence_ttsActive', JSON.stringify(newState));
      
      // Cancelar qualquer fala em andamento se desabilitando
      if (!newState && 'speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
      
      return newState;
    });
  }, []);
  
  // 🎯 SISTEMA DE RODADAS BASEADO NA CONFIGURAÇÃO (4-7 rodadas)
  const getRoundsForDifficulty = useCallback((diff) => {
    return MusicalSequenceConfig.roundsConfig[diff] || MusicalSequenceConfig.roundsConfig.easy;
  }, []);
  
  const [maxRoundsPerLevel] = useState(() => getRoundsForDifficulty(difficulty));
  const audioRef = useRef({});
  const audioContextRef = useRef(null);

  // Sistema de coleta de dados próprio do MusicalSequence
  const collectorsHubRef = useRef(null);
  const [metricsSession, setMetricsSession] = useState({
    sessionId: null,
    isActive: false,
    insights: {}
  });

  // Função para enviar métricas usando o sistema próprio
  const sendMetrics = useCallback(async (metricsData) => {
    try {
      // Usar MusicalSequenceMetrics que já gerencia o hub de coletores
      MusicalSequenceMetrics.recordAdvancedInteraction({
        ...metricsData,
        sessionId: metricsSession.sessionId,
        timestamp: Date.now()
      });
      return true;
    } catch (error) {
      console.error('Erro ao enviar métricas:', error);
      return false;
    }
  }, [metricsSession.sessionId]);
  
  const getInsights = useCallback(async () => {
    try {
      const hub = MusicalSequenceMetrics.getCollectorsHub();
      if (hub) {
        return hub.getConsolidatedInsights?.() || {};
      }
      return {};
    } catch (error) {
      console.error('Erro ao obter insights:', error);
      return {};
    }
  }, []);

  // Propriedade de conveniência para manter compatibilidade
  const orchestratorReady = metricsSession.isActive;

  const explainGame = useCallback(() => {
    speak('Jogo de Sequência Musical. Escute a sequência de instrumentos e reproduza-a na ordem correta. Desenvolva sua memória auditiva e percepção musical!', {
      rate: 0.8
    });
  }, [speak]);

  const getAccuracy = useCallback(() => {
    if (totalAttempts === 0) return 100;
    return Math.round((correctAttempts / totalAttempts) * 100);
  }, [totalAttempts, correctAttempts]);

  // ✅ DECLARAR generateNewSequence ANTES DOS useEffects QUE A UTILIZAM
  const generateNewSequence = useCallback(async () => {
    console.log('generateNewSequence called with difficulty:', difficulty);

    // Usar dificuldade simples se não encontrar configuração
    const difficultyMap = {
      'easy': 3,
      'medium': 4,
      'hard': 5
    };

    const sequenceLength = difficultyMap[difficulty] || 3;
    console.log('sequenceLength:', sequenceLength);

    const newSequence = [];
    for (let i = 0; i < sequenceLength; i++) {
      const randomInstrument = instruments[Math.floor(Math.random() * instruments.length)];
      newSequence.push(randomInstrument.id);
    }

    console.log('Generated sequence:', newSequence);
    setSequence(newSequence);
    setPlayerSequence([]);
    setFeedback({ show: false, type: '', message: '' });

    // Coleta avançada de dados da nova sequência
    try {
      const sequenceGenerationData = {
        timestamp: Date.now(),
        sequenceLength: newSequence.length,
        difficulty,
        level: currentLevel,
        round: currentRound,
        sequence: newSequence,
        sessionDuration: Date.now() - gameStartTime,
        gameState: 'sequence_generated'
      };

      await collectorsHub.processInteraction(sequenceGenerationData);

      // Registrar com backend
      if (recordInteraction) {
        recordInteraction({
          type: 'sequence_generation',
          data: sequenceGenerationData
        });
      }
    } catch (error) {
      console.warn('⚠️ Erro ao coletar dados da sequência:', error);
    }
  }, [difficulty, currentLevel, currentRound, gameStartTime, collectorsHub, recordInteraction]);

  useEffect(() => {
    // Inicializar AudioContext apenas quando necessário
    
    instruments.forEach(instrument => {
      audioRef.current[instrument.id] = {
        play: () => {
          try {
            // Inicializar AudioContext se necessário
            initAudioContext();
            
            // Verificar se o contexto de áudio ainda está ativo
            if (audioContextRef.current.state === 'closed') {
              console.warn('AudioContext fechado, não é possível reproduzir som');
              return;
            }
            
            // Resumir contexto se estiver suspenso
            if (audioContextRef.current.state === 'suspended') {
              audioContextRef.current.resume();
            }
            
            const oscillator = audioContextRef.current.createOscillator();
            const gainNode = audioContextRef.current.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContextRef.current.destination);
            
            const frequencies = {
              piano: 523.25, // C5
              guitar: 329.63, // E4
              drum: 146.83,  // D3
              flute: 440.00, // A4
              violin: 659.25, // E5
              sax: 293.66    // D4
            };
            
            oscillator.frequency.setValueAtTime(frequencies[instrument.id] || 440, audioContextRef.current.currentTime);
            oscillator.type = instrument.id === 'drum' ? 'square' : 'sine';
            
            gainNode.gain.setValueAtTime(0.3, audioContextRef.current.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.001, audioContextRef.current.currentTime + 0.5);
            
            oscillator.start(audioContextRef.current.currentTime);
            oscillator.stop(audioContextRef.current.currentTime + 0.5);
          } catch (error) {
            console.error('Error playing sound:', error);
          }
        }
      };
    });

    return () => {
      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        audioContextRef.current.close();
      }
      // Cancelar qualquer TTS ativo quando sair do jogo
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, [initAudioContext]);  // Auto-start sequence when game begins
  // Auto-start sequence when game begins - SIMPLIFICADO E CONTROLADO
  useEffect(() => {
    if (!showStartScreen && gameState.status === 'playing' && difficulty && sequence.length === 0) {
      console.log('✅ Auto-starting sequence after game start...');

      // Gerar sequência simples diretamente
      const difficultyMap = { 'easy': 3, 'medium': 4, 'hard': 5 };
      const sequenceLength = difficultyMap[difficulty] || 3;
      const newSequence = [];

      for (let i = 0; i < sequenceLength; i++) {
        const randomInstrument = instruments[Math.floor(Math.random() * instruments.length)];
        newSequence.push(randomInstrument.id);
      }

      setSequence(newSequence);
      setPlayerSequence([]);
    }
  }, [showStartScreen, difficulty]); // Dependências mínimas

  // Auto-play sequence when it's generated - CONTROLADO
  useEffect(() => {
    if (sequence.length > 0 && !isPlaying && !showStartScreen && 
        gameState.currentActivity === ACTIVITY_TYPES.SEQUENCE_REPRODUCTION.id) {
      console.log('✅ Auto-playing sequence after generation...');
      const timer = setTimeout(() => {
        playSequence();
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [sequence, gameState.currentActivity]); // Adicionar currentActivity como dependência

  // Função para tocar som de instrumento
  const playSoundInstrument = useCallback(async (instrumentId) => {
    try {
      if (audioRef.current[instrumentId]) {
        audioRef.current[instrumentId].play();
      }
    } catch (error) {
      console.error('Error playing sound:', error);
    }
  }, []);

  // Função para tocar a sequência gerada
  const playSequence = useCallback(async () => {
    console.log('playSequence called with sequence:', sequence);
    setIsPlaying(true);
    
    // Anunciar início da sequência
    speak(`Agora ouça atentamente a sequência de ${sequence.length} instrumentos.`, {
      rate: 1.0
    });
    
    // Aguardar o TTS terminar antes de tocar a sequência
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    for (let i = 0; i < sequence.length; i++) {
      console.log(`Playing instrument ${i}:`, sequence[i]);
      setActiveInstrument(sequence[i]);
      await playSoundInstrument(sequence[i]);
      await new Promise(resolve => setTimeout(resolve, 600));
      setActiveInstrument(null);
      await new Promise(resolve => setTimeout(resolve, 400));
    }
    
    console.log('Sequence finished, ready for user input');
    setIsPlaying(false);
    
    // Anunciar fim da sequência
    speak('Agora é sua vez! Toque os instrumentos na ordem correta.', {
      rate: 1.0
    });
  }, [sequence, speak, playSound, setIsPlaying, setGameState, setActiveInstrument]);

  const handleInstrumentClick = useCallback(async (instrumentId) => {
    console.log('handleInstrumentClick called:', { instrumentId, gameState: gameState.status, isPlaying, playerSequenceLength: playerSequence.length });

    // Permitir cliques apenas quando não está tocando a sequência
    if (isPlaying) {
      console.log('Click ignorado - aguarde a sequência terminar');
      return;
    }

    // Permitir cliques se o jogo está ativo e há uma sequência para comparar
    if (gameState.status !== 'playing' || sequence.length === 0) {
      console.log('Click ignorado - jogo não iniciado ou sem sequência');
      return;
    }
    
    const nextIndex = playerSequence.length;
    const expectedInstrument = sequence[nextIndex];
    const startTime = Date.now();
    
    console.log('Expected:', expectedInstrument, 'Clicked:', instrumentId);
    
    setActiveInstrument(instrumentId);
    await playSoundInstrument(instrumentId);

    setTimeout(() => setActiveInstrument(null), 500);
    
    const newPlayerSequence = [...playerSequence, instrumentId];
    setPlayerSequence(newPlayerSequence);
    setTotalAttempts(prev => prev + 1);
    
    const isCorrect = instrumentId === expectedInstrument;
    const responseTime = Date.now() - startTime;
    
    // Coleta avançada de dados da interação
    try {
      const interactionData = {
        type: 'instrument_click',
        action: 'sequence_attempt',
        instrumentClicked: instrumentId,
        expectedInstrument: expectedInstrument,
        isCorrect: isCorrect,
        sequencePosition: nextIndex,
        totalSequenceLength: sequence.length,
        currentSequence: sequence,
        playerSequence: newPlayerSequence,
        responseTime: responseTime,
        difficulty: difficulty,
        level: currentLevel,
        round: currentRound,
        streak: streak,
        gameState: gameState.status,
        timestamp: Date.now(),
        // Dados específicos para análise de memória auditiva
        memoryLoad: sequence.length,
        sequenceProgress: (nextIndex + 1) / sequence.length,
        // Dados para análise de padrões musicais
        instrumentFrequency: newPlayerSequence.filter(inst => inst === instrumentId).length,
        lastInstruments: newPlayerSequence.slice(-3),
        // Dados para análise de execução
        attemptNumber: totalAttempts + 1,
        sessionTime: Date.now() - gameStartTime
      };
      
      // Processar com coletores avançados
      MusicalSequenceMetrics.recordAdvancedInteraction(interactionData);
      
      // Enviar para orquestrador central se disponível
      if (orchestratorReady && sendMetrics) {
        sendMetrics({
          gameType: 'musical_sequence',
          sessionId: `musical_${gameStartTime}`,
          metrics: interactionData,
          timestamp: Date.now()
        });
      }
    } catch (error) {
      console.error('Erro na coleta avançada:', error);
    }
    
    if (!isCorrect) {
      console.log('Wrong instrument!');
      setStreak(0);
      setFeedback({
        show: true,
        type: 'error',
        message: 'Ops! Instrumento incorreto. Vamos tentar novamente!'
      });
      
      // Feedback sonoro TTS para erro
      speak('Ops! Instrumento incorreto. Vamos tentar novamente!', {
        rate: 0.9,
        pitch: 0.8
      });
      
      setTimeout(() => {
        setFeedback({ show: false, type: '', message: '' });
        setPlayerSequence([]);
        playSequence();
      }, 2000);
      return;
    }
    
    // Correct instrument
    console.log('Correct instrument!');
    setCorrectAttempts(prev => prev + 1);
      // Check if sequence is complete
    if (newPlayerSequence.length === sequence.length) {
    console.log('Sequence completed!');
    const roundScore = sequence.length * 10;
    setScore(prev => prev + roundScore);
    const newStreak = streak + 1;
    setStreak(newStreak);
    
    // Coleta avançada de dados da sequência completa
    try {
      const sequenceCompletionData = {
        type: 'sequence_completion',
        action: 'sequence_completed',
        targetSequence: sequence,
        playerSequence: newPlayerSequence,
        isCorrect: true,
        completionTime: Date.now() - gameStartTime,
        difficulty: difficulty,
        level: currentLevel,
        round: currentRound,
        score: score + roundScore,
        newStreak: newStreak,
        totalAttempts: totalAttempts + 1,
        correctAttempts: correctAttempts + 1,
        // Dados específicos para análise de aprendizado
        sequenceLength: sequence.length,
        accuracy: ((correctAttempts + 1) / (totalAttempts + 1)) * 100,
        improvementIndicator: newStreak > streak,
        timestamp: Date.now()
      };
      
      MusicalSequenceMetrics.recordAdvancedInteraction(sequenceCompletionData);
      
      if (orchestratorReady && sendMetrics) {
        sendMetrics({
          gameType: 'musical_sequence',
          sessionId: `musical_${gameStartTime}`,
          metrics: sequenceCompletionData,
          timestamp: Date.now()
        });
      }
    } catch (error) {
      console.error('Erro na coleta de dados de sequência completa:', error);
    }
    
    setFeedback({
      show: true,
      type: 'success',
      message: '🎉 Excelente! Sequência correta!'
    });
    
    // Feedback sonoro TTS para sucesso
    speak('Excelente! Sequência correta! Parabéns!', {
      rate: 1.0,
      pitch: 1.2
    });
    
    setTimeout(() => {
      setFeedback({ show: false, type: '', message: '' });

      if (newStreak % 3 === 0) {
        setCurrentLevel(prev => prev + 1);
      }
      
      // Controle de rodadas: usar configuração baseada na dificuldade
      setCurrentRound(prev => {
        const nextRound = prev + 1;
        const maxRounds = getRoundsForDifficulty(difficulty);
        
        if (nextRound > maxRounds) {
          // Avançar nível e resetar rodada
          setCurrentLevel(prevLevel => prevLevel + 1);
          return 1;
        }
        return nextRound;
      });
      
      setPlayerSequence([]);
      generateNewSequence();
    }, 2000);
    }
  }, [gameState, isPlaying, playerSequence, sequence, totalAttempts, setActiveInstrument, playSound, setPlayerSequence, setTotalAttempts, difficulty, currentLevel, currentRound, streak, gameStartTime, correctAttempts, score, setStreak, setFeedback, speak, setCorrectAttempts, setScore, setCurrentLevel, generateNewSequence, orchestratorReady, sendMetrics]);

  const handleTTSClick = useCallback(() => {
    speak("Sequência Musical. Escute a sequência de instrumentos musicais e repita tocando na mesma ordem. Use os botões da sequência para reproduzir a ordem que você ouviu.");
  }, [speak]);

  // 🎼 FUNÇÕES DO NOVO JOGO DE RITMO - METRÔNOMO INTERATIVO

  // Função para tocar padrão rítmico
  const playRhythmPattern = useCallback(async () => {
    const activityData = gameState.activityData?.rhythmTiming || {};
    const { targetBPM, totalBeats } = activityData;

    if (!targetBPM || !totalBeats) {
      console.warn('Dados do ritmo não encontrados');
      return;
    }

    setIsPlaying(true);
    speak('Ouça o padrão rítmico da bateria atentamente.');

    // Aguardar TTS terminar
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Gerar padrão rítmico baseado na dificuldade
    const rhythmPattern = generateRhythmPattern(difficulty);
    console.log('🥁 Padrão rítmico gerado:', rhythmPattern);

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        rhythmTiming: {
          ...prev.activityData.rhythmTiming,
          pattern: rhythmPattern,
          gamePhase: 'listening',
          currentBeat: 0
        }
      }
    }));

    // Tocar padrão rítmico usando sons de bateria
    for (let i = 0; i < rhythmPattern.length; i++) {
      const duration = rhythmPattern[i];
      setActiveInstrument(`beat-${i}`);

      console.log(`🥁 Tocando batida ${i + 1}/${rhythmPattern.length}, duração: ${duration}ms`);

      // Som de bateria baseado na duração (mais longo = mais enfático)
      if (duration > 500) {
        await playSound('DRUM', 200); // Som mais forte para durações longas
      } else if (duration > 300) {
        await playSound('BEAT', 150); // Som médio
      } else {
        await playSound('TAP', 100);  // Som mais rápido para durações curtas
      }

      setActiveInstrument(null);

      if (i < rhythmPattern.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    }

    setIsPlaying(false);
    speak('Agora é sua vez! Toque no botão da bateria seguindo o mesmo padrão rítmico.');
  }, [gameState.activityData?.rhythmTiming, difficulty, playSound, speak, setIsPlaying, setActiveInstrument]);

  // Função para gerar padrão rítmico baseado na dificuldade
  const generateRhythmPattern = useCallback((diff) => {
    const patterns = {
      'easy': [400, 400, 400, 400], // Ritmo simples e uniforme
      'medium': [300, 600, 300, 600, 300], // Alternado
      'hard': [200, 400, 200, 800, 200, 400] // Complexo
    };

    return patterns[diff] || patterns['easy'];
  }, []);

  // Função para alternar gravação de ritmo
  const toggleRhythmRecording = useCallback(() => {
    setGameState(prev => {
      const currentData = prev.activityData?.rhythmTiming || {};
      const isCurrentlyRecording = currentData.isRecording;

      if (isCurrentlyRecording) {
        // Parar gravação e avaliar
        console.log('🛑 Parando gravação de ritmo');
        calculateRhythmAccuracy();
        speak('Gravação finalizada! Calculando sua precisão...');

        return {
          ...prev,
          activityData: {
            ...prev.activityData,
            rhythmTiming: {
              ...currentData,
              isRecording: false,
              gamePhase: 'finished'
            }
          }
        };
      } else {
        // Iniciar gravação
        console.log('🔴 Iniciando gravação de ritmo');
        speak('Gravação iniciada! Toque na bateria seguindo o padrão rítmico que você ouviu.');

        return {
          ...prev,
          activityData: {
            ...prev.activityData,
            rhythmTiming: {
              ...currentData,
              isRecording: true,
              gamePhase: 'recording', // Definir fase específica para gravação
              userTaps: [],
              startTime: Date.now()
            }
          }
        };
      }
    });
  }, [speak]);

  const startMetronome = useCallback(async () => {
    const activityData = gameState.activityData?.rhythmTiming || {};
    const { targetBPM, totalBeats } = activityData;

    if (!targetBPM || !totalBeats) {
      console.warn('Dados do ritmo não encontrados');
      return;
    }

    // Calcular intervalo correto baseado no BPM
    const beatInterval = (60 / targetBPM) * 1000; // Converter BPM para milissegundos
    
    console.log(`🥁 Iniciando metrônomo: ${targetBPM} BPM, Intervalo: ${beatInterval}ms`);

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        rhythmTiming: {
          ...prev.activityData.rhythmTiming,
          metronomeActive: true,
          gamePhase: 'listening',
          currentBeat: 0,
          beatInterval: beatInterval // Atualizar o intervalo calculado
        }
      }
    }));

    speak(`Ouça o metrônomo no ritmo de ${targetBPM} batidas por minuto. Depois toque junto!`);

    // Aguardar TTS terminar
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Tocar metrônomo por totalBeats batidas com som de bateria
    for (let beat = 1; beat <= totalBeats; beat++) {
      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          rhythmTiming: {
            ...prev.activityData.rhythmTiming,
            currentBeat: beat
          }
        }
      }));

      console.log(`🥁 Batida ${beat}/${totalBeats}`);
      
      // Som do metrônomo usando bateria (mais forte no primeiro tempo)
      const isDrumSound = beat === 1 || beat % 4 === 1;
      await playSound(isDrumSound ? 'DRUM' : 'BEAT', 150);

      if (beat < totalBeats) {
        await new Promise(resolve => setTimeout(resolve, beatInterval));
      }
    }

    // Fase de seguir o metrônomo
    setTimeout(() => {
      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          rhythmTiming: {
            ...prev.activityData.rhythmTiming,
            gamePhase: 'playing',
            currentBeat: 0,
            startTime: Date.now(),
            userTaps: []
          }
        }
      }));

      speak('Agora toque junto! Siga o metrônomo e mantenha o ritmo.');
      startPlayingPhase();
    }, 1000);
  }, [gameState.activityData?.rhythmTiming, playSound, speak]);

  const startPlayingPhase = useCallback(async () => {
    const activityData = gameState.activityData?.rhythmTiming || {};
    const { targetBPM, totalBeats, beatInterval } = activityData;
    
    console.log(`🎵 Iniciando fase de tocar junto: ${targetBPM} BPM`);
    
    // Tocar metrônomo novamente, mas agora o usuário deve seguir
    for (let beat = 1; beat <= totalBeats; beat++) {
      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          rhythmTiming: {
            ...prev.activityData.rhythmTiming,
            currentBeat: beat
          }
        }
      }));
      
      console.log(`🎵 Batida guia ${beat}/${totalBeats}`);
      
      // Som do metrônomo mais suave para orientar o usuário (som de bateria mais baixo)
      await playSound('BEAT', 80);
      
      if (beat < totalBeats) {
        await new Promise(resolve => setTimeout(resolve, beatInterval));
      }
    }
    
    // Finalizar e calcular resultado
    setTimeout(() => {
      console.log('🏁 Finalizando teste de ritmo');
      calculateRhythmAccuracy();
    }, 500);
  }, [gameState.activityData?.rhythmTiming, playSound]);

  const handleRhythmTap = useCallback(() => {
    const now = Date.now();
    const activityData = gameState.activityData?.rhythmTiming || {};
    
    console.log('🥁 CLIQUE NA BATERIA DETECTADO!');
    console.log('Estado atual:', {
      gamePhase: activityData.gamePhase,
      isRecording: activityData.isRecording,
      isPlaying: isPlaying
    });
    
    // SEMPRE permitir o som quando não está tocando sequência
    if (isPlaying) {
      console.log('🚫 Aguarde a sequência terminar');
      return;
    }
    
    console.log('🥁 TOCANDO SOM DA BATERIA!');
    
    setActiveInstrument('tap');
    setTimeout(() => setActiveInstrument(null), 200);
    
    // Som de feedback do usuário - SEMPRE tocar!
    playSound('TAP', 200);

    // Registrar toque apenas se estiver gravando
    if (activityData.isRecording || activityData.gamePhase === 'recording' || activityData.gamePhase === 'playing') {
      console.log('📝 Registrando toque no ritmo');
      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          rhythmTiming: {
            ...prev.activityData.rhythmTiming,
            userTaps: [...(prev.activityData.rhythmTiming.userTaps || []), now]
          }
        }
      }));
      
      console.log(`🎯 Total de toques registrados: ${(activityData.userTaps?.length || 0) + 1}`);
    } else {
      console.log('🎵 Som tocado sem registrar (apenas feedback)');
    }
  }, [gameState.activityData?.rhythmTiming, playSound, isPlaying]);

  const calculateRhythmAccuracy = useCallback(() => {
    setGameState(prev => {
      const currentData = prev.activityData?.rhythmTiming || {};
      const { userTaps, startTime, targetBPM, totalBeats, beatInterval } = currentData;

      console.log('🎯 Calculando precisão do ritmo:', {
        userTaps: userTaps?.length || 0,
        targetBPM,
        totalBeats,
        beatInterval
      });

      if (!userTaps || userTaps.length === 0) {
        speak('Você não tocou nenhuma batida na bateria. Tente novamente!');
        return {
          ...prev,
          activityData: {
            ...prev.activityData,
            rhythmTiming: {
              ...currentData,
              gamePhase: 'finished',
              accuracy: 0,
              perfectHits: 0
            }
          }
        };
      }

      // Calcular precisão baseada no metrônomo
      let accuracy = 0;
      let perfectHits = 0;

      if (startTime && beatInterval && totalBeats) {
        // Calcular quando cada batida deveria ter acontecido
        const expectedBeats = [];
        for (let i = 0; i < totalBeats; i++) {
          expectedBeats.push(startTime + (i * beatInterval));
        }

        console.log('🎵 Batidas esperadas:', expectedBeats.map(beat => new Date(beat).toLocaleTimeString()));
        console.log('🥁 Batidas do usuário:', userTaps.map(tap => new Date(tap).toLocaleTimeString()));

        // Tolerância baseada no BPM (mais rápido = menos tolerância)
        const tolerance = Math.max(beatInterval * 0.25, 200); // Mínimo 200ms, máximo 25% do intervalo
        
        console.log(`⏰ Tolerância: ${tolerance}ms`);

        expectedBeats.forEach((expectedTime, index) => {
          const closestTap = userTaps.reduce((closest, tapTime) => {
            const currentDistance = Math.abs(tapTime - expectedTime);
            const closestDistance = Math.abs(closest - expectedTime);
            return currentDistance < closestDistance ? tapTime : closest;
          });

          const error = Math.abs(closestTap - expectedTime);
          console.log(`🎯 Batida ${index + 1}: esperada em ${new Date(expectedTime).toLocaleTimeString()}, usuário tocou em ${new Date(closestTap).toLocaleTimeString()}, erro: ${error}ms`);
          
          if (error <= tolerance) {
            perfectHits++;
            console.log('✅ Batida correta!');
          } else {
            console.log('❌ Batida fora do tempo');
          }
        });

        accuracy = Math.max(0, Math.round((perfectHits / totalBeats) * 100));
      } else {
        console.warn('⚠️ Dados insuficientes para calcular precisão');
        accuracy = 0;
      }

      console.log(`🏆 Resultado final: ${perfectHits}/${totalBeats} corretas, ${accuracy}% de precisão`);

      const feedbackMessage = accuracy >= 80 ? 'Excelente timing na bateria!' :
                             accuracy >= 60 ? 'Bom ritmo! Continue praticando!' :
                             'Continue praticando o timing da bateria!';

      speak(`Você acertou ${perfectHits} de ${totalBeats} batidas. Precisão: ${accuracy}%. ${feedbackMessage}`);

      // MANTER na mesma atividade de ritmo - não mudar para outra atividade!
      setTimeout(() => {
        console.log('🔄 Gerando nova rodada de RITMO (mesma atividade)');
        generateRhythmTiming(); // Chamar diretamente a função de ritmo ao invés de generateNewRound
      }, 4000);

      return {
        ...prev,
        activityData: {
          ...prev.activityData,
          rhythmTiming: {
            ...currentData,
            gamePhase: 'finished',
            accuracy: accuracy,
            perfectHits: perfectHits,
            metronomeActive: false,
            isRecording: false
          }
        }
      };
    });
  }, [speak, generateNewRound]);

  // 🎵 FUNÇÕES DO JOGO DE ALTURA (PITCH DISCRIMINATION)

  // Função para tocar tom com frequência específica
  const playToneWithFrequency = useCallback(async (frequency, duration = 1000) => {
    console.log('🔊 TOCANDO FREQUÊNCIA:', frequency + 'Hz por', duration + 'ms');
    
    // FORÇAR reinicialização do AudioContext se necessário
    if (!audioContextRef.current || audioContextRef.current.state === 'closed') {
      console.log('🎵 Reinicializando AudioContext...');
      audioContextRef.current = null;
      await initAudioContext();
    }

    // Verificar se AudioContext está suspenso e tentar retomar
    if (audioContextRef.current && audioContextRef.current.state === 'suspended') {
      console.log('🎵 Retomando AudioContext suspenso...');
      try {
        await audioContextRef.current.resume();
        console.log('✅ AudioContext retomado com sucesso');
      } catch (error) {
        console.error('❌ Erro ao retomar AudioContext:', error);
        // Tentar reinicializar completamente
        audioContextRef.current = null;
        await initAudioContext();
      }
    }

    try {
      const audioContext = audioContextRef.current;
      
      if (!audioContext || audioContext.state === 'closed') {
        console.error('❌ AudioContext ainda não disponível após reinicialização');
        // Tentar uma última vez com novo AudioContext
        try {
          const newAudioContext = new (window.AudioContext || window.webkitAudioContext)();
          audioContextRef.current = newAudioContext;
          console.log('🎵 Novo AudioContext criado diretamente');
        } catch (directError) {
          console.error('❌ Falha ao criar AudioContext diretamente:', directError);
          return;
        }
      }
      
      const finalAudioContext = audioContextRef.current;
      const oscillator = finalAudioContext.createOscillator();
      const gainNode = finalAudioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(finalAudioContext.destination);

      oscillator.frequency.setValueAtTime(frequency, finalAudioContext.currentTime);
      oscillator.type = 'sine';

      gainNode.gain.setValueAtTime(0.3, finalAudioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, finalAudioContext.currentTime + duration / 1000);

      oscillator.start(finalAudioContext.currentTime);
      oscillator.stop(finalAudioContext.currentTime + duration / 1000);

      console.log('✅ Tom iniciado com sucesso');

      return new Promise(resolve => {
        oscillator.onended = () => {
          console.log('🎵 Tom finalizado');
          resolve();
        };
      });
    } catch (error) {
      console.error('❌ Erro ao tocar frequência:', error);
      
      // Último recurso: tentar com playSound padrão
      console.log('🎵 Tentando usar playSound como fallback...');
      try {
        await playSound('PIANO', duration);
      } catch (fallbackError) {
        console.error('❌ Fallback também falhou:', fallbackError);
      }
    }
  }, [initAudioContext, playSound]);

  // Função melhorada para tocar tom de referência
  const playReferencePitch = useCallback(async () => {
    const activityData = gameState.activityData?.pitchDiscrimination || {};
    const { referencePitch, pitchSystem } = activityData;

    console.log('🎵 TOCANDO TOM DE REFERÊNCIA MELHORADO:', referencePitch + 'Hz');

    if (!referencePitch) {
      console.warn('❌ Frequência de referência não encontrada!');
      speak('Erro: Tom de referência não disponível.');
      return;
    }

    // Forçar interação do usuário para desbloquear áudio
    if (audioContextRef.current && audioContextRef.current.state === 'suspended') {
      try {
        await audioContextRef.current.resume();
        console.log('🎵 AudioContext desbloqueado por interação do usuário');
      } catch (error) {
        console.error('❌ Erro ao desbloquear AudioContext:', error);
      }
    }

    setIsPlaying(true);
    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        pitchDiscrimination: {
          ...prev.activityData.pitchDiscrimination,
          currentPhase: 'reference',
          hasPlayedReference: true
        }
      }
    }));

    // Anunciar o que está sendo tocado
    speak('Tom de referência - Lá 440 Hz');
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Tocar tom de referência com duração mais longa para melhor percepção
    console.log('🔊 Iniciando tom de referência com frequência:', referencePitch);
    await playToneWithFrequency(referencePitch, 2500);
    console.log('✅ Tom de referência finalizado');

    setTimeout(() => {
      setIsPlaying(false);
      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          pitchDiscrimination: {
            ...prev.activityData.pitchDiscrimination,
            currentPhase: 'waiting_test'
          }
        }
      }));

      speak('Agora clique para ouvir o tom de teste.');
    }, 2500);
  }, [gameState.activityData?.pitchDiscrimination, playToneWithFrequency, speak]);

  // Função melhorada para tocar tom de teste
  const playTestPitch = useCallback(async () => {
    const activityData = gameState.activityData?.pitchDiscrimination || {};
    const { targetPitch, targetPitchObj } = activityData;

    console.log('🎵 TOCANDO TOM DE TESTE MELHORADO:', targetPitch + 'Hz');

    if (!targetPitch) {
      console.warn('❌ Frequência de teste não encontrada!');
      speak('Erro: Tom de teste não disponível.');
      return;
    }

    // Forçar interação do usuário para desbloquear áudio
    if (audioContextRef.current && audioContextRef.current.state === 'suspended') {
      try {
        await audioContextRef.current.resume();
        console.log('🎵 AudioContext desbloqueado por interação do usuário');
      } catch (error) {
        console.error('❌ Erro ao desbloquear AudioContext:', error);
      }
    }

    setIsPlaying(true);
    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        pitchDiscrimination: {
          ...prev.activityData.pitchDiscrimination,
          currentPhase: 'test',
          hasPlayedTest: true
        }
      }
    }));

    // Anunciar o tom de teste
    const pitchName = targetPitchObj?.name || `${targetPitch}Hz`;
    speak(`Tom de teste - ${pitchName}`);
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Tocar tom de teste com duração mais longa
    console.log('🔊 Iniciando tom de teste com frequência:', targetPitch);
    await playToneWithFrequency(targetPitch, 2500);
    console.log('✅ Tom de teste finalizado');

    setTimeout(() => {
      setIsPlaying(false);
      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          pitchDiscrimination: {
            ...prev.activityData.pitchDiscrimination,
            currentPhase: 'comparison',
            canAnswer: true
          }
        }
      }));

      speak('Agora compare os dois tons. O tom de teste é mais agudo, mais grave ou igual ao tom de referência?');
    }, 2500);
  }, [gameState.activityData?.pitchDiscrimination, playToneWithFrequency, speak]);

  // Função para tocar ambos os tons em sequência para comparação
  const playBothPitches = useCallback(async () => {
    const activityData = gameState.activityData?.pitchDiscrimination || {};
    const { referencePitch, targetPitch } = activityData;

    if (!referencePitch || !targetPitch) {
      speak('Erro: Tons não disponíveis para comparação.');
      return;
    }

    setIsPlaying(true);
    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        pitchDiscrimination: {
          ...prev.activityData.pitchDiscrimination,
          currentPhase: 'comparing'
        }
      }
    }));

    speak('Comparação: primeiro o tom de referência, depois o tom de teste.');
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Tocar tom de referência
    console.log('🔊 Comparação - Tom de referência:', referencePitch);
    await playToneWithFrequency(referencePitch, 2000);

    // Pausa entre os tons
    await new Promise(resolve => setTimeout(resolve, 500));

    // Tocar tom de teste
    console.log('🔊 Comparação - Tom de teste:', targetPitch);
    await playToneWithFrequency(targetPitch, 2000);

    setTimeout(() => {
      setIsPlaying(false);
      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          pitchDiscrimination: {
            ...prev.activityData.pitchDiscrimination,
            currentPhase: 'answer',
            canAnswer: true
          }
        }
      }));

      speak('Comparação concluída. Agora escolha sua resposta.');
    }, 2000);
  }, [gameState.activityData?.pitchDiscrimination, playToneWithFrequency, speak]);

  // Função para avaliar resposta do jogo de altura
  const evaluatePitchAnswer = useCallback((userAnswer) => {
    const activityData = gameState.activityData?.pitchDiscrimination || {};
    const { correctAnswer, referencePitch, targetPitch, frequencyDifference, difficulty } = activityData;

    const isCorrect = userAnswer === correctAnswer;

    // Calcular pontuação baseada na dificuldade e precisão
    let score = 0;
    if (isCorrect) {
      score = difficulty === 'hard' ? 100 : difficulty === 'medium' ? 85 : 70;
    }

    // Feedback detalhado
    let feedback = '';
    const freqDiff = Math.abs(targetPitch - referencePitch);

    if (isCorrect) {
      feedback = `Excelente! O tom de teste (${targetPitch}Hz) é realmente ${
        correctAnswer === 'higher' ? 'mais agudo' :
        correctAnswer === 'lower' ? 'mais grave' : 'igual'
      } que o tom de referência (${referencePitch}Hz). `;

      if (difficulty === 'hard') {
        feedback += `Diferença sutil de apenas ${freqDiff}Hz detectada com precisão!`;
      } else if (difficulty === 'medium') {
        feedback += `Boa percepção da diferença de ${freqDiff}Hz!`;
      } else {
        feedback += `Diferença de ${freqDiff}Hz identificada corretamente!`;
      }
    } else {
      feedback = `Não é bem assim. O tom de teste (${targetPitch}Hz) é ${
        correctAnswer === 'higher' ? 'mais agudo' :
        correctAnswer === 'lower' ? 'mais grave' : 'igual'
      } que o tom de referência (${referencePitch}Hz). `;

      feedback += `A diferença é de ${freqDiff}Hz. Tente ouvir novamente com mais atenção.`;
    }

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        pitchDiscrimination: {
          ...prev.activityData.pitchDiscrimination,
          userAnswer: userAnswer,
          isCorrect: isCorrect,
          score: score,
          feedback: feedback,
          currentPhase: 'result',
          showResult: true
        }
      }
    }));

    speak(feedback);

    // Avançar para próxima rodada após delay
    if (isCorrect) {
      setTimeout(() => {
        generateNewRound();
      }, 4000);
    } else {
      // Permitir nova tentativa
      setTimeout(() => {
        setGameState(prev => ({
          ...prev,
          activityData: {
            ...prev.activityData,
            pitchDiscrimination: {
              ...prev.activityData.pitchDiscrimination,
              currentPhase: 'comparison',
              canAnswer: true,
              showResult: false
            }
          }
        }));
      }, 5000);
    }
  }, [gameState.activityData?.pitchDiscrimination, speak, generateNewRound]);

  // Função para verificar resposta da discriminação de altura
  const checkPitchAnswer = useCallback((userAnswer) => {
    const activityData = gameState.activityData?.pitchDiscrimination || {};
    const { referencePitch, targetPitch } = activityData;

    if (!userAnswer || !referencePitch || !targetPitch) {
      speak('Por favor, selecione uma resposta.');
      return;
    }

    console.log('🎵 Verificando resposta de altura:', {
      referencia: referencePitch,
      teste: targetPitch,
      resposta: userAnswer
    });

    // Determinar a resposta correta
    let correctAnswer = '';
    if (targetPitch > referencePitch) {
      correctAnswer = 'higher';
    } else if (targetPitch < referencePitch) {
      correctAnswer = 'lower';
    } else {
      correctAnswer = 'same';
    }

    const isCorrect = userAnswer === correctAnswer;
    
    // Feedback detalhado
    let feedbackMessage = '';
    if (isCorrect) {
      feedbackMessage = `Correto! O tom teste (${targetPitch}Hz) é `;
      if (correctAnswer === 'higher') {
        feedbackMessage += `mais alto (agudo) que o tom referência (${referencePitch}Hz).`;
      } else if (correctAnswer === 'lower') {
        feedbackMessage += `mais baixo (grave) que o tom referência (${referencePitch}Hz).`;
      } else {
        feedbackMessage += `igual ao tom referência (${referencePitch}Hz).`;
      }
    } else {
      feedbackMessage = `Incorreto. O tom teste (${targetPitch}Hz) `;
      if (correctAnswer === 'higher') {
        feedbackMessage += `é mais alto (agudo) que a referência (${referencePitch}Hz).`;
      } else if (correctAnswer === 'lower') {
        feedbackMessage += `é mais baixo (grave) que a referência (${referencePitch}Hz).`;
      } else {
        feedbackMessage += `é igual à referência (${referencePitch}Hz).`;
      }
    }

    // Atualizar estado com resultado
    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        pitchDiscrimination: {
          ...prev.activityData.pitchDiscrimination,
          isAnswered: true,
          isCorrect: isCorrect,
          correctAnswer: correctAnswer,
          feedback: feedbackMessage
        }
      }
    }));

    speak(feedbackMessage);

    // Avançar para próxima rodada após delay
    setTimeout(() => {
      generatePitchDiscrimination(); // Nova rodada de discriminação de altura
    }, 4000);

  }, [gameState.activityData?.pitchDiscrimination, speak, generatePitchDiscrimination]);

  // =====================================================
  // 🎵 INTERFACE ESPECÍFICA PARA CADA ATIVIDADE MUSICAL
  // =====================================================

  // =====================================================
  // 🎯 INTERFACES REDESENHADAS - LAYOUTS ÚNICOS PARA CADA ATIVIDADE
  // =====================================================

  const renderActivityInterface = () => {
    const currentActivity = gameState.currentActivity;

    switch (currentActivity) {
      case ACTIVITY_TYPES.SEQUENCE_REPRODUCTION.id:
        return renderSequenceReproduction();
      case ACTIVITY_TYPES.RHYTHM_TIMING.id:
        return renderRhythmTiming();
      case ACTIVITY_TYPES.PITCH_DISCRIMINATION.id:
        return renderPitchDiscrimination();
      case ACTIVITY_TYPES.PATTERN_PREDICTION.id:
        return renderPatternPrediction();
      default:
        return renderSequenceReproduction();
    }
  };

  // 🔄 REPRODUÇÃO DE SEQUÊNCIA - Layout padrão LetterRecognition
  const renderSequenceReproduction = () => {
    const activityData = gameState.activityData?.sequenceReproduction || {};

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>🔄 Teste de Memória Auditiva Sequencial</h2>
          <p className={styles.questionSubtitle}>Memorize a ordem exata dos instrumentos e reproduza a sequência</p>
        </div>

        {/* Área de objetos - padrão LetterRecognition */}
        <div className={styles.objectsDisplay}>
          <div className={styles.sequenceSlots}>
            {sequence.map((instrumentId, index) => {
              const instrument = instruments.find(inst => inst.id === instrumentId);
              const isCurrentlyPlaying = isPlaying && activeInstrument === instrumentId;
              const isCompleted = playerSequence.length > index;
              const isCorrect = isCompleted && playerSequence[index] === instrumentId;

              return (
                <div
                  key={index}
                  className={`${styles.countingObject} ${
                    isCurrentlyPlaying ? styles.playing : ''
                  } ${isCompleted ? (isCorrect ? styles.correct : styles.incorrect) : ''}`}
                  style={{
                    backgroundColor: isCorrect ? 'rgba(76, 175, 80, 0.3)' :
                                   isCompleted ? 'rgba(244, 67, 54, 0.3)' :
                                   'var(--card-background)',
                    border: '2px solid rgba(255,255,255,0.3)',
                    borderRadius: '12px',
                    padding: '1rem',
                    margin: '0.5rem',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center'
                  }}
                >
                  <div style={{ fontSize: '0.8rem', opacity: 0.8 }}>{index + 1}</div>
                  <div style={{ fontSize: '3rem', margin: '0.5rem 0' }}>
                    {isCurrentlyPlaying ? instrument?.emoji : '?'}
                  </div>
                  <div style={{ fontSize: '0.9rem' }}>{instrument?.name}</div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Opções de resposta - padrão LetterRecognition */}
        <div className={styles.answerOptions}>
          {instruments.map((instrument) => (
            <button
              key={instrument.id}
              className={`${styles.answerButton} ${
                activeInstrument === instrument.id ? styles.playing : ''
              }`}
              onClick={() => handleInstrumentClick(instrument.id)}
              disabled={isPlaying || playerSequence.length >= sequence.length}
              aria-label={`Escolher ${instrument.name}`}
            >
              <div style={{
                fontSize: '2rem',
                marginBottom: '0.5rem'
              }}>
                {instrument.emoji}
              </div>
              <div style={{ fontSize: '0.9rem' }}>
                {instrument.name}
              </div>
            </button>
          ))}
        </div>

        {/* Progresso */}
        {playerSequence.length > 0 && (
          <div style={{
            textAlign: 'center',
            marginTop: '1rem',
            color: 'rgba(255,255,255,0.8)'
          }}>
            Progresso: {playerSequence.length} / {sequence.length}
          </div>
        )}
      </div>
    );
  };

  // ⏱️ SINCRONIZAÇÃO RÍTMICA - Layout padrão LetterRecognition
  const renderRhythmTiming = () => {
    const activityData = gameState.activityData?.rhythmTiming || {};
    const pattern = activityData.pattern || [];

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>⏱️ Teste de Coordenação Temporal</h2>
          <p className={styles.questionSubtitle}>
            1° Ouça o padrão rítmico | 2° Reproduza tocando no mesmo ritmo | 3° Tente ser preciso!
          </p>
        </div>

        {/* Área de objetos - padrão LetterRecognition */}
        <div className={styles.objectsDisplay}>
          <div className={styles.rhythmDisplay} style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '2rem',
            padding: '2rem',
            backgroundColor: 'var(--card-background)',
            borderRadius: '16px',
            border: '2px solid rgba(255,255,255,0.3)'
          }}>
            <div style={{
              fontSize: '4rem',
              animation: isPlaying ? 'pulse 1s infinite' : 'none'
            }}>
              🎼
            </div>
            
            <div style={{ 
              fontSize: '1.4rem', 
              color: 'rgba(255,255,255,0.9)',
              fontWeight: 'bold',
              textAlign: 'center'
            }}>
              {isPlaying ? '🔊 Tocando Padrão...' : activityData.isRecording ? '🔴 Gravando seu Ritmo' : '▶️ Clique para Ouvir o Padrão'}
            </div>
            
            {/* Visualizador do padrão */}
            <div style={{
              display: 'flex',
              gap: '0.5rem',
              flexWrap: 'wrap',
              justifyContent: 'center'
            }}>
              {pattern.map((duration, index) => (
                <div
                  key={index}
                  style={{
                    width: `${Math.max(20, duration / 20)}px`,
                    height: '20px',
                    backgroundColor: isPlaying && activeInstrument === `beat-${index}` ? 
                      'rgba(255, 193, 7, 0.8)' : 'rgba(255,255,255,0.6)',
                    borderRadius: '10px',
                    transition: 'all 0.3s ease'
                  }}
                />
              ))}
            </div>
            
            <div style={{ fontSize: '1rem', color: 'rgba(255,255,255,0.7)' }}>
              Padrão: {pattern.length} batidas | BPM: {activityData.targetBPM || 120}
            </div>
          </div>
        </div>

        {/* Opções de resposta - padrão LetterRecognition */}
        <div className={styles.answerOptions}>
          {/* Botão para ouvir o padrão */}
          <button
            className={`${styles.answerButton} ${isPlaying ? styles.playing : ''}`}
            onClick={() => playRhythmPattern()}
            disabled={isPlaying || activityData.isRecording}
            aria-label="Ouvir padrão rítmico"
            style={{
              backgroundColor: isPlaying ? 'rgba(76, 175, 80, 0.3)' : 'var(--card-background)',
              borderColor: isPlaying ? 'rgba(76, 175, 80, 0.5)' : 'rgba(255,255,255,0.3)'
            }}
          >
            <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>
              {isPlaying ? '🔊' : '▶️'}
            </div>
            <div style={{ fontSize: '0.9rem' }}>
              {isPlaying ? 'Tocando...' : 'Ouvir Padrão'}
            </div>
          </button>

          {/* Botão para tocar no ritmo */}
          <button
            className={`${styles.answerButton} ${activeInstrument === 'tap' ? styles.playing : ''}`}
            onClick={() => handleRhythmTap()}
            disabled={false} // NUNCA desabilitar - sempre permitir som
            aria-label="Tocar bateria"
            style={{
              minHeight: '120px',
              fontSize: '3rem',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: '0.5rem',
              backgroundColor: activityData.isRecording ? 'rgba(244, 67, 54, 0.3)' : 
                               activeInstrument === 'tap' ? 'rgba(255, 193, 7, 0.5)' : 'var(--card-background)',
              borderColor: activityData.isRecording ? 'rgba(244, 67, 54, 0.5)' : 
                          activeInstrument === 'tap' ? 'rgba(255, 193, 7, 0.8)' : 'rgba(255,255,255,0.3)',
              cursor: 'pointer'
            }}
          >
            <div>{activityData.isRecording ? '🔴' : '🥁'}</div>
            <div style={{ fontSize: '1rem' }}>
              {activityData.isRecording ? 'GRAVANDO...' : 'TOQUE AQUI'}
            </div>
          </button>

          {/* Botão de controle da gravação */}
          <button
            className={styles.answerButton}
            onClick={() => toggleRhythmRecording()}
            disabled={isPlaying || activityData.gamePhase === 'finished'}
            aria-label={activityData.isRecording ? 'Parar e avaliar' : 'Iniciar gravação'}
            style={{
              backgroundColor: activityData.isRecording ? 'rgba(244, 67, 54, 0.3)' :
                             activityData.gamePhase === 'finished' ? 'rgba(76, 175, 80, 0.3)' : 'var(--card-background)',
              borderColor: activityData.isRecording ? 'rgba(244, 67, 54, 0.5)' :
                          activityData.gamePhase === 'finished' ? 'rgba(76, 175, 80, 0.5)' : 'rgba(255,255,255,0.3)'
            }}
          >
            <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>
              {activityData.isRecording ? '⏹️' :
               activityData.gamePhase === 'finished' ? '✅' : '🎙️'}
            </div>
            <div style={{ fontSize: '0.9rem' }}>
              {activityData.isRecording ? 'Finalizar' :
               activityData.gamePhase === 'finished' ? 'Concluído' : 'Gravar'}
            </div>
          </button>

          {/* Botão para próxima rodada (apenas quando finalizado) */}
          {activityData.gamePhase === 'finished' && (
            <button
              className={styles.answerButton}
              onClick={() => generateRhythmTiming()} // Manter na atividade de ritmo
              style={{
                backgroundColor: 'rgba(33, 150, 243, 0.3)',
                borderColor: 'rgba(33, 150, 243, 0.5)'
              }}
            >
              <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🥁</div>
              <div style={{ fontSize: '0.9rem' }}>Novo Ritmo</div>
            </button>
          )}
        </div>

        {/* Feedback de precisão */}
        {activityData.accuracy !== undefined && activityData.accuracy > 0 && (
          <div style={{
            textAlign: 'center',
            marginTop: '1rem',
            padding: '1rem',
            backgroundColor: activityData.accuracy >= 70 ? 'rgba(76, 175, 80, 0.2)' : 'rgba(255, 193, 7, 0.2)',
            borderRadius: '8px',
            color: 'white'
          }}>
            <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>
              Precisão: {activityData.accuracy}%
            </div>
            <div style={{ fontSize: '0.9rem', marginTop: '0.5rem' }}>
              {activityData.accuracy >= 70 ? '🎉 Excelente ritmo!' : '👍 Continue praticando!'}
            </div>
          </div>
        )}

        {/* Instruções */}
        <div style={{
          textAlign: 'center',
          marginTop: '1rem',
          padding: '1rem',
          backgroundColor: 'rgba(0,0,0,0.3)',
          borderRadius: '8px',
          color: 'rgba(255,255,255,0.8)',
          fontSize: '0.9rem'
        }}>
          <div><strong>Como jogar:</strong></div>
          <div>1. Clique em "Ouvir Padrão" para escutar o ritmo</div>
          <div>2. Toque no botão 🥁 seguindo o mesmo ritmo</div>
          <div>3. Clique "Finalizar" para ver sua precisão</div>
        </div>
      </div>
    );
  };

  // 🎵 DISCRIMINAÇÃO DE ALTURA - Layout padrão LetterRecognition
  const renderPitchDiscrimination = () => {
    const activityData = gameState.activityData?.pitchDiscrimination || {};

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>🎵 Teste de Processamento Auditivo</h2>
          <p className={styles.questionSubtitle}>Compare tons musicais e identifique diferenças sutis de altura</p>
        </div>

        <div className={styles.objectsDisplay}>
          <div style={{ display: 'flex', gap: '2rem', justifyContent: 'center', alignItems: 'center', flexWrap: 'wrap' }}>
            <div style={{ textAlign: 'center', padding: '2rem', border: '3px solid rgba(76, 175, 80, 0.6)', borderRadius: '12px', backgroundColor: 'rgba(76, 175, 80, 0.1)', minWidth: '200px' }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🎯</div>
              <div style={{ fontSize: '1.2rem', marginBottom: '1rem', fontWeight: 'bold' }}>Tom Referência</div>
              <div style={{ fontSize: '1rem', marginBottom: '1rem' }}>{activityData.referencePitch || 220}Hz</div>
              <button className={styles.answerButton} onClick={() => playReferencePitch()} disabled={isPlaying}>
                {isPlaying ? '🔊 Tocando...' : '▶️ Tocar Referência'}
              </button>
            </div>

            <div style={{ fontSize: '3rem', color: 'rgba(255,255,255,0.5)', fontWeight: 'bold' }}>VS</div>

            <div style={{ textAlign: 'center', padding: '2rem', border: '3px solid rgba(255, 152, 0, 0.6)', borderRadius: '12px', backgroundColor: 'rgba(255, 152, 0, 0.1)', minWidth: '200px' }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🎼</div>
              <div style={{ fontSize: '1.2rem', marginBottom: '1rem', fontWeight: 'bold' }}>Tom Teste</div>
              <div style={{ fontSize: '1rem', marginBottom: '1rem' }}>{activityData.targetPitch || 240}Hz</div>
              <button className={styles.answerButton} onClick={() => playTestPitch()} disabled={isPlaying}>
                {isPlaying ? '🔊 Tocando...' : '▶️ Tocar Teste'}
              </button>
            </div>
          </div>

          <div className={styles.answerOptions}>
            <button className={styles.answerButton} onClick={() => evaluatePitchAnswer('higher')} disabled={isPlaying} style={{ backgroundColor: 'rgba(244, 67, 54, 0.3)' }}>
              <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>⬆️</div>
              <div>MAIS AGUDO</div>
            </button>
            <button className={styles.answerButton} onClick={() => evaluatePitchAnswer('same')} disabled={isPlaying} style={{ backgroundColor: 'rgba(255, 193, 7, 0.3)' }}>
              <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>➡️</div>
              <div>IGUAL</div>
            </button>
            <button className={styles.answerButton} onClick={() => evaluatePitchAnswer('lower')} disabled={isPlaying} style={{ backgroundColor: 'rgba(33, 150, 243, 0.3)' }}>
              <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>⬇️</div>
              <div>MAIS GRAVE</div>
            </button>
          </div>

          {activityData.showResult && (
            <div style={{ textAlign: 'center', marginTop: '2rem', padding: '1.5rem', backgroundColor: activityData.isCorrect ? 'rgba(76, 175, 80, 0.2)' : 'rgba(244, 67, 54, 0.2)', borderRadius: '12px' }}>
              <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>{activityData.isCorrect ? '✅' : '❌'}</div>
              <div style={{ fontSize: '1.2rem', fontWeight: 'bold', marginBottom: '1rem' }}>{activityData.isCorrect ? 'Correto!' : 'Tente novamente'}</div>
              <div style={{ fontSize: '1rem' }}>{activityData.feedback}</div>
            </div>
          )}
        </div>
      </div>
    );
  };

  // 🔮 PREDIÇÃO DE PADRÕES - Layout padrão LetterRecognition
  const renderPatternPrediction = () => {
    const activityData = gameState.activityData?.patternPrediction || {};

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>🔮 Teste de Raciocínio Lógico Musical</h2>
          <p className={styles.questionSubtitle}>Analise o padrão musical e preveja qual elemento vem a seguir</p>
        </div>
        <div className={styles.objectsDisplay}>
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>🧩</div>
            <div style={{ fontSize: '1.2rem', marginBottom: '1rem', fontWeight: 'bold' }}>Padrão Musical</div>
            <div style={{ fontSize: '1rem', color: 'rgba(255,255,255,0.8)' }}>
              Tipo: {activityData.patternType || 'Analise a sequência'}
            </div>
          </div>
        </div>
        <div className={styles.answerOptions}>
          {(activityData.options || []).map((instrumentId) => {
            const instrument = instruments.find(inst => inst.id === instrumentId);
            const isSelected = activityData.userAnswer === instrumentId;
            return (
              <button
                key={instrumentId}
                className={styles.answerButton}
                onClick={() => checkPatternAnswer(instrumentId)}
                disabled={isPlaying}
                style={{ border: isSelected ? '3px solid rgba(255, 193, 7, 0.8)' : '2px solid rgba(255,255,255,0.3)' }}
              >
                <div style={{ fontSize: '2.5rem', marginBottom: '0.5rem' }}>{instrument?.emoji}</div>
                <div style={{ fontSize: '1rem', fontWeight: 'bold' }}>{instrument?.name}</div>
              </button>
            );
          })}
        </div>
        {activityData.showResult && (
          <div style={{ textAlign: 'center', marginTop: '2rem', padding: '1.5rem', backgroundColor: activityData.isCorrect ? 'rgba(76, 175, 80, 0.2)' : 'rgba(244, 67, 54, 0.2)', borderRadius: '12px' }}>
            <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>{activityData.isCorrect ? '✅' : '❌'}</div>
            <div style={{ fontSize: '1.2rem', fontWeight: 'bold', marginBottom: '1rem' }}>{activityData.isCorrect ? 'Correto!' : 'Tente novamente'}</div>
            <div style={{ fontSize: '1rem' }}>{activityData.feedback}</div>
          </div>
        )}
      </div>
    );
  };

  // 🧠 MEMÓRIA MUSICAL - Layout com sequência longa
  const renderMusicalMemory = () => (
    <div className={styles.activityInterface}>
      <div className={styles.instructionPanel}>
        <h3>🧠 Memória Musical</h3>
        <p>Memorize esta sequência longa de instrumentos e reproduza perfeitamente</p>
      </div>
      <div className={styles.memoryInterface}>
        <div className={styles.memoryChallenge}>
          <h4>🎵 Sequência para memorizar ({sequence.length} instrumentos):</h4>
          <div className={styles.memorySequence}>
            {sequence.map((instrumentId, index) => {
              const instrument = instruments.find(inst => inst.id === instrumentId);
              return (
                <div key={index} className={styles.memoryItem}>
                  <div className={styles.itemNumber}>{index + 1}</div>
                  <div className={styles.itemIcon}>{instrument?.emoji || instrumentId}</div>
                </div>
              );
            })}
          </div>
        </div>
        <div className={styles.memoryInput}>
          <h4>🎯 Reproduza a sequência:</h4>
          <div className={styles.instrumentsGrid}>
            {instruments.map((instrument) => (
              <button
                key={instrument.id}
                className={styles.instrumentButton}
                onClick={() => handleInstrumentClick(instrument.id)}
                disabled={isPlaying}
              >
                <span className={styles.instrumentIcon}>{instrument.emoji}</span>
                <span className={styles.instrumentName}>{instrument.name}</span>
              </button>
            ))}
          </div>
          <div className={styles.memoryProgress}>
            <p>Progresso: {playerSequence.length} / {sequence.length}</p>
            <div className={styles.progressBar}>
              <div className={styles.progressFill} style={{ width: `${(playerSequence.length / sequence.length) * 100}%` }}></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // 🎨 EXPRESSÃO CRIATIVA - Layout padrão LetterRecognition
  const renderCreativeExpression = () => {
    const activityData = gameState.activityData?.creativeExpression || {};
    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>🎨 Teste de Flexibilidade Cognitiva e Criatividade</h2>
          <p className={styles.questionSubtitle}>{activityData.prompt?.description || 'Expresse sua criatividade musical seguindo as diretrizes'}</p>
        </div>
        <div className={styles.objectsDisplay}>
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>🎼</div>
            <div style={{ fontSize: '1.2rem', marginBottom: '1rem', color: 'rgba(255,255,255,0.8)' }}>
              {activityData.prompt?.constraint || 'Criação Livre'}
            </div>
            <div style={{ fontSize: '1rem', color: 'rgba(255,255,255,0.6)' }}>
              Tamanho: {activityData.prompt?.minLength || 3}-{activityData.prompt?.maxLength || 8} notas
            </div>
            <div style={{ fontSize: '1rem', color: 'rgba(255,255,255,0.6)' }}>
              Atual: {activityData.userComposition?.length || 0} notas
            </div>
          </div>
        </div>
        <div className={styles.answerOptions}>
          {instruments.map((instrument) => (
            <button
              key={instrument.id}
              className={styles.answerButton}
              onClick={() => {
                const currentComposition = activityData.userComposition || [];
                if (currentComposition.length < (activityData.prompt?.maxLength || 8)) {
                  setGameState(prev => ({
                    ...prev,
                    activityData: {
                      ...prev.activityData,
                      creativeExpression: {
                        ...prev.activityData.creativeExpression,
                        userComposition: [...currentComposition, instrument.id]
                      }
                    }
                  }));
                  playSoundInstrument(instrument.id);
                }
              }}
              disabled={isPlaying || (activityData.userComposition?.length || 0) >= (activityData.prompt?.maxLength || 8)}
            >
              <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>{instrument.emoji}</div>
              <div style={{ fontSize: '0.9rem' }}>{instrument.name}</div>
            </button>
          ))}
        </div>
        <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', marginTop: '2rem' }}>
          <button className={styles.answerButton} onClick={() => playSequenceWithArray(activityData.userComposition || [])} disabled={isPlaying || !activityData.userComposition?.length}>
            ▶️ Tocar
          </button>
          <button className={styles.answerButton} onClick={clearCreativeComposition} disabled={isPlaying}>
            🗑️ Limpar
          </button>
          <button className={styles.answerButton} onClick={finishCreativeComposition} disabled={isPlaying || (activityData.userComposition?.length || 0) < (activityData.prompt?.minLength || 3)}>
            ✅ Finalizar
          </button>
        </div>
        {activityData.isFinished && activityData.evaluation && (
          <div style={{ textAlign: 'center', marginTop: '2rem', padding: '1.5rem', backgroundColor: 'rgba(76, 175, 80, 0.2)', borderRadius: '12px' }}>
            <div style={{ fontSize: '1.2rem', fontWeight: 'bold', marginBottom: '1rem' }}>🎨 Avaliação da Composição</div>
            <div style={{ fontSize: '1rem' }}>"{activityData.evaluation.feedback}"</div>
            <button className={styles.answerButton} onClick={() => generateNewRound()} style={{ marginTop: '1rem' }}>
              <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>➡️</div>
              <div style={{ fontSize: '0.9rem' }}>Próxima Atividade</div>
            </button>
          </div>
        )}
      </div>
    );
  };

  const handleBackToMenu = useCallback(() => {
    // Gerar relatório final e recomendações
    try {
      const gameEndData = {
        gameType: 'musical_sequence',
        sessionDuration: Date.now() - gameStartTime,
        finalScore: score,
        finalLevel: currentLevel,
        totalAttempts: totalAttempts,
        correctAttempts: correctAttempts,
        finalAccuracy: totalAttempts > 0 ? (correctAttempts / totalAttempts) * 100 : 0,
        maxStreak: streak,
        difficulty: difficulty,
        timestamp: Date.now()
      };

      MusicalSequenceMetrics.recordAdvancedInteraction(gameEndData);

      // Gerar relatório final dos coletores
      const collectorsHub = MusicalSequenceMetrics.getCollectorsHub();
      if (collectorsHub) {
        const finalReport = collectorsHub.generateFinalReport();
        const recommendations = collectorsHub.generateRecommendations();

        console.log('🎯 Relatório Final:', finalReport);
        console.log('🎯 Recomendações Geradas:', recommendations);

        // Enviar relatório final para o orquestrador
        if (orchestratorReady && sendMetrics) {
          sendMetrics({
            gameType: 'musical_sequence',
            sessionId: `musical_${gameStartTime}`,
            metrics: {
              ...gameEndData,
              finalReport,
              recommendations
            },
            timestamp: Date.now()
          });
        }

        // Finalizar sessão dos coletores
        collectorsHub.endSession();
      }

    } catch (error) {
      console.error('Erro ao gerar relatório final:', error);
    }

    // Voltar ao menu
    if (onBack) {
      onBack();
    }
  }, [gameStartTime, score, currentLevel, totalAttempts, correctAttempts, streak, difficulty, orchestratorReady, sendMetrics, onBack]);

  return (
    <div
      className={`${styles.musicalSequenceGame} ${settings.reducedMotion ? 'reduced-motion' : ''} ${settings.highContrast ? 'high-contrast' : ''}`}
      data-font-size={settings.fontSize}
      data-theme={settings.colorScheme}
      style={{
        fontSize: settings.fontSize === 'small' ? '0.875rem' :
                 settings.fontSize === 'large' ? '1.25rem' : '1rem'
      }}
    >
      {showStartScreen ? (
        <GameStartScreen
          gameTitle="🎵 Sequência Musical"
          gameDescription="Teste sua memória auditiva e habilidades musicais com diferentes atividades interativas"
          onStart={startGame}
          onBack={onBack}
        />
      ) : (
        <div className={styles.gameContent}>
          {/* Header do jogo - padrão LetterRecognition */}
          <div className={styles.gameHeader}>
            <h1 className={styles.gameTitle}>
              🎵 Sequência Musical
              <div style={{ fontSize: '0.7rem', opacity: 0.8, marginTop: '0.25rem' }}>
                {ACTIVITY_TYPES[gameState.currentActivity?.toUpperCase()]?.name || 'Reprodução de Sequência'}
              </div>
            </h1>
            <button
              className={`${styles.headerTtsButton} ${ttsActive ? styles.ttsActive : ''}`}
              onClick={toggleTTS}
              title={ttsActive ? 'Desativar TTS' : 'Ativar TTS'}
              aria-label={ttsActive ? 'Desativar narração' : 'Ativar narração'}
            >
              {ttsActive ? '🔊' : '🔇'}
            </button>
          </div>

          {/* Header com estatísticas - padrão LetterRecognition */}
          <div className={styles.gameStats}>
            <div className={styles.statCard}>
              <div className={styles.statValue}>{score}</div>
              <div className={styles.statLabel}>Pontos</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statValue}>{currentLevel}</div>
              <div className={styles.statLabel}>Nível</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statValue}>{streak}</div>
              <div className={styles.statLabel}>Sequência</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statValue}>{getAccuracy()}%</div>
              <div className={styles.statLabel}>Precisão</div>
            </div>
          </div>

          {/* Menu de atividades - padrão LetterRecognition */}
          <div className={styles.activityMenu}>
            {Object.values(ACTIVITY_TYPES).map((activity) => (
              <button
                key={activity.id}
                className={`${styles.activityButton} ${
                  gameState.currentActivity === activity.id ? styles.active : ''
                }`}
                onClick={() => changeActivity(activity.id)}
              >
                <span>{activity.icon}</span>
                <span>{activity.name}</span>
              </button>
            ))}
          </div>

          {/* Renderização da atividade atual - EXATO padrão LetterRecognition */}
          {gameState.currentActivity === ACTIVITY_TYPES.SEQUENCE_REPRODUCTION.id && renderSequenceReproduction()}
          {gameState.currentActivity === ACTIVITY_TYPES.RHYTHM_TIMING.id && renderRhythmTiming()}
          {gameState.currentActivity === ACTIVITY_TYPES.PITCH_DISCRIMINATION.id && renderPitchDiscrimination()}
          {gameState.currentActivity === ACTIVITY_TYPES.PATTERN_PREDICTION.id && renderPatternPrediction()}

          {/* Controles do jogo - padrão LetterRecognition */}
          <div className={styles.gameControls}>
            <button className={styles.controlButton} onClick={explainGame}>
              🔊 Explicar
            </button>
            <button className={styles.controlButton} onClick={playSequence}>
              🔄 Repetir Sequência
            </button>
            <button className={styles.controlButton} onClick={handleBackToMenu}>
              ⬅️ Voltar
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default MusicalSequenceGame;
