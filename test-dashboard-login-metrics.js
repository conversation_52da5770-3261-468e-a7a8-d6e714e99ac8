#!/usr/bin/env node

/**
 * @file test-dashboard-login-metrics.js
 * @description Teste completo do sistema de métricas condicionais
 * @version 1.0.0
 */

console.log('🧪 TESTE COMPLETO: Sistema de Métricas Condicionais');
console.log('=' .repeat(70));

// Simular localStorage para Node.js
global.localStorage = {
  data: {},
  getItem(key) {
    return this.data[key] || null;
  },
  setItem(key, value) {
    this.data[key] = value;
  },
  removeItem(key) {
    delete this.data[key];
  },
  clear() {
    this.data = {};
  }
};

// Simular console com cores
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️ ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️ ${msg}${colors.reset}`),
  test: (msg) => console.log(`${colors.cyan}🧪 ${msg}${colors.reset}`)
};

// Simular hook useDashboardAuth
function simulateUseDashboardAuth() {
  const checkDashboardLogin = () => {
    const checks = {
      authToken: localStorage.getItem('authToken'),
      userData: localStorage.getItem('userData'),
      adminToken: localStorage.getItem('admin_token'),
      adminAuth: localStorage.getItem('adminAuth'),
      portalAuth: localStorage.getItem('portalBetina_adminAuth'),
      betinaAdminSession: localStorage.getItem('betina_admin_session')
    };

    let hasValidLogin = false;
    let currentLoginType = null;

    // Verificar dashboard principal
    if (checks.authToken && checks.userData) {
      try {
        const userData = JSON.parse(checks.userData);
        if (userData.email) {
          hasValidLogin = true;
          currentLoginType = 'dashboard_user';
        }
      } catch (error) {
        // Dados inválidos
      }
    }

    // Verificar dashboard administrativo
    if (!hasValidLogin && (checks.adminToken || checks.adminAuth === 'true')) {
      hasValidLogin = true;
      currentLoginType = 'admin_dashboard';
    }

    // Verificar portal admin
    if (!hasValidLogin && checks.portalAuth === 'true') {
      hasValidLogin = true;
      currentLoginType = 'portal_admin';
    }

    // Verificar admin integrado
    if (!hasValidLogin && checks.betinaAdminSession) {
      try {
        const session = JSON.parse(checks.betinaAdminSession);
        if (session.user && session.timestamp) {
          hasValidLogin = true;
          currentLoginType = 'integrated_admin';
        }
      } catch (error) {
        // Sessão inválida
      }
    }

    return {
      isLoggedIn: hasValidLogin,
      loginType: currentLoginType,
      checks
    };
  };

  const shouldSaveMetrics = () => {
    const result = checkDashboardLogin();
    return result.isLoggedIn;
  };

  const getLoginInfo = () => {
    const result = checkDashboardLogin();
    return {
      isLoggedIn: result.isLoggedIn,
      loginType: result.loginType,
      userInfo: result.isLoggedIn ? { type: result.loginType } : null
    };
  };

  return { shouldSaveMetrics, getLoginInfo, checkDashboardLogin };
}

// Simular salvamento de métricas
function simulateMetricsSaving(dashboardAuth, isPremium = false) {
  const saveMetricsData = async (data) => {
    if (!dashboardAuth.shouldSaveMetrics()) {
      const loginInfo = dashboardAuth.getLoginInfo();
      console.log('🎮 Jogo gratuito - métricas não salvas (sem login no dashboard)', {
        hasLogin: loginInfo.isLoggedIn,
        loginType: loginInfo.loginType
      });
      return false;
    }

    const loginInfo = dashboardAuth.getLoginInfo();
    console.log('✅ Login ativo detectado - salvando métricas:', {
      loginType: loginInfo.loginType,
      userInfo: loginInfo.userInfo
    });

    if (isPremium) {
      console.log('👑 Dashboard logado + Premium: salvando dados remotamente');
      return true;
    } else {
      console.log('🔐 Dashboard logado: salvando dados localmente');
      return true;
    }
  };

  return { saveMetricsData };
}

// Executar testes
async function runTests() {
  console.log('\n🚀 Iniciando testes do sistema...\n');

  const dashboardAuth = simulateUseDashboardAuth();
  let testsPassed = 0;
  let totalTests = 0;

  // Teste 1: Sem login - métricas não devem ser salvas
  console.log('📋 TESTE 1: Jogo sem login no dashboard');
  console.log('-'.repeat(50));
  
  localStorage.clear();
  const metrics1 = simulateMetricsSaving(dashboardAuth, false);
  const result1 = await metrics1.saveMetricsData({ test: 'data' });
  
  totalTests++;
  if (result1 === false) {
    log.success('Métricas não foram salvas (correto)');
    testsPassed++;
  } else {
    log.error('Métricas foram salvas incorretamente');
  }

  // Teste 2: Login de usuário - métricas devem ser salvas localmente
  console.log('\n📋 TESTE 2: Login de usuário no dashboard');
  console.log('-'.repeat(50));
  
  localStorage.setItem('authToken', 'user_token_123');
  localStorage.setItem('userData', JSON.stringify({ email: '<EMAIL>', name: 'Usuário Teste' }));
  
  const metrics2 = simulateMetricsSaving(dashboardAuth, false);
  const result2 = await metrics2.saveMetricsData({ test: 'data' });
  
  totalTests++;
  if (result2 === true) {
    log.success('Métricas foram salvas localmente (correto)');
    testsPassed++;
  } else {
    log.error('Métricas não foram salvas');
  }

  // Teste 3: Login de usuário premium - métricas devem ser salvas remotamente
  console.log('\n📋 TESTE 3: Login de usuário premium no dashboard');
  console.log('-'.repeat(50));
  
  const metrics3 = simulateMetricsSaving(dashboardAuth, true);
  const result3 = await metrics3.saveMetricsData({ test: 'data' });
  
  totalTests++;
  if (result3 === true) {
    log.success('Métricas foram salvas remotamente (correto)');
    testsPassed++;
  } else {
    log.error('Métricas não foram salvas');
  }

  // Teste 4: Login administrativo - métricas devem ser salvas
  console.log('\n📋 TESTE 4: Login administrativo no dashboard');
  console.log('-'.repeat(50));
  
  localStorage.clear();
  localStorage.setItem('adminAuth', 'true');
  localStorage.setItem('adminLoginTime', new Date().toISOString());
  
  const metrics4 = simulateMetricsSaving(dashboardAuth, false);
  const result4 = await metrics4.saveMetricsData({ test: 'data' });
  
  totalTests++;
  if (result4 === true) {
    log.success('Métricas foram salvas com login admin (correto)');
    testsPassed++;
  } else {
    log.error('Métricas não foram salvas');
  }

  // Teste 5: Portal admin - métricas devem ser salvas
  console.log('\n📋 TESTE 5: Login no portal administrativo');
  console.log('-'.repeat(50));
  
  localStorage.clear();
  localStorage.setItem('portalBetina_adminAuth', 'true');
  
  const metrics5 = simulateMetricsSaving(dashboardAuth, false);
  const result5 = await metrics5.saveMetricsData({ test: 'data' });
  
  totalTests++;
  if (result5 === true) {
    log.success('Métricas foram salvas com portal admin (correto)');
    testsPassed++;
  } else {
    log.error('Métricas não foram salvas');
  }

  // Teste 6: Admin integrado - métricas devem ser salvas
  console.log('\n📋 TESTE 6: Login no admin integrado');
  console.log('-'.repeat(50));
  
  localStorage.clear();
  localStorage.setItem('betina_admin_session', JSON.stringify({
    user: 'admin',
    timestamp: new Date().toISOString(),
    permissions: ['dashboard_integrated']
  }));
  
  const metrics6 = simulateMetricsSaving(dashboardAuth, false);
  const result6 = await metrics6.saveMetricsData({ test: 'data' });
  
  totalTests++;
  if (result6 === true) {
    log.success('Métricas foram salvas com admin integrado (correto)');
    testsPassed++;
  } else {
    log.error('Métricas não foram salvas');
  }

  // Resultados finais
  console.log('\n' + '='.repeat(70));
  console.log('📊 RESULTADOS FINAIS DOS TESTES');
  console.log('='.repeat(70));
  
  console.log(`📈 Testes executados: ${totalTests}`);
  console.log(`✅ Testes aprovados: ${testsPassed}`);
  console.log(`❌ Testes falharam: ${totalTests - testsPassed}`);
  
  const successRate = ((testsPassed / totalTests) * 100).toFixed(1);
  console.log(`🎯 Taxa de sucesso: ${successRate}%`);
  
  if (successRate >= 100) {
    log.success('SISTEMA FUNCIONANDO PERFEITAMENTE!');
    console.log('🎉 O sistema de métricas condicionais está implementado corretamente!');
  } else if (successRate >= 80) {
    log.warning('SISTEMA FUNCIONANDO COM PEQUENOS AJUSTES NECESSÁRIOS');
  } else {
    log.error('SISTEMA PRECISA DE CORREÇÕES SIGNIFICATIVAS');
  }

  console.log('\n✅ TESTE COMPLETO FINALIZADO!');
}

// Executar testes
runTests().catch(console.error);
