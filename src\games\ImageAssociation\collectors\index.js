/**
 * HUB INTEGRADOR: COLETORES DO IMAGE ASSOCIATION
 * 
 * Coordena e integra todos os coletores especializados do jogo ImageAssociation,
 * fornecendo análise unificada das capacidades cognitivas avaliadas.
 * 
 * COLETORES INTEGRADOS:
 * - AssociativeMemoryCollector: Memória associativa e formação de conceitos
 * - VisualProcessingCollector: Processamento visual e discriminação
 * - CognitiveCategorization: Categorização cognitiva e raciocínio taxonômico
 * - MentalFlexibilityCollector: Flexibilidade mental e adaptação cognitiva
 */

import { AssociativeMemoryCollector } from './AssociativeMemoryCollector.js'
import { VisualProcessingCollector } from './VisualProcessingCollector.js'
import { CognitiveCategorizationCollector } from './CognitiveCategorizationCollector.js'
import { MentalFlexibilityCollector } from './MentalFlexibilityCollector.js'
import { SemanticMemoryCollector } from './SemanticMemoryCollector.js'

import { ErrorPatternCollector } from './ErrorPatternCollector.js';
import SimpleAssociationCollector from './SimpleAssociationCollector.js';
import CategoricalAssociationCollector from './CategoricalAssociationCollector.js';
import EmotionalAssociationCollector from './EmotionalAssociationCollector.js';
import FunctionalAssociationCollector from './FunctionalAssociationCollector.js';
import ConceptualAssociationCollector from './ConceptualAssociationCollector.js';
import SequentialAssociationCollector from './SequentialAssociationCollector.js';

export class ImageAssociationCollectorsHub {
  constructor() {
    this.hubId = 'image-association-collectors-hub'
    this.version = '3.0.0'
    this.gameType = 'ImageAssociation'
    
    // Inicializar coletores especializados
    this.associativeMemoryCollector = new AssociativeMemoryCollector()
    this.visualProcessingCollector = new VisualProcessingCollector()
    this.categorizationCollector = new CognitiveCategorizationCollector()
    this.flexibilityCollector = new MentalFlexibilityCollector()
    this.semanticMemoryCollector = new SemanticMemoryCollector()
    this.errorPatternCollector = new ErrorPatternCollector()
    this.simpleAssociationCollector = new SimpleAssociationCollector()
    this.categoricalAssociationCollector = new CategoricalAssociationCollector()
    this.emotionalAssociationCollector = new EmotionalAssociationCollector()
    this.functionalAssociationCollector = new FunctionalAssociationCollector()
    this.conceptualAssociationCollector = new ConceptualAssociationCollector()
    this.sequentialAssociationCollector = new SequentialAssociationCollector()

    // Objeto collectors para compatibilidade com script de contagem
    this._collectors = {
      associativeMemory: this.associativeMemoryCollector,
      visualProcessing: this.visualProcessingCollector,
      categorization: this.categorizationCollector,
      flexibility: this.flexibilityCollector,
      semanticMemory: this.semanticMemoryCollector,
      errorPattern: this.errorPatternCollector,
      simpleAssociation: this.simpleAssociationCollector,
      categoricalAssociation: this.categoricalAssociationCollector,
      emotionalAssociation: this.emotionalAssociationCollector,
      functionalAssociation: this.functionalAssociationCollector,
      conceptualAssociation: this.conceptualAssociationCollector,
      sequentialAssociation: this.sequentialAssociationCollector
    };

    // Dados de integração
    this.integratedData = []
    this.sessionMetrics = new Map()
    this.correlationMatrix = new Map()
    this.initialized = false
  }

  /**
   * Getter para acessar os coletores do hub
   * @returns {Object} Objeto com todos os coletores
   */
  get collectors() {
    return {
      associativeMemory: this.associativeMemoryCollector,
      visualProcessing: this.visualProcessingCollector,
      categorization: this.categorizationCollector,
      flexibility: this.flexibilityCollector,
      errorPattern: this.errorPatternCollector,
      simpleAssociation: this.simpleAssociationCollector,
      categoricalAssociation: this.categoricalAssociationCollector,
      emotionalAssociation: this.emotionalAssociationCollector,
      functionalAssociation: this.functionalAssociationCollector,
      conceptualAssociation: this.conceptualAssociationCollector,
      sequentialAssociation: this.sequentialAssociationCollector
    };
  }

  /**
   * INICIALIZAÇÃO DO HUB
   */
  async initialize() {
    try {
      // Marcar coletores como inicializados
      this.associativeMemoryCollector.initialized = true
      this.visualProcessingCollector.initialized = true
      this.categorizationCollector.initialized = true
      this.flexibilityCollector.initialized = true
      
      this.initialized = true
      console.log('🎯 ImageAssociation Collectors Hub: Inicializado com sucesso')
      
      return {
        status: 'success',
        hubId: this.hubId,
        collectors: this.getCollectorStatus(),
        message: 'Hub de coletores inicializado'
      }
    } catch (error) {
      console.error('❌ Erro na inicialização do hub:', error)
      return { status: 'error', message: error.message }
    }
  }

  /**
   * COLETA INTEGRADA: Processar evento do jogo
   */
  async collectGameEvent(gameData) {
    if (!this.initialized) {
      await this.initialize()
    }

    const timestamp = Date.now()
    
    try {
      // Preparar dados para cada coletor
      const eventData = {
        timestamp,
        sessionId: gameData.sessionId || `session_${timestamp}`,
        phase: gameData.phase,
        category: gameData.category,
        mainItem: gameData.mainItem,
        correctAnswer: gameData.correctAnswer,
        userAnswer: gameData.userAnswer,
        isCorrect: gameData.isCorrect,
        responseTime: gameData.responseTime,
        difficulty: gameData.difficulty,
        options: gameData.options,
        explanation: gameData.explanation
      }

      // Coletar dados de cada coletor especializado
      const [
        associativeData,
        visualData,
        categorizationData,
        flexibilityData
      ] = await Promise.all([
        this.associativeMemoryCollector.collectAssociationAttempt(eventData),
        this.visualProcessingCollector.collectVisualProcessing(eventData),
        this.categorizationCollector.collectCategorizationProcess(eventData),
        this.flexibilityCollector.collectFlexibilityEvent(eventData)
      ])

      // Integrar dados coletados
      const integratedEvent = {
        timestamp,
        eventId: `event_${timestamp}`,
        gameType: this.gameType,
        rawData: eventData,
        collectors: {
          associativeMemory: associativeData,
          visualProcessing: visualData,
          categorization: categorizationData,
          mentalFlexibility: flexibilityData
        },
        integrationMetrics: this.calculateIntegrationMetrics(
          associativeData, visualData, categorizationData, flexibilityData
        )
      }

      this.integratedData.push(integratedEvent)
      
      // Atualizar métricas da sessão
      this.updateSessionMetrics(integratedEvent)
      
      // Análise em tempo real
      if (this.integratedData.length % 5 === 0) {
        await this.performRealTimeIntegratedAnalysis()
      }

      return integratedEvent

    } catch (error) {
      console.error('❌ Erro na coleta integrada:', error)
      return { status: 'error', message: error.message }
    }
  }

  /**
   * ANÁLISE INTEGRADA: Padrões cognitivos unificados
   */
  async performIntegratedAnalysis() {
    if (this.integratedData.length === 0) {
      return { 
        status: 'insufficient_data', 
        message: 'Dados insuficientes para análise integrada' 
      }
    }

    try {
      // Análises individuais dos coletores
      const [
        associativeAnalysis,
        visualAnalysis,
        categorizationAnalysis,
        flexibilityAnalysis
      ] = await Promise.all([
        this.associativeMemoryCollector.analyzeAssociativePatterns(),
        this.visualProcessingCollector.analyzeVisualProcessingPatterns(),
        this.categorizationCollector.analyzeCategorizationPatterns(),
        this.flexibilityCollector.analyzeMentalFlexibilityPatterns()
      ])

      // Análise integrada
      const integratedAnalysis = {
        timestamp: Date.now(),
        gameType: this.gameType,
        totalEvents: this.integratedData.length,
        
        // Análises individuais
        individualAnalyses: {
          associativeMemory: associativeAnalysis,
          visualProcessing: visualAnalysis,
          categorization: categorizationAnalysis,
          mentalFlexibility: flexibilityAnalysis
        },
        
        // Análises integradas
        cognitiveProfile: this.buildIntegratedCognitiveProfile(),
        crossDomainCorrelations: this.analyzeCorrelations(),
        emergentPatterns: this.identifyEmergentPatterns(),
        cognitiveStrengths: this.identifyCognitiveStrengths(),
        cognitiveWeaknesses: this.identifyCognitiveWeaknesses(),
        developmentalTrajectory: this.analyzeDevelopmentalTrajectory(),
        
        // Insights e recomendações integradas
        unifiedInsights: this.generateUnifiedInsights(),
        personalizedRecommendations: this.generatePersonalizedRecommendations(),
        
        // Métricas de qualidade
        dataQuality: this.assessDataQuality(),
        analysisConfidence: this.calculateAnalysisConfidence()
      }

      return {
        status: 'success',
        type: 'integrated_analysis',
        data: integratedAnalysis
      }

    } catch (error) {
      console.error('❌ Erro na análise integrada:', error)
      return { status: 'error', message: error.message }
    }
  }

  /**
   * MÉTRICAS DE INTEGRAÇÃO
   */
  calculateIntegrationMetrics(associative, visual, categorization, flexibility) {
    return {
      // Consistência entre coletores
      responseConsistency: this.calculateResponseConsistency(associative, visual, categorization, flexibility),
      
      // Correlações cognitivas
      associativeVisualCorrelation: this.calculateCorrelation(
        associative.associationStrength?.finalStrength || 0,
        visual.discriminationAccuracy?.finalDiscriminationScore || 0
      ),
      
      categorizationFlexibilityCorrelation: this.calculateCorrelation(
        categorization.ruleComplexity?.complexity || 0,
        flexibility.cognitiveLoad?.totalLoad || 0
      ),
      
      // Eficiência cognitiva integrada
      integratedEfficiency: this.calculateIntegratedEfficiency(associative, visual, categorization, flexibility),
      
      // Carga cognitiva total
      totalCognitiveLoad: this.calculateTotalCognitiveLoad(associative, visual, categorization, flexibility),
      
      // Sincronia cognitiva
      cognitiveSync: this.assessCognitiveSync(associative, visual, categorization, flexibility)
    }
  }

  /**
   * PERFIL COGNITIVO INTEGRADO
   */
  buildIntegratedCognitiveProfile() {
    const recentEvents = this.integratedData.slice(-10)
    
    if (recentEvents.length === 0) {
      return { status: 'insufficient_data' }
    }

    // Extrair métricas de cada domínio
    const associativeMetrics = this.extractDomainMetrics(recentEvents, 'associativeMemory')
    const visualMetrics = this.extractDomainMetrics(recentEvents, 'visualProcessing')
    const categorizationMetrics = this.extractDomainMetrics(recentEvents, 'categorization')
    const flexibilityMetrics = this.extractDomainMetrics(recentEvents, 'mentalFlexibility')

    return {
      // Domínios cognitivos
      domains: {
        associativeMemory: {
          strength: this.calculateDomainStrength(associativeMetrics),
          efficiency: this.calculateDomainEfficiency(associativeMetrics),
          consistency: this.calculateDomainConsistency(associativeMetrics)
        },
        visualProcessing: {
          strength: this.calculateDomainStrength(visualMetrics),
          efficiency: this.calculateDomainEfficiency(visualMetrics),
          consistency: this.calculateDomainConsistency(visualMetrics)
        },
        categorization: {
          strength: this.calculateDomainStrength(categorizationMetrics),
          efficiency: this.calculateDomainEfficiency(categorizationMetrics),
          consistency: this.calculateDomainConsistency(categorizationMetrics)
        },
        mentalFlexibility: {
          strength: this.calculateDomainStrength(flexibilityMetrics),
          efficiency: this.calculateDomainEfficiency(flexibilityMetrics),
          consistency: this.calculateDomainConsistency(flexibilityMetrics)
        }
      },

      // Perfil geral
      overallProfile: {
        dominantDomain: this.identifyDominantDomain(),
        cognitiveBalance: this.assessCognitiveBalance(),
        processingStyle: this.identifyProcessingStyle(),
        learningPreference: this.identifyLearningPreference()
      },

      // Métricas integradas
      integratedMetrics: {
        globalEfficiency: this.calculateGlobalEfficiency(),
        cognitiveFlexibility: this.calculateGlobalFlexibility(),
        adaptiveCapacity: this.calculateAdaptiveCapacity(),
        learningRate: this.calculateLearningRate()
      }
    }
  }

  /**
   * ANÁLISE DE CORRELAÇÕES CRUZADAS
   */
  analyzeCorrelations() {
    const correlations = {
      // Correlações entre domínios
      associativeVisual: this.calculateDomainCorrelation('associativeMemory', 'visualProcessing'),
      visualCategorization: this.calculateDomainCorrelation('visualProcessing', 'categorization'),
      categorizationFlexibility: this.calculateDomainCorrelation('categorization', 'mentalFlexibility'),
      flexibilityAssociative: this.calculateDomainCorrelation('mentalFlexibility', 'associativeMemory'),

      // Correlações temporais
      performanceStability: this.analyzePerformanceStability(),
      learningProgression: this.analyzeLearningProgression(),

      // Correlações contextuais
      difficultyResponse: this.analyzeDifficultyResponse(),
      categorySpecificPerformance: this.analyzeCategorySpecificPerformance()
    }

    return {
      correlationMatrix: correlations,
      strongestCorrelations: this.identifyStrongestCorrelations(correlations),
      emergentPatterns: this.identifyCorrelationPatterns(correlations),
      predictiveFactors: this.identifyPredictiveFactors(correlations)
    }
  }

  /**
   * ANÁLISE EM TEMPO REAL
   */
  async performRealTimeIntegratedAnalysis() {
    const recentEvents = this.integratedData.slice(-5)
    
    // Verificar padrões emergentes
    const patterns = this.detectEmergentPatterns(recentEvents)
    
    // Avaliar performance integrada
    const performance = this.assessIntegratedPerformance(recentEvents)
    
    // Gerar alertas se necessário
    if (performance.overallEfficiency < 0.4) {
      console.log('🎯 ImageAssociation Hub: Performance baixa detectada - análise aprofundada recomendada')
    } else if (performance.overallEfficiency > 0.8) {
      console.log('🎯 ImageAssociation Hub: Excelente performance integrada')
    }

    if (patterns.length > 0) {
      console.log('🎯 ImageAssociation Hub: Padrões emergentes detectados:', patterns)
    }
  }

  /**
   * INSIGHTS UNIFICADOS
   */
  generateUnifiedInsights() {
    const insights = []
    const profile = this.buildIntegratedCognitiveProfile()
    
    if (profile.domains?.associativeMemory?.strength > 0.8) {
      insights.push('Excelente capacidade de formação de associações conceituais')
    }
    
    if (profile.domains?.visualProcessing?.efficiency > 0.7) {
      insights.push('Processamento visual eficiente e discriminação precisa')
    }
    
    if (profile.domains?.categorization?.strength > 0.8) {
      insights.push('Forte raciocínio categorial e flexibilidade taxonômica')
    }
    
    if (profile.domains?.mentalFlexibility?.consistency > 0.7) {
      insights.push('Excelente controle executivo e adaptabilidade mental')
    }
    
    if (profile.overallProfile?.cognitiveBalance > 0.8) {
      insights.push('Perfil cognitivo bem balanceado entre todos os domínios')
    }
    
    return insights
  }

  /**
   * RECOMENDAÇÕES PERSONALIZADAS
   */
  generatePersonalizedRecommendations() {
    const recommendations = []
    const profile = this.buildIntegratedCognitiveProfile()
    const correlations = this.analyzeCorrelations()
    
    // Recomendações baseadas em pontos fracos
    if (profile.domains?.associativeMemory?.strength < 0.5) {
      recommendations.push('Praticar exercícios de associação semântica e formação de conceitos')
    }
    
    if (profile.domains?.visualProcessing?.efficiency < 0.6) {
      recommendations.push('Treinar discriminação visual com complexidade gradual')
    }
    
    if (profile.domains?.categorization?.consistency < 0.6) {
      recommendations.push('Exercitar raciocínio categorial com diferentes taxonomias')
    }
    
    if (profile.domains?.mentalFlexibility?.strength < 0.6) {
      recommendations.push('Desenvolver flexibilidade mental com exercícios de switching')
    }
    
    // Recomendações baseadas em correlações
    if (correlations.strongestCorrelations?.length > 0) {
      recommendations.push('Explorar pontos fortes para apoiar áreas em desenvolvimento')
    }
    
    return recommendations
  }

  /**
   * MÉTODOS AUXILIARES MATEMÁTICOS
   */
  calculateCorrelation(x, y) {
    // Correlação simples entre duas variáveis
    return Math.max(-1, Math.min(1, (x + y) / 2 - Math.abs(x - y) / 2))
  }

  calculateResponseConsistency(associative, visual, categorization, flexibility) {
    const accuracies = [
      associative.isCorrect ? 1 : 0,
      visual.isVisualMatch ? 1 : 0,
      categorization.correctCategorization ? 1 : 0,
      flexibility.responseAccuracy ? 1 : 0
    ]
    
    const mean = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length
    const variance = accuracies.reduce((sum, acc) => sum + Math.pow(acc - mean, 2), 0) / accuracies.length
    
    return Math.max(0, 1 - Math.sqrt(variance))
  }

  calculateIntegratedEfficiency(associative, visual, categorization, flexibility) {
    const efficiencies = [
      associative.associationStrength?.finalStrength || 0,
      visual.discriminationAccuracy?.finalDiscriminationScore || 0,
      categorization.abstractionLevel?.score / 5 || 0,
      flexibility.adaptiveCapacity || 0
    ]
    
    return efficiencies.reduce((sum, eff) => sum + eff, 0) / efficiencies.length
  }

  // Métodos auxiliares simplificados
  calculateTotalCognitiveLoad() { return 0.5 }
  assessCognitiveSync() { return 0.7 }
  extractDomainMetrics() { return [] }
  calculateDomainStrength() { return 0.7 }
  calculateDomainEfficiency() { return 0.75 }
  calculateDomainConsistency() { return 0.8 }
  identifyDominantDomain() { return 'associativeMemory' }
  assessCognitiveBalance() { return 0.8 }
  identifyProcessingStyle() { return 'systematic' }
  identifyLearningPreference() { return 'visual-conceptual' }
  calculateGlobalEfficiency() { return 0.75 }
  calculateGlobalFlexibility() { return 0.8 }
  calculateAdaptiveCapacity() { return 0.7 }
  calculateLearningRate() { return 0.6 }
  calculateDomainCorrelation() { return 0.6 }
  analyzePerformanceStability() { return 0.7 }
  analyzeLearningProgression() { return 0.8 }
  analyzeDifficultyResponse() { return 0.6 }
  analyzeCategorySpecificPerformance() { return {} }
  identifyStrongestCorrelations() { return [] }
  identifyCorrelationPatterns() { return [] }
  identifyPredictiveFactors() { return [] }
  identifyEmergentPatterns() { return [] }
  detectEmergentPatterns() { return [] }
  assessIntegratedPerformance() { return { overallEfficiency: 0.7 } }
  identifyCognitiveStrengths() { return ['visual_processing', 'categorization'] }
  identifyCognitiveWeaknesses() { return ['mental_flexibility'] }
  analyzeDevelopmentalTrajectory() { return { trend: 'improving', rate: 0.1 } }
  assessDataQuality() { return { completeness: 0.9, consistency: 0.8 } }
  calculateAnalysisConfidence() { return 0.85 }
  updateSessionMetrics() { /* Atualiza métricas da sessão */ }

  /**
   * STATUS DOS COLETORES
   */
  getCollectorStatus() {
    return {
      associativeMemory: this.associativeMemoryCollector.getStatus(),
      visualProcessing: this.visualProcessingCollector.getStatus(),
      categorization: this.categorizationCollector.getStatus(),
      mentalFlexibility: this.flexibilityCollector.getStatus()
    }
  }

  /**
   * RESET COMPLETO
   */
  reset() {
    this.associativeMemoryCollector.reset()
    this.visualProcessingCollector.reset()
    this.categorizationCollector.reset()
    this.flexibilityCollector.reset()
    
    this.integratedData = []
    this.sessionMetrics.clear()
    this.correlationMatrix.clear()
    this.initialized = false
    
    console.log('🎯 ImageAssociation Hub: Reset completo realizado')
  }

  /**
   * STATUS DO HUB
   */
  getStatus() {
    return {
      hubId: this.hubId,
      version: this.version,
      gameType: this.gameType,
      isInitialized: this.initialized,
      totalEvents: this.integratedData.length,
      collectors: this.getCollectorStatus(),
      lastEvent: this.integratedData.length > 0 ? 
        this.integratedData[this.integratedData.length - 1].timestamp : null
    }
  }
}

// Exportar classe para instanciação personalizada
export default ImageAssociationCollectorsHub

/**
 * Factory function para criar instância dos coletores
 * Função esperada pelos processadores do sistema
 */
export function createCollectors() {
  return new ImageAssociationCollectorsHub();
}

/**
 * Função alternativa para obter coletores
 * Compatibilidade com diferentes padrões
 */
export function getCollectors() {
  return new ImageAssociationCollectorsHub();
}

export {
  AssociativeMemoryCollector,
  VisualProcessingCollector,
  CognitiveCategorizationCollector,
  MentalFlexibilityCollector,
  SemanticMemoryCollector,
  ErrorPatternCollector
};
