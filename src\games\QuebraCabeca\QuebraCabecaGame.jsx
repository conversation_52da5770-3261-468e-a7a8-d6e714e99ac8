/**
 * 🧩 QUEBRA-CABEÇA V3 - JOGO DE QUEBRA-CABEÇA COM MÚLTIPLAS ATIVIDADES
 * Portal Betina V3 - Jogo educativo com 6 atividades diversificadas
 */

import React, { useState, useEffect, useContext, useCallback, useRef } from 'react'
import { QuebraCabecaConfig, QuebraCabecaV3Config } from './QuebraCabecaConfig'
import { QuebraCabecaMetrics } from './QuebraCabecaMetrics'
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic'
import { useAccessibilityContext } from '../../components/context/AccessibilityContext'
import { SystemContext } from '../../components/context/SystemContext.jsx'
import { useGameAudio } from '../../games/shared/GameUtils'
import { v4 as uuidv4 } from 'uuid';

// Importa o componente padrão de tela de dificuldade
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';

// 🧩 Importar coletores avançados V3
import { QuebraCabecaCollectorsHub } from './collectors/index.js'
// 🔄 Importar hook multissensorial
import { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js';
// 🎯 Importar hook orquestrador terapêutico
import { useTherapeuticOrchestrator } from '../../hooks/useTherapeuticOrchestrator.js';

// 🎨 Importar estilos V3
import styles from './QuebraCabeca.module.css';

// 🎯 SISTEMA DE ATIVIDADES REDESENHADO V3 - QUEBRA-CABEÇA EMOCIONAL
// Cada atividade testa diferentes funções cognitivas com layouts únicos
const ACTIVITY_TYPES = {
  EMOTION_PUZZLE: {
    id: 'emotion_puzzle',
    name: 'Quebra-Cabeça Emocional',
    icon: '🎯',
    description: 'Teste de coordenação motora e montagem de emoções',
    cognitiveFunction: 'motor_coordination_emotion_assembly',
    component: 'EmotionPuzzleActivity'
  },
  PIECE_ROTATION: {
    id: 'piece_rotation',
    name: 'Rotação de Peças',
    icon: '🔄',
    description: 'Teste de rotação mental e orientação espacial',
    cognitiveFunction: 'spatial_rotation_mental_processing',
    component: 'PieceRotationActivity'
  },
  PATTERN_PUZZLE: {
    id: 'pattern_puzzle',
    name: 'Quebra-Cabeça de Padrões',
    icon: '🔍',
    description: 'Teste de reconhecimento de padrões em quebra-cabeças',
    cognitiveFunction: 'pattern_recognition_spatial',
    component: 'PatternPuzzleActivity'
  },
  EMOTION_MATCHING: {
    id: 'emotion_matching',
    name: 'Correspondência Emocional',
    icon: '😊',
    description: 'Teste de correspondência entre peças emocionais',
    cognitiveFunction: 'emotional_matching_recognition',
    component: 'EmotionMatchingActivity'
  },
  CREATIVE_ASSEMBLY: {
    id: 'creative_assembly',
    name: 'Montagem Criativa',
    icon: '🎨',
    description: 'Teste de criatividade e montagem livre',
    cognitiveFunction: 'creative_spatial_assembly',
    component: 'CreativeAssemblyActivity'
  }
};

function QuebraCabecaGame({ onBack }) {
  const { user, ttsEnabled = true } = useContext(SystemContext);
  const { settings } = useAccessibilityContext();
  const { playSound } = useGameAudio();

  // Referência para métricas
  const metricsRef = useRef(null);

  // 🎯 ESTADO EXPANDIDO COM SISTEMA DE ATIVIDADES V3
  const [gameState, setGameState] = useState({
    status: 'start', // 'start', 'playing', 'paused', 'finished'
    score: 0,
    round: 1,
    totalRounds: 10,
    difficulty: 'EASY',
    accuracy: 100,
    roundStartTime: null,

    // 🎯 Sistema de atividades redesenhado (5 atividades distintas)
    currentActivity: ACTIVITY_TYPES.EMOTION_PUZZLE.id,
    activityCycle: [
      ACTIVITY_TYPES.EMOTION_PUZZLE.id,
      ACTIVITY_TYPES.PIECE_ROTATION.id,
      ACTIVITY_TYPES.PATTERN_PUZZLE.id,
      ACTIVITY_TYPES.EMOTION_MATCHING.id,
      ACTIVITY_TYPES.CREATIVE_ASSEMBLY.id
    ],
    activityIndex: 0,
    roundsPerActivity: 4,
    activityRoundCount: 0,
    activitiesCompleted: [],

    // 🎯 Dados específicos de atividades
    activityData: {
      freeAssembly: {
        puzzlePieces: [],
        placedPieces: [],
        completedPuzzles: 0
      },
      guidedAssembly: {
        currentHint: null,
        hintsUsed: 0,
        guidanceLevel: 'basic'
      },
      patternMatching: {
        patterns: [],
        matchedPatterns: [],
        currentPattern: null
      },
      shapeSorting: {
        shapes: [],
        sortedShapes: {},
        categories: []
      },
      timedChallenge: {
        timeLimit: 60,
        timeRemaining: 60,
        isTimerActive: false
      },
      creativeBuilding: {
        availablePieces: [],
        userCreation: [],
        savedCreations: []
      }
    },

    // 🎯 Feedback e animações
    showFeedback: false,
    feedbackType: null,
    feedbackMessage: '',
    showCelebration: false,

    // 🎯 Métricas comportamentais
    responseTime: 0,
    hesitationCount: 0,
    helpUsed: false,
    consecutiveCorrect: 0,
    totalAttempts: 0,
    correctAttempts: 0,
    
    // Peças e controle do jogo
    availablePieces: [],
    draggedPiece: null,
    isComplete: false,
    
    // Feedback e interação
    feedback: null,
    
    // Estatísticas gerais
    gameStats: {
      score: 0,
      completed: 0,
      totalAttempts: 0,
      accuracy: 100,
      sessionStartTime: null,
      roundStartTime: null
    },
    
    // 🔥 Configurações especiais
    specialConfig: {
      mentalRotationTime: [],
      spatialErrors: [],
      pieceClassification: {
        categoryAccuracy: {},
        sortingStrategy: 'color_first',
        classificationTime: []
      },
      patternIdentification: {
        patternRecognitionSpeed: [],
        logicalAccuracy: [],
        predictionSuccess: []
      },
      collaborativeSolving: {
        cooperationIndex: 0,
        communicationTurns: 0,
        leadershipEvents: []
      }
    },
    
    // Métricas comportamentais V3
    behavioralMetrics: {
      reactionTime: [],
      accuracy: [],
      attentionSpan: 0,
      frustrationEvents: [],
      persistenceLevel: 0,
      engagementScore: 100,
      multisensoryProcessing: {},
      activitySpecific: {}
    }
  });

  // ✅ REFS PARA CONTROLE DE SESSÃO
  const sessionIdRef = useRef(null);
  const roundStartTimeRef = useRef(null);
  
  // 🧠 Integração com sistema unificado de métricas
  const { 
    collectMetrics, 
    processGameSession, 
    initializeSession: initUnifiedSession,
    processAdvancedMetrics, // Novo: para AdvancedMetricsEngine
    sessionId,
    isSessionActive
  } = useUnifiedGameLogic('QuebraCabeca')

  // 🧩 Inicializar coletores avançados V3
  const [collectorsHub] = useState(() => new QuebraCabecaCollectorsHub())

  // TTS control
  const [ttsActive, setTtsActive] = useState(() => {
    const saved = localStorage.getItem('quebraCabeca_ttsActive');
    return saved ? JSON.parse(saved) : true;
  });

  // 🔄 Hook multissensorial integrado
  const {
    initializeSession: initMultisensory,
    recordInteraction: recordMultisensoryInteraction,
    finalizeSession: finalizeMultisensory,
    updateData: updateMultisensoryData,
    multisensoryData,
    isInitialized: multisensoryInitialized
  } = useMultisensoryIntegration(sessionId, {
    gameType: 'puzzle-game-v3',
    sensorTypes: {
      visual: true,
      haptic: true,
      tts: ttsEnabled,
      gestural: true,
      biometric: true
    },
    adaptiveMode: true,
    autoUpdate: true,
    enablePatternAnalysis: true,
    logLevel: 'info',
    learningStyle: user?.profile?.learningStyle || 'visual'
  });

  // 🎯 Hook orquestrador terapêutico integrado
  const therapeuticOrchestrator = useTherapeuticOrchestrator({ 
    gameType: 'puzzle-game',
    collectorsHub,
    recordMultisensoryInteraction,
    autoUpdate: true,
    logLevel: 'info'
  });

  const [cognitiveAnalysisVisible, setCognitiveAnalysisVisible] = useState(false)
  const [gameStarted, setGameStarted] = useState(false)
  const [showStartScreen, setShowStartScreen] = useState(true)
  const [gameStats, setGameStats] = useState({
    level: 1,
    score: 0,
    completed: 0,
    totalAttempts: 0,
    accuracy: 100
  })
  const [difficulty, setDifficulty] = useState('easy')
  const [draggedPiece, setDraggedPiece] = useState(null)
  const [placedPieces, setPlacedPieces] = useState([])
  const [puzzlePieces, setPuzzlePieces] = useState([])
  const [isComplete, setIsComplete] = useState(false)
  const [currentEmotion, setCurrentEmotion] = useState(null)
  const [feedback, setFeedback] = useState(null)

  // Função TTS padronizada
  const speak = useCallback((text, options = {}) => {
    if (!ttsActive || !('speechSynthesis' in window)) {
      return;
    }

    window.speechSynthesis.cancel();

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'pt-BR';
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;

    window.speechSynthesis.speak(utterance);
  }, [ttsActive]);

  // ✅ FUNÇÕES V3 - GERAÇÃO DE ATIVIDADES

  // 🎯 Gerar conteúdo para atividade atual - REDESENHADO
  const generateActivityContent = useCallback((activityId, difficulty) => {
    const config = QuebraCabecaV3Config.DIFFICULTY_CONFIGS[difficulty.toUpperCase()];
    const activityConfig = QuebraCabecaV3Config.ACTIVITY_CONFIG[activityId.toUpperCase()];

    switch (activityId) {
      case 'emotion_puzzle':
        return generateEmotionPuzzleActivity(config, activityConfig);
      case 'piece_rotation':
        return generatePieceRotationActivity(config, activityConfig);
      case 'pattern_puzzle':
        return generatePatternPuzzleActivity(config, activityConfig);
      case 'emotion_matching':
        return generateEmotionMatchingActivity(config, activityConfig);
      case 'creative_assembly':
        return generateCreativeAssemblyActivity(config, activityConfig);
      default:
        return generateEmotionPuzzleActivity(config, activityConfig);
    }
  }, []);

  // =====================================================
  // 🎯 FUNÇÕES DE GERAÇÃO REDESENHADAS - QUEBRA-CABEÇA ESPECÍFICO
  // =====================================================

  // 🧩 QUEBRA-CABEÇA EMOCIONAL - Montagem tradicional de peças emocionais
  const generateEmotionPuzzleActivity = useCallback((difficultyConfig, activityConfig) => {
    console.log('🧩 Generating emotion puzzle activity');

    const emotionLibrary = QuebraCabecaV3Config.EMOTIONS_LIBRARY[difficultyConfig?.emotionComplexity || 'basic'];
    const randomEmotion = emotionLibrary[Math.floor(Math.random() * emotionLibrary.length)];

    const pieceCounts = {
      easy: 4,
      medium: 6,
      hard: 9
    };

    const pieceCount = pieceCounts[gameState.difficulty] || 4;
    const correctPieces = randomEmotion.pieces.slice(0, pieceCount);
    const distractorPieces = generateDistractorPieces(correctPieces, 2);

    return {
      emotion: randomEmotion,
      correctPieces,
      availablePieces: [...correctPieces, ...distractorPieces].sort(() => Math.random() - 0.5),
      targetSlots: pieceCount,
      instruction: `Monte o quebra-cabeça da emoção "${randomEmotion.name}"`,
      level: gameState.difficulty,
      activityType: 'emotion_puzzle',
      gridLayout: Math.ceil(Math.sqrt(pieceCount))
    };
  }, [gameState.difficulty]);

  // 🔄 ROTAÇÃO DE PEÇAS - Rotação mental e orientação espacial
  const generatePieceRotationActivity = useCallback((difficultyConfig, activityConfig) => {
    console.log('🔄 Generating piece rotation activity');

    const emotionLibrary = QuebraCabecaV3Config.EMOTIONS_LIBRARY[difficultyConfig?.emotionComplexity || 'basic'];
    const randomEmotion = emotionLibrary[Math.floor(Math.random() * emotionLibrary.length)];

    const rotationAngles = {
      easy: [90, 180],
      medium: [90, 180, 270],
      hard: [45, 90, 135, 180, 225, 270, 315]
    };

    const pieces = randomEmotion.pieces.slice(0, 4);
    const rotatedPieces = pieces.map((piece, index) => ({
      id: index,
      original: piece,
      angle: rotationAngles[gameState.difficulty][Math.floor(Math.random() * rotationAngles[gameState.difficulty].length)],
      placed: false,
      correctPosition: index
    }));

    return {
      emotion: randomEmotion,
      rotatedPieces,
      instruction: `Rotacione mentalmente as peças e monte "${randomEmotion.name}"`,
      level: gameState.difficulty,
      activityType: 'piece_rotation',
      completedRotations: 0
    };
  }, [gameState.difficulty]);

  // 🔍 QUEBRA-CABEÇA DE PADRÕES - Reconhecimento de padrões em quebra-cabeças
  const generatePatternPuzzleActivity = useCallback((difficultyConfig, activityConfig) => {
    console.log('🔍 Generating pattern puzzle activity');

    const patternTypes = {
      easy: [
        { type: 'alternating', sequence: ['😊', '😢', '😊', '😢'], missing: 2, correct: '😊' },
        { type: 'sequential', sequence: ['😊', '🤩', '😍', '🥰'], missing: 3, correct: '🥰' }
      ],
      medium: [
        { type: 'progressive', sequence: ['😐', '😊', '😄', '🤩'], missing: 2, correct: '😄' },
        { type: 'emotional_scale', sequence: ['😢', '😔', '😐', '😊'], missing: 1, correct: '😔' }
      ],
      hard: [
        { type: 'complex_pattern', sequence: ['😊', '😢', '😡', '😊', '😢'], missing: 4, correct: '😡' },
        { type: 'emotional_cycle', sequence: ['😴', '😊', '😰', '😴'], missing: 2, correct: '😰' }
      ]
    };

    const levelPatterns = patternTypes[gameState.difficulty] || patternTypes.easy;
    const selectedPattern = levelPatterns[Math.floor(Math.random() * levelPatterns.length)];

    // Criar opções de resposta
    const wrongOptions = ['😲', '🤔', '😏', '🥳'].filter(opt => opt !== selectedPattern.correct);
    const options = [selectedPattern.correct, ...wrongOptions.slice(0, 2)].sort(() => Math.random() - 0.5);

    return {
      pattern: selectedPattern,
      options,
      instruction: `Complete o padrão emocional observando a sequência`,
      level: gameState.difficulty,
      activityType: 'pattern_puzzle',
      userAnswer: null,
      solved: false
    };
  }, [gameState.difficulty]);

  // 😊 CORRESPONDÊNCIA EMOCIONAL - Correspondência entre peças emocionais
  const generateEmotionMatchingActivity = useCallback((difficultyConfig, activityConfig) => {
    console.log('😊 Generating emotion matching activity');

    const emotionPairs = {
      easy: [
        { emotion: '😊', matches: ['🎁', '🎉', '🌞'], context: 'Coisas que deixam feliz' },
        { emotion: '😢', matches: ['🌧️', '💔', '😔'], context: 'Coisas que deixam triste' }
      ],
      medium: [
        { emotion: '😰', matches: ['📚', '🎭', '⚡'], context: 'Coisas que causam ansiedade' },
        { emotion: '😡', matches: ['🚫', '💥', '🌋'], context: 'Coisas que causam raiva' },
        { emotion: '😌', matches: ['🌊', '🕊️', '🌿'], context: 'Coisas que acalmam' }
      ],
      hard: [
        { emotion: '🤔', matches: ['❓', '🧩', '📖'], context: 'Coisas que fazem pensar' },
        { emotion: '😍', matches: ['💖', '🌹', '✨'], context: 'Coisas que inspiram amor' },
        { emotion: '😤', matches: ['🏆', '⭐', '👑'], context: 'Coisas que geram orgulho' },
        { emotion: '😵', matches: ['🤯', '🌪️', '💫'], context: 'Coisas que confundem' }
      ]
    };

    const levelPairs = emotionPairs[gameState.difficulty] || emotionPairs.easy;
    const selectedPair = levelPairs[Math.floor(Math.random() * levelPairs.length)];

    // Adicionar distratores
    const allMatches = Object.values(emotionPairs).flat().map(p => p.matches).flat();
    const distractors = allMatches.filter(match => !selectedPair.matches.includes(match)).slice(0, 3);
    const allOptions = [...selectedPair.matches, ...distractors].sort(() => Math.random() - 0.5);

    return {
      targetEmotion: selectedPair.emotion,
      correctMatches: selectedPair.matches,
      allOptions,
      context: selectedPair.context,
      selectedMatches: [],
      instruction: `Encontre todas as peças que combinam com ${selectedPair.emotion}`,
      level: gameState.difficulty,
      activityType: 'emotion_matching'
    };
  }, [gameState.difficulty]);

  // 🎨 MONTAGEM CRIATIVA - Criatividade e montagem livre
  const generateCreativeAssemblyActivity = useCallback((difficultyConfig, activityConfig) => {
    console.log('🎨 Generating creative assembly activity');

    const emotionLibrary = QuebraCabecaV3Config.EMOTIONS_LIBRARY[difficultyConfig?.emotionComplexity || 'basic'];
    const availableEmotions = emotionLibrary.slice(0, 3); // Múltiplas emoções para criatividade

    const creativePieces = [];

    // Coletar peças de múltiplas emoções para composição criativa
    availableEmotions.forEach(emotion => {
      creativePieces.push(...emotion.pieces.slice(0, 3));
    });

    const constraints = {
      easy: { minPieces: 4, maxPieces: 6, canvasSize: '3x2' },
      medium: { minPieces: 6, maxPieces: 9, canvasSize: '3x3' },
      hard: { minPieces: 8, maxPieces: 12, canvasSize: '4x3' }
    };

    const currentConstraints = constraints[gameState.difficulty] || constraints.easy;

    return {
      availableEmotions,
      creativePieces: creativePieces.sort(() => Math.random() - 0.5),
      constraints: currentConstraints,
      userComposition: [],
      canvasGrid: Array(currentConstraints.minPieces).fill(null),
      instruction: `Monte livremente uma composição emocional criativa`,
      creativityScore: 0,
      level: gameState.difficulty,
      activityType: 'creative_assembly',
      isComplete: false
    };
  }, [gameState.difficulty]);

  // 🔧 Funções auxiliares de geração
  const generateDistractorPieces = useCallback((correctPieces, count) => {
    const allPieces = ['😊', '😢', '😲', '😠', '😌', '🤩', '🌞', '🌧️', '💔', '🎁', '🎉', '❓', '✨', '🌊', '🕊️', '🌿', '💥', '🌋', '⚡', '🎪', '🎢', '🎊'];
    const distractors = allPieces.filter(piece => !correctPieces.includes(piece));
    return distractors.sort(() => Math.random() - 0.5).slice(0, count);
  }, []);

  // Funções auxiliares para as novas atividades
  const generateEmotionCategories = useCallback((settings) => {
    const baseCategories = {
      positive: { name: 'Emoções Positivas', icon: '😊', pieces: [] },
      negative: { name: 'Emoções Negativas', icon: '😢', pieces: [] },
      neutral: { name: 'Emoções Neutras', icon: '😐', pieces: [] },
      intense: { name: 'Emoções Intensas', icon: '🤯', pieces: [] }
    };

    const selectedCategories = Object.keys(baseCategories).slice(0, settings.categories || 2);
    const result = {};
    selectedCategories.forEach(key => {
      result[key] = baseCategories[key];
    });

    return result;
  }, []);

  const generateEmotionPiecesForSorting = useCallback((categories, settings) => {
    const categoryKeys = Object.keys(categories);
    const pieces = [];

    const emotionMap = {
      positive: ['😊', '😄', '🤩', '😍', '🥰', '😌'],
      negative: ['😢', '😭', '😠', '😡', '😰', '😔'],
      neutral: ['😐', '🤔', '😑', '😶', '🙄', '😏'],
      intense: ['🤯', '😱', '🤬', '😵', '🥵', '🥶']
    };

    categoryKeys.forEach(categoryKey => {
      const categoryPieces = emotionMap[categoryKey] || [];
      const selectedPieces = categoryPieces.slice(0, settings.piecesPerCategory || 3);
      pieces.push(...selectedPieces.map(piece => ({ piece, category: categoryKey, emoji: piece })));
    });

    return pieces.sort(() => Math.random() - 0.5);
  }, []);

  const generatePiecesForClassification = useCallback((categories, settings) => {
    const categoryKeys = Object.keys(categories);
    const pieces = [];
    
    categoryKeys.forEach(categoryKey => {
      const categoryPieces = getCategoryPieces(categoryKey);
      const selectedPieces = categoryPieces.slice(0, settings.piecesPerCategory);
      pieces.push(...selectedPieces.map(piece => ({ piece, category: categoryKey })));
    });
    
    return pieces.sort(() => Math.random() - 0.5);
  }, []);

  const getCategoryPieces = useCallback((category) => {
    const categoryMap = {
      emotions: ['😊', '😢', '😲', '😠', '😌', '🤩'],
      nature: ['🌞', '🌧️', '🌊', '🕊️', '🌿', '🌋'],
      objects: ['🎁', '🎉', '🎪', '🎢', '🎊', '💔'],
      symbols: ['❓', '✨', '💥', '⚡', '🏆', '⭐']
    };
    return categoryMap[category] || [];
  }, []);

  const generateLogicalPattern = useCallback((settings) => {
    const patternTypes = settings.patternTypes;
    const selectedType = patternTypes[Math.floor(Math.random() * patternTypes.length)];
    
    switch (selectedType) {
      case 'alternating':
        return { type: 'alternating', sequence: ['😊', '😢', '😊', '😢'], next: '😊' };
      case 'sequential':
        return { type: 'sequential', sequence: ['😊', '🌞', '😢', '🌧️'], next: '😲' };
      case 'progressive':
        return { type: 'progressive', sequence: ['😊', '🤩', '😢', '😭'], next: '😡' };
      default:
        return { type: 'alternating', sequence: ['😊', '😢', '😊', '😢'], next: '😊' };
    }
  }, []);

  const generatePatternOptions = useCallback((pattern, settings) => {
    const correctAnswer = pattern.next;
    const wrongOptions = ['😲', '🤔', '😴', '🥳'].filter(opt => opt !== correctAnswer);
    const selectedWrong = wrongOptions.slice(0, settings.completionOptions - 1);
    
    return [correctAnswer, ...selectedWrong].sort(() => Math.random() - 0.5);
  }, []);

  // ✅ FUNÇÕES V3 - ROTAÇÃO DE ATIVIDADES

  // 🔄 Rotacionar para próxima atividade
  const rotateToNextActivity = useCallback(() => {
    setGameState(prev => {
      const nextIndex = (prev.activityIndex + 1) % prev.activityCycle.length;
      const nextActivity = prev.activityCycle[nextIndex];
      
      // Registrar rotação de atividade
      if (recordMultisensoryInteraction) {
        recordMultisensoryInteraction('activity_rotation', {
          from: prev.currentActivity,
          to: nextActivity,
          automatic: true,
          round: prev.round
        });
      }
      
      const newState = {
        ...prev,
        currentActivity: nextActivity,
        activityIndex: nextIndex,
        activityRoundCount: 0,
        round: prev.round + 1,
        roundStartTime: Date.now()
      };
      
      // Gerar novo conteúdo para a atividade
      const activityContent = generateActivityContent(newState.currentActivity, newState.difficulty);
      
      // Atualizar dados específicos da atividade
      newState.currentEmotion = activityContent.emotion || null;
      newState.puzzlePieces = activityContent.availablePieces || activityContent.pieces || [];
      newState.placedPieces = new Array(activityContent.targetSlots || 3).fill(null);
      
      // Anunciar nova atividade
      const activityConfig = QuebraCabecaV3Config.ACTIVITY_CONFIG[nextActivity.toUpperCase()];
      const activityName = activityConfig?.name || 'Nova Atividade';
      
      setTimeout(() => {
        if (speak) {
          speak(`Nova atividade: ${activityName}. ${activityContent.instruction}`, { rate: 0.8 });
        }
      }, 500);
      
      return newState;
    });
  }, [generateActivityContent, recordMultisensoryInteraction, speak]);

  // 🎯 Trocar atividade manualmente
  const switchActivity = useCallback((activityId) => {
    setGameState(prev => {
      const activityIndex = prev.activityCycle.indexOf(activityId);
      if (activityIndex === -1) return prev;
      
      const activityConfig = QuebraCabecaV3Config.ACTIVITY_CONFIG[activityId.toUpperCase()];
      const activityName = activityConfig?.name || 'Nova Atividade';
      
      // Registrar troca manual de atividade
      if (recordMultisensoryInteraction) {
        recordMultisensoryInteraction('activity_switch', {
          from: prev.currentActivity,
          to: activityId,
          manual: true,
          round: prev.round
        });
      }
      
      const newState = {
        ...prev,
        currentActivity: activityId,
        activityIndex: activityIndex,
        activityRoundCount: 0,
        round: prev.round + 1,
        roundStartTime: Date.now()
      };
      
      // Gerar novo conteúdo
      const activityContent = generateActivityContent(newState.currentActivity, newState.difficulty);
      
      newState.currentEmotion = activityContent.emotion || null;
      newState.puzzlePieces = activityContent.availablePieces || activityContent.pieces || [];
      newState.placedPieces = new Array(activityContent.targetSlots || 3).fill(null);
      
      // Anunciar atividade
      setTimeout(() => {
        if (speak) {
          speak(`Atividade alterada para: ${activityName}. ${activityContent.instruction}`, { rate: 0.8 });
        }
      }, 300);
      
      return newState;
    });
  }, [generateActivityContent, recordMultisensoryInteraction, speak]);

  // ✅ FUNÇÕES V3 - GERAÇÃO DE NOVA RODADA
  const generateNewRound = useCallback(() => {
    console.log('🎯 Gerando nova rodada V3...', { 
      currentActivity: gameState.currentActivity,
      activityRoundCount: gameState.activityRoundCount,
      round: gameState.round 
    });
    
    setGameState(prev => {
      // Verificar se precisa rotar atividade
      const shouldRotateActivity = prev.activityRoundCount >= prev.roundsPerActivity;
      
      console.log('🔄 Verificando rotação de atividade:', { 
        shouldRotateActivity, 
        activityRoundCount: prev.activityRoundCount,
        roundsPerActivity: prev.roundsPerActivity 
      });
      
      let newState = { ...prev };
      
      if (shouldRotateActivity) {
        // Rotar para próxima atividade
        const nextActivityIndex = (prev.activityIndex + 1) % prev.activityCycle.length;
        const nextActivity = prev.activityCycle[nextActivityIndex];
        
        console.log('🎮 Rotacionando para nova atividade:', { 
          from: prev.currentActivity, 
          to: nextActivity,
          nextActivityIndex 
        });
        
        newState = {
          ...newState,
          currentActivity: nextActivity,
          activityIndex: nextActivityIndex,
          activityRoundCount: 0,
          activitiesCompleted: prev.activitiesCompleted + 1
        };
        
        // 🔊 Anunciar nova atividade
        const activityConfig = QuebraCabecaV3Config.ACTIVITY_CONFIG[nextActivity.toUpperCase()];
        const activityName = activityConfig?.name || 'Nova Atividade';
        setTimeout(() => {
          if (speak) {
            speak(`Nova atividade: ${activityName}`, { pitch: 1.2, rate: 0.8 });
          }
        }, 500);
      }
      
      // Gerar conteúdo específico da atividade
      const activityContent = generateActivityContent(newState.currentActivity, newState.difficulty);
      
      console.log('📝 Conteúdo gerado para atividade:', { 
        activity: newState.currentActivity, 
        content: activityContent 
      });
      
      // Atualizar estado baseado na atividade atual
      newState.currentEmotion = activityContent.emotion || newState.currentEmotion;
      newState.puzzlePieces = activityContent.availablePieces || activityContent.pieces || [];
      newState.placedPieces = new Array(activityContent.targetSlots || 3).fill(null);
      newState.isComplete = false;
      newState.draggedPiece = null;
      newState.feedback = null;
      newState.showFeedback = false;
      
      // Incrementar contadores
      newState.activityRoundCount = newState.activityRoundCount + 1;
      newState.round = newState.round + 1;
      newState.roundStartTime = Date.now();
      
      return newState;
    });
  }, [gameState.currentActivity, gameState.activityRoundCount, gameState.round, generateActivityContent, speak]);

  // =====================================================
  // 🎯 FUNÇÕES DE RENDERIZAÇÃO PARA CADA ATIVIDADE
  // =====================================================

  // 🧩 MONTAGEM EMOCIONAL - Layout de quebra-cabeça tradicional
  const renderEmotionAssembly = () => {
    return (
      <div className={styles.activityContainer}>
        <div className={styles.activityHeader}>
          <h2 className={styles.activityTitle}>🎯 Montagem Emocional</h2>
          <p className={styles.activitySubtitle}>
            {currentEmotion ? `Monte a emoção: ${currentEmotion.name}` : 'Coordenação motora e reconhecimento emocional'}
          </p>
        </div>

        {currentEmotion && (
          <>
            {/* Área do quebra-cabeça */}
            <div className={styles.puzzleArea}>
              <div className={styles.puzzleBoard}>
                <div className={styles.boardTitle}>
                  Monte a emoção: {currentEmotion.name}
                </div>
                <div className={styles.puzzleGrid}>
                  {placedPieces.map((piece, index) => (
                    <div
                      key={index}
                      className={`${styles.puzzlePiece} ${piece ? styles.filled : styles.empty}`}
                      onDragOver={(e) => e.preventDefault()}
                      onDrop={() => handleDrop(index)}
                      style={{ backgroundColor: currentEmotion.color + '20' }}
                    >
                      {piece && (
                        <div className={`${styles.puzzlePiece} ${styles.correct}`}>
                          {piece.content}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Área das peças disponíveis */}
              <div className={styles.piecesArea}>
                <div className={styles.piecesTitle}>Peças Disponíveis</div>
                <div className={styles.piecesGrid}>
                  {puzzlePieces.map((piece) => (
                    <div
                      key={piece.id}
                      className={`${styles.availablePiece} ${piece.used ? styles.used : ''}`}
                      draggable
                      onDragStart={() => handleDragStart(piece)}
                      onClick={() => {
                        const emptySlotIndex = placedPieces.findIndex(p => p === null)
                        if (emptySlotIndex !== -1) {
                          handleDrop(emptySlotIndex)
                        }
                      }}
                    >
                      {piece.content}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    );
  };

  // 🔍 COMPLETAR PADRÕES - Layout de sequência lógica
  const renderPatternCompletion = () => {
    const activityData = gameState.activityData?.patternCompletion || {};

    return (
      <div className={styles.activityContainer}>
        <div className={styles.activityHeader}>
          <h2 className={styles.activityTitle}>🔍 Completar Padrões</h2>
          <p className={styles.activitySubtitle}>Identifique e complete sequências lógicas</p>
        </div>

        {activityData.pattern && (
          <>
            {/* Área do padrão */}
            <div className={styles.patternArea}>
              <div className={styles.patternTitle}>
                Padrão: {activityData.pattern.type}
              </div>
              <div className={styles.patternSequence}>
                {activityData.pattern.sequence.map((item, index) => (
                  <div key={index} className={styles.patternItem}>
                    <div className={styles.patternNumber}>{index + 1}</div>
                    <div className={styles.patternEmoji}>{item}</div>
                  </div>
                ))}
                <div className={styles.patternItem}>
                  <div className={styles.patternNumber}>{activityData.pattern.sequence.length + 1}</div>
                  <div className={styles.patternQuestion}>❓</div>
                </div>
              </div>
            </div>

            {/* Opções de resposta */}
            <div className={styles.optionsArea}>
              <div className={styles.optionsTitle}>Escolha o próximo elemento:</div>
              <div className={styles.optionsGrid}>
                {activityData.options?.map((option, index) => (
                  <button
                    key={index}
                    className={`${styles.optionButton} ${
                      activityData.userAnswer === option ? styles.selected : ''
                    }`}
                    onClick={() => {
                      setGameState(prev => ({
                        ...prev,
                        activityData: {
                          ...prev.activityData,
                          patternCompletion: {
                            ...prev.activityData.patternCompletion,
                            userAnswer: option
                          }
                        }
                      }));
                    }}
                    disabled={activityData.userAnswer !== null}
                  >
                    {option}
                  </button>
                ))}
              </div>
            </div>
          </>
        )}
      </div>
    );
  };

  // 🔄 ROTAÇÃO ESPACIAL - Layout de peças rotacionadas
  const renderSpatialRotation = () => {
    const activityData = gameState.activityData?.spatialRotation || {};

    return (
      <div className={styles.activityContainer}>
        <div className={styles.activityHeader}>
          <h2 className={styles.activityTitle}>🔄 Rotação Espacial</h2>
          <p className={styles.activitySubtitle}>Mentalize a rotação e posicione as peças corretamente</p>
        </div>

        {activityData.rotatedPieces && (
          <>
            {/* Área de rotação */}
            <div className={styles.rotationArea}>
              <div className={styles.rotationTitle}>
                Peças Rotacionadas - Mentalize a posição original
              </div>
              <div className={styles.rotationGrid}>
                {activityData.rotatedPieces.map((piece, index) => (
                  <div
                    key={index}
                    className={`${styles.rotatedPiece} ${piece.placed ? styles.placed : ''}`}
                    style={{ transform: `rotate(${piece.angle}deg)` }}
                    onClick={() => {
                      // Lógica de posicionamento da peça rotacionada
                      setGameState(prev => ({
                        ...prev,
                        activityData: {
                          ...prev.activityData,
                          spatialRotation: {
                            ...prev.activityData.spatialRotation,
                            rotatedPieces: prev.activityData.spatialRotation.rotatedPieces.map((p, i) =>
                              i === index ? { ...p, placed: true } : p
                            )
                          }
                        }
                      }));
                    }}
                  >
                    <div className={styles.pieceContent}>{piece.original}</div>
                    <div className={styles.rotationAngle}>{piece.angle}°</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Área de posicionamento */}
            <div className={styles.positionArea}>
              <div className={styles.positionTitle}>Posições Corretas</div>
              <div className={styles.positionGrid}>
                {Array.from({ length: activityData.targetPositions || 4 }).map((_, index) => (
                  <div
                    key={index}
                    className={`${styles.positionSlot} ${
                      activityData.rotatedPieces?.some(p => p.correctPosition === index && p.placed)
                        ? styles.filled : styles.empty
                    }`}
                  >
                    {index + 1}
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
      </div>
    );
  };

  // 😊 CLASSIFICAÇÃO EMOCIONAL - Layout de categorização
  const renderEmotionSorting = () => {
    const activityData = gameState.activityData?.emotionSorting || {};

    return (
      <div className={styles.activityContainer}>
        <div className={styles.activityHeader}>
          <h2 className={styles.activityTitle}>😊 Classificação Emocional</h2>
          <p className={styles.activitySubtitle}>Organize as emoções nas categorias corretas</p>
        </div>

        {activityData.categories && (
          <>
            {/* Área de categorias */}
            <div className={styles.categoriesArea}>
              {Object.entries(activityData.categories).map(([categoryKey, category]) => (
                <div key={categoryKey} className={styles.categoryContainer}>
                  <div className={styles.categoryHeader}>
                    <span className={styles.categoryIcon}>{category.icon}</span>
                    <span className={styles.categoryName}>{category.name}</span>
                  </div>
                  <div className={styles.categoryDropZone}>
                    {activityData.classified?.[categoryKey]?.map((item, index) => (
                      <div key={index} className={styles.classifiedItem}>
                        {item.emoji}
                      </div>
                    )) || <div className={styles.emptyCategory}>Arraste emoções aqui</div>}
                  </div>
                </div>
              ))}
            </div>

            {/* Peças para classificar */}
            <div className={styles.sortingPieces}>
              <div className={styles.sortingTitle}>Emoções para Classificar</div>
              <div className={styles.sortingGrid}>
                {activityData.pieces?.map((piece, index) => (
                  <div
                    key={index}
                    className={`${styles.sortingPiece} ${
                      Object.values(activityData.classified || {}).flat().some(item => item.emoji === piece.emoji)
                        ? styles.classified : ''
                    }`}
                    draggable
                    onClick={() => {
                      // Lógica de classificação automática para touch
                      const targetCategory = piece.category;
                      setGameState(prev => ({
                        ...prev,
                        activityData: {
                          ...prev.activityData,
                          emotionSorting: {
                            ...prev.activityData.emotionSorting,
                            classified: {
                              ...prev.activityData.emotionSorting.classified,
                              [targetCategory]: [
                                ...(prev.activityData.emotionSorting.classified?.[targetCategory] || []),
                                piece
                              ]
                            }
                          }
                        }
                      }));
                    }}
                  >
                    {piece.emoji}
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
      </div>
    );
  };

  // 🎨 COMPOSIÇÃO CRIATIVA - Layout de criação livre
  const renderCreativeComposition = () => {
    const activityData = gameState.activityData?.creativeComposition || {};

    return (
      <div className={styles.activityContainer}>
        <div className={styles.activityHeader}>
          <h2 className={styles.activityTitle}>🎨 Composição Criativa</h2>
          <p className={styles.activitySubtitle}>Crie uma composição emocional única</p>
        </div>

        {activityData.creativePieces && (
          <>
            {/* Área de composição */}
            <div className={styles.compositionArea}>
              <div className={styles.compositionTitle}>Sua Composição Criativa</div>
              <div className={styles.compositionCanvas}>
                {activityData.userComposition?.length > 0 ? (
                  activityData.userComposition.map((piece, index) => (
                    <div key={index} className={styles.compositionPiece}>
                      {piece}
                    </div>
                  ))
                ) : (
                  <div className={styles.emptyCanvas}>
                    Clique nas peças abaixo para criar sua composição
                  </div>
                )}
              </div>

              {/* Restrições */}
              <div className={styles.compositionConstraints}>
                <div className={styles.constraintItem}>
                  Peças: {activityData.userComposition?.length || 0} / {activityData.constraints?.maxPieces || 5}
                </div>
                <div className={styles.constraintItem}>
                  Temas: {activityData.constraints?.themes?.join(', ') || 'Livre'}
                </div>
              </div>
            </div>

            {/* Peças disponíveis */}
            <div className={styles.creativePieces}>
              <div className={styles.creativePiecesTitle}>Peças Disponíveis</div>
              <div className={styles.creativePiecesGrid}>
                {activityData.creativePieces?.map((piece, index) => (
                  <button
                    key={index}
                    className={`${styles.creativePiece} ${
                      activityData.userComposition?.includes(piece) ? styles.used : ''
                    }`}
                    onClick={() => {
                      if ((activityData.userComposition?.length || 0) < (activityData.constraints?.maxPieces || 5)) {
                        setGameState(prev => ({
                          ...prev,
                          activityData: {
                            ...prev.activityData,
                            creativeComposition: {
                              ...prev.activityData.creativeComposition,
                              userComposition: [
                                ...(prev.activityData.creativeComposition?.userComposition || []),
                                piece
                              ]
                            }
                          }
                        }));
                      }
                    }}
                    disabled={activityData.userComposition?.includes(piece)}
                  >
                    {piece}
                  </button>
                ))}
              </div>
            </div>
          </>
        )}
      </div>
    );
  };

  // =====================================================
  // 🎯 FUNÇÕES DE RENDERIZAÇÃO PARA CADA ATIVIDADE
  // =====================================================

  // 🧩 QUEBRA-CABEÇA EMOCIONAL - SEGUINDO PADRÃO MEMORY GAME
  const renderEmotionPuzzle = () => {
    // Se não há emoção atual, gerar uma nova
    if (!currentEmotion && gameStarted) {
      generateNewPuzzle();
      return (
        <div className={styles.questionArea}>
          <div className={styles.questionHeader}>
            <h2 className={styles.questionTitle}>🧩 Quebra-Cabeça Emocional</h2>
            <p className={styles.instructions}>Carregando quebra-cabeça...</p>
          </div>
        </div>
      );
    }

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>🧩 Monte a emoção: {currentEmotion?.name}</h2>
          <p className={styles.instructions}>
            {placedPieces.filter(p => p).length === 0
              ? "Toque nas peças para montar o quebra-cabeça emocional"
              : `Progresso: ${placedPieces.filter(p => p).length} / ${placedPieces.length} peças`}
          </p>
        </div>

        {/* Área do quebra-cabeça - seguindo padrão Memory Game */}
        <div className={styles.puzzleContainer}>
          {/* Emoção alvo */}
          {currentEmotion && (
            <div className={styles.targetEmotionDisplay}>
              <div className={styles.targetEmotionIcon}>{currentEmotion.emoji}</div>
              <div className={styles.targetEmotionName}>{currentEmotion.name}</div>
            </div>
          )}

          {/* Grid do quebra-cabeça */}
          <div
            className={styles.puzzleGrid}
            style={{
              gridTemplateColumns: `repeat(${Math.ceil(Math.sqrt(placedPieces.length))}, 1fr)`,
              backgroundColor: currentEmotion?.color + '10' || 'rgba(255,255,255,0.05)'
            }}
          >
            {placedPieces.map((piece, index) => (
              <div
                key={index}
                className={`${styles.puzzleSlot} ${piece ? styles.filled : styles.empty}`}
                onDragOver={(e) => e.preventDefault()}
                onDrop={() => handleDrop(index)}
                onClick={() => piece && handleRemovePiece(index)}
              >
                {piece ? (
                  <div className={styles.placedPiece}>
                    {piece.content}
                  </div>
                ) : (
                  <div className={styles.emptySlot}>+</div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Peças disponíveis - seguindo padrão Memory Game */}
        <div className={styles.piecesContainer}>
          <h4 className={styles.piecesTitle}>Peças disponíveis:</h4>
          <div className={styles.piecesGrid}>
            {puzzlePieces.length > 0 ? puzzlePieces.map((piece) => (
              <button
                key={piece.id}
                className={`${styles.availablePiece} ${piece.used ? styles.used : ''}`}
                draggable
                onDragStart={() => handleDragStart(piece)}
                onClick={() => {
                  const emptySlotIndex = placedPieces.findIndex(p => p === null);
                  if (emptySlotIndex !== -1) {
                    handleDrop(emptySlotIndex);
                  }
                }}
              >
                {piece.content}
              </button>
            )) : (
              <div className={styles.noPieces}>
                Carregando peças...
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // 🔄 ROTAÇÃO DE PEÇAS - PADRÃO COLORMATCH EXATO
  const renderPieceRotation = () => {
    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>🔄 Rotação de Peças</h2>
          <p className={styles.instructions}>Gire as peças para encaixá-las corretamente</p>
        </div>

        <div className={styles.objectsDisplay}>
          <div className={styles.instructionText}>
            🧠 Visualize mentalmente como cada peça deve ser rotacionada
          </div>
        </div>

        <div className={styles.answerOptions}>
          <h4 className={styles.optionsTitle}>Gire as peças:</h4>
          <div className={styles.optionsContainer}>
            {[0, 90, 180, 270].map((rotation, index) => (
              <button
                key={index}
                className={styles.answerOption}
                style={{ transform: `rotate(${rotation}deg)` }}
                onClick={() => handlePieceRotation(index)}
              >
                <div style={{ fontSize: '2rem' }}>🧩</div>
                <div style={{ fontSize: '0.8rem', marginTop: '0.5rem' }}>
                  {rotation}°
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>
    );
  };

  // 🔍 QUEBRA-CABEÇA DE PADRÕES - PADRÃO COLORMATCH EXATO
  const renderPatternPuzzle = () => {
    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>🔍 Quebra-Cabeça de Padrões</h2>
          <p className={styles.instructions}>Complete o padrão emocional observando a sequência</p>
        </div>

        <div className={styles.objectsDisplay}>
          <div className={styles.patternSequence}>
            {['😊', '😢', '😊', '😢', '❓'].map((item, index) => (
              <div
                key={index}
                className={`${styles.patternPiece} ${index === 4 ? styles.missing : ''}`}
              >
                {item}
              </div>
            ))}
          </div>
        </div>

        <div className={styles.answerOptions}>
          <h4 className={styles.optionsTitle}>Complete o padrão:</h4>
          <div className={styles.optionsContainer}>
            {['😊', '😢', '😡', '😨'].map((option, index) => (
              <button
                key={index}
                className={styles.answerOption}
                onClick={() => handlePatternAnswer(option)}
              >
                {option}
              </button>
            ))}
          </div>
        </div>
      </div>
    );
  };

  // 😊 CORRESPONDÊNCIA EMOCIONAL - PADRÃO COLORMATCH EXATO
  const renderEmotionMatching = () => {
    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>😊 Correspondência Emocional</h2>
          <p className={styles.instructions}>Encontre peças que combinam com a emoção</p>
        </div>

        <div className={styles.objectsDisplay}>
          <div className={styles.targetEmotion}>😊</div>
        </div>

        <div className={styles.answerOptions}>
          <h4 className={styles.optionsTitle}>Escolha a combinação:</h4>
          <div className={styles.optionsContainer}>
            {['😊', '😢', '😡', '😨'].map((option, index) => (
              <button
                key={index}
                className={styles.answerOption}
                onClick={() => handleEmotionMatch(option)}
              >
                {option}
              </button>
            ))}
          </div>
        </div>

        {activityData.targetEmotion && (
          <>
            {/* Emoção alvo */}
            <div className={styles.targetEmotionArea}>
              <div className={styles.targetEmotionTitle}>Emoção Principal</div>
              <div className={styles.targetEmotion}>
                <div className={styles.targetEmotionEmoji}>{activityData.targetEmotion}</div>
                <div className={styles.targetEmotionContext}>{activityData.context}</div>
              </div>
            </div>

            {/* Área de correspondências */}
            <div className={styles.matchingArea}>
              <div className={styles.matchingTitle}>Peças Selecionadas</div>
              <div className={styles.selectedMatches}>
                {activityData.selectedMatches?.length > 0 ? (
                  activityData.selectedMatches.map((match, index) => (
                    <div key={index} className={styles.selectedMatch}>
                      {match}
                    </div>
                  ))
                ) : (
                  <div className={styles.emptyMatches}>Clique nas peças abaixo que combinam</div>
                )}
              </div>
            </div>

            {/* Opções disponíveis */}
            <div className={styles.matchingOptions}>
              <div className={styles.matchingOptionsTitle}>Peças Disponíveis</div>
              <div className={styles.matchingOptionsGrid}>
                {activityData.allOptions?.map((option, index) => {
                  const isSelected = activityData.selectedMatches?.includes(option);
                  const isCorrect = activityData.correctMatches?.includes(option);

                  return (
                    <button
                      key={index}
                      className={`${styles.matchingOption} ${isSelected ? styles.selected : ''}`}
                      onClick={() => {
                        if (!isSelected) {
                          setGameState(prev => ({
                            ...prev,
                            activityData: {
                              ...prev.activityData,
                              emotionMatching: {
                                ...prev.activityData.emotionMatching,
                                selectedMatches: [
                                  ...(prev.activityData.emotionMatching?.selectedMatches || []),
                                  option
                                ]
                              }
                            }
                          }));

                          if (isCorrect) {
                            speak(`Correto! ${option} combina com ${activityData.targetEmotion}.`);
                          } else {
                            speak(`${option} selecionado. Verifique se realmente combina.`);
                          }
                        }
                      }}
                      disabled={isSelected}
                      style={{
                        opacity: isSelected ? 0.6 : 1,
                        border: isSelected ?
                          (isCorrect ? '3px solid #4CAF50' : '3px solid #f44336') :
                          '2px solid rgba(255,255,255,0.3)'
                      }}
                    >
                      {option}
                    </button>
                  );
                })}
              </div>
            </div>
          </>
        )}
      </div>
    );
  };

  // 🎨 MONTAGEM CRIATIVA - PADRÃO COLORMATCH EXATO
  const renderCreativeAssembly = () => {
    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>🎨 Montagem Criativa</h2>
          <p className={styles.instructions}>Monte livremente uma composição emocional criativa</p>
        </div>

        <div className={styles.objectsDisplay}>
          <div className={styles.creativeCanvas}>
            <div className={styles.canvasTitle}>Sua Criação:</div>
            <div className={styles.canvasArea}>
              {/* Canvas criativo */}
              <div className={styles.creativeGrid}>
                {Array.from({ length: 9 }, (_, index) => (
                  <div
                    key={index}
                    className={styles.creativeSlot}
                    onClick={() => handleCreativeClick(index)}
                  >
                    {gameState.creativeComposition?.[index] || '⭐'}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className={styles.answerOptions}>
          <h4 className={styles.optionsTitle}>Peças Criativas:</h4>
          <div className={styles.optionsContainer}>
            {['😊', '😢', '😡', '😨', '🎨', '⭐', '💫', '🌟'].map((piece, index) => (
              <button
                key={index}
                className={styles.answerOption}
                onClick={() => handleCreativePiece(piece)}
              >
                {piece}
              </button>
            ))}
          </div>
        </div>


      </div>
    );
  };

  // 🔊 Cleanup do TTS quando componente é desmontado
  useEffect(() => {
    return () => {
      // Cancelar qualquer TTS ativo quando sair do jogo
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, []);

  const [analysisResults, setAnalysisResults] = useState(null)
  const [attemptCount, setAttemptCount] = useState(0)

  // Estados para métricas avançadas
  const [sessionStartTime, setSessionStartTime] = useState(null)
  const [pieceInteractions, setPieceInteractions] = useState([])
  const [spatialStrategies, setSpatialStrategies] = useState([])
  const [problemSolvingApproach, setProblemSolvingApproach] = useState('systematic')

  // Inicializar sistema multissensorial quando sessionId estiver disponível
  useEffect(() => {
    if (sessionId && typeof sessionId === 'string' && sessionId.length > 0 && gameStarted && !multisensoryInitialized) {
      const initializeMultisensorial = async () => {
        try {
          await initMultisensory();
          console.log('✅ Sistema multissensorial inicializado com sessionId:', sessionId);
        } catch (error) {
          console.error('❌ Erro ao inicializar sistema multissensorial:', error);
        }
      };
      initializeMultisensorial();
    }
  }, [sessionId, gameStarted, multisensoryInitialized, initMultisensory]);

  // Calcular precisão
  const getAccuracy = useCallback(() => {
    if (gameStats.totalAttempts === 0) return 100
    return Math.round((gameStats.completed / gameStats.totalAttempts) * 100)
  }, [gameStats])

  // 🧠 Coletar métricas específicas do quebra-cabeça
  const collectPuzzleMetrics = async () => {
    const currentTime = Date.now()
    const sessionDuration = sessionStartTime ? currentTime - sessionStartTime : 0

    // Métricas básicas do jogo
    const basicMetrics = {
      totalTime: sessionDuration,
      correctAnswers: gameStats.completed,
      incorrectAnswers: gameStats.totalAttempts - gameStats.completed,
      accuracy: getAccuracy(),
      difficultyLevel: difficulty,
      completionLevel: isComplete ? 100 : (placedPieces.length / puzzlePieces.length) * 100
    }

    // Métricas específicas do quebra-cabeça
    const puzzleSpecificMetrics = {
      spatialReasoning: calculateSpatialReasoning(),
      piecePlacementAccuracy: calculatePlacementAccuracy(),
      completionStrategy: identifyCompletionStrategy(),
      visualSpatialMemory: analyzeVisualSpatialMemory(),
      problemSolvingApproach: problemSolvingApproach,
      frustranceTolerance: calculateFrustranceTolerance(),
      persistenceLevel: calculatePersistenceLevel(),
      rotationAttempts: countRotationAttempts(),
      sequentialPlacement: analyzeSequentialPlacement()
    }

    // Coletar métricas através do sistema unificado
    await collectMetrics({
      ...basicMetrics,
      ...puzzleSpecificMetrics
    })

    // 🚀 Processar métricas avançadas para análise espacial
    await processAdvancedMetrics({
      gameType: 'QuebraCabeca',
      sessionData: {
        ...basicMetrics,
        ...puzzleSpecificMetrics,
        interactions: pieceInteractions,
        spatialStrategies: spatialStrategies,
        duration: sessionDuration
      },
      userProfile: {
        preferredDifficulty: difficulty,
        spatialPreferences: identifySpatialPreferences()
      }
    })
  }

  // Funções auxiliares para análise de métricas espaciais
  const calculateSpatialReasoning = () => {
    const correctPlacements = pieceInteractions.filter(i => i.correct).length
    const totalInteractions = pieceInteractions.length
    return totalInteractions > 0 ? (correctPlacements / totalInteractions) * 100 : 0
  }

  const calculatePlacementAccuracy = () => {
    const firstTryCorrect = pieceInteractions.filter(i => i.attemptNumber === 1 && i.correct).length
    const totalPieces = puzzlePieces.length
    return totalPieces > 0 ? (firstTryCorrect / totalPieces) * 100 : 0
  }

  const identifyCompletionStrategy = () => {
    if (spatialStrategies.length === 0) return 'unknown'
    
    const strategies = spatialStrategies.reduce((count, strategy) => {
      count[strategy] = (count[strategy] || 0) + 1
      return count
    }, {})
    
    return Object.keys(strategies).reduce((a, b) => strategies[a] > strategies[b] ? a : b)
  }

  const analyzeVisualSpatialMemory = () => {
    const memoryScore = pieceInteractions.reduce((score, interaction, index) => {
      if (index > 0) {
        const prevInteraction = pieceInteractions[index - 1]
        if (interaction.pieceId === prevInteraction.pieceId && 
            interaction.position.x === prevInteraction.position.x &&
            interaction.position.y === prevInteraction.position.y) {
          score += 10 // Bonus por lembrar posição
        }
      }
      return score
    }, 0)
    
    return Math.min(memoryScore, 100)
  }

  const calculateFrustranceTolerance = () => {
    const incorrectAttempts = pieceInteractions.filter(i => !i.correct).length
    const totalAttempts = pieceInteractions.length
    
    if (totalAttempts === 0) return 100
    
    // Menor % de tentativas incorretas = maior tolerância
    return Math.max(0, 100 - ((incorrectAttempts / totalAttempts) * 100))
  }

  const calculatePersistenceLevel = () => {
    const maxAttemptsPerPiece = Math.max(...puzzlePieces.map(piece => {
      return pieceInteractions.filter(i => i.pieceId === piece.id).length
    }), 1)
    
    // Persistência baseada no número máximo de tentativas por peça
    return Math.min(maxAttemptsPerPiece * 20, 100)
  }

  const countRotationAttempts = () => {
    return pieceInteractions.filter(i => i.action === 'rotate').length
  }

  const analyzeSequentialPlacement = () => {
    if (pieceInteractions.length < 2) return 'insufficient_data'
    
    let sequentialCount = 0
    for (let i = 1; i < pieceInteractions.length; i++) {
      const curr = pieceInteractions[i]
      const prev = pieceInteractions[i - 1]
      
      if (curr.pieceId === prev.pieceId + 1) {
        sequentialCount++
      }
    }
    
    const sequentialRatio = sequentialCount / (pieceInteractions.length - 1)
    return sequentialRatio > 0.7 ? 'sequential' : sequentialRatio > 0.4 ? 'mixed' : 'random'
  }

  const identifySpatialPreferences = () => {
    return {
      preferredStartPosition: 'top-left', // Placeholder
      rotationFrequency: countRotationAttempts() / Math.max(pieceInteractions.length, 1),
      systematicApproach: problemSolvingApproach === 'systematic'
    }
  }

  // 🧩 Registrar interação com peça para análise cognitiva
  const recordPieceInteraction = async (pieceId, position, action, correct) => {
    const interactionData = {
      pieceId,
      position,
      action,
      correct,
      timestamp: Date.now(),
      attemptNumber: attemptCount + 1,
      difficultyLevel: difficulty,
      sessionTime: Date.now() - (sessionStartTime || Date.now())
    }

    // Adicionar à lista de interações
    setPieceInteractions(prev => [...prev, interactionData])
    
    // Coletar dados com os coletores especializados
    try {
      await collectorsHub.collectMoveData({
        ...interactionData,
        puzzleState: {
          totalPieces: puzzlePieces.length,
          placedPieces: placedPieces.length,
          completionPercentage: (placedPieces.filter(p => p !== null).length / puzzlePieces.length) * 100
        }
      });
      
      // 🔄 Registrar interação multissensorial
      await recordMultisensoryInteraction('game_interaction', {
        interactionType: 'user_action',
        gameSpecificData: interactionData,
        multisensoryProcessing: {
          spatialProcessing: { spatialReasoning: 0.7, visualSpatialMemory: 0.7, spatialOrientation: 0.7 },
          cognitiveProcessing: { problemSolving: 0.7, processingSpeed: 0.7, adaptability: 0.7 },
          behavioralProcessing: { interactionCount: 0.7, averageResponseTime: 0.7, persistence: 0.7 }
        }
      });

      // A cada 3 tentativas, fazer análise cognitiva completa
      const newAttemptCount = attemptCount + 1
      setAttemptCount(newAttemptCount)

      if (newAttemptCount % 3 === 0) {
        performCognitiveAnalysis()
      }

    } catch (error) {
      console.error('Erro ao coletar dados da interação:', error)
    }
  }

  // 🧠 Realizar análise cognitiva completa
  const performCognitiveAnalysis = async () => {
    try {
      setCognitiveAnalysisVisible(true)
      
      const analysisData = await collectorsHub.collectComprehensiveData({
        sessionId: sessionStartTime?.toString() || 'session',
        gameState: {
          difficulty,
          currentLevel: gameStats.level,
          score: gameStats.score,
          accuracy: getAccuracy(),
          isComplete
        },
        interactions: pieceInteractions,
        spatialData: {
          strategies: spatialStrategies,
          approach: problemSolvingApproach
        }
      })

      setAnalysisResults(analysisData)

      // Manter visível por 3 segundos
      setTimeout(() => {
        setCognitiveAnalysisVisible(false)
      }, 3000)

    } catch (error) {
      console.error('Erro na análise cognitiva:', error)
      setCognitiveAnalysisVisible(false)
    }
  }

  // Gerar novo quebra-cabeça
  const generateNewPuzzle = useCallback(() => {
    const randomEmotion = QuebraCabecaConfig.emotions[Math.floor(Math.random() * QuebraCabecaConfig.emotions.length)]
    const difficultyData = QuebraCabecaConfig.difficulties.find(d => d.id === difficulty)
    
    // Verificar se difficultyData existe
    if (!difficultyData) {
      console.error('Difficulty data not found for:', difficulty)
      return
    }
    
    setCurrentEmotion(randomEmotion)
    setIsComplete(false)
    setFeedback(null)
    setGameStats(prev => ({ ...prev, totalAttempts: prev.totalAttempts + 1 }))

    // Criar peças com posições corretas definidas
    const pieces = randomEmotion.pieces.slice(0, difficultyData.pieces).map((piece, index) => ({
      id: index,
      content: piece,
      placed: false,
      correctPosition: index // Posição correta da peça
    }))
    
    // Embaralhar as peças para o usuário
    setPuzzlePieces(pieces.sort(() => Math.random() - 0.5))
    setPlacedPieces(Array(difficultyData.pieces).fill(null))
  }, [difficulty]);

  // Iniciar o jogo
  const startGame = useCallback(async (selectedDifficulty) => {
    // Configurar dificuldade se fornecida
    if (selectedDifficulty) {
      setDifficulty(selectedDifficulty);
    }

    // Esconder tela inicial
    setShowStartScreen(false);
    setGameStarted(true)
    setGameStats({
      level: 1,
      score: 0,
      completed: 0,
      totalAttempts: 0
    });

    // 🔄 Inicializar integração multissensorial
    try {
      await initMultisensory(`session_${Date.now()}`, {
        difficulty: difficulty,
        gameMode: 'puzzle_solving',
        userId: user?.id || 'anonymous'
      });
    } catch (error) {
      console.warn('⚠️ Erro ao inicializar sessão multissensorial:', error);
    }

    generateNewPuzzle()
  }, [initMultisensory, generateNewPuzzle, user]);

  // Toggle TTS
  const toggleTTS = useCallback(() => {
    setTtsActive(prev => {
      const newState = !prev;
      localStorage.setItem('quebraCabeca_ttsActive', JSON.stringify(newState));
      if (!newState && 'speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
      return newState;
    });
  }, []);

  // Lidar com início do drag
  const handleDragStart = useCallback((piece) => {
    setDraggedPiece(piece)
  }, [])

  // Lidar com quebra-cabeça completo
  const handlePuzzleComplete = useCallback(async () => {
    setIsComplete(true)
    
    const points = QuebraCabecaConfig.gameSettings.pointsByDifficulty[difficulty.toUpperCase()] || 10
    setGameStats(prev => ({
      ...prev,
      completed: prev.completed + 1,
      score: prev.score + points,
      level: prev.level + 1
    }))

    const message = QuebraCabecaConfig.encouragingMessages[Math.floor(Math.random() * QuebraCabecaConfig.encouragingMessages.length)]
    
    // Feedback sonoro ao completar (substituindo feedback visual)
    speak(`${message} Você ganhou ${points} pontos!`);

    // 🧠 Coletar métricas completas ao completar o quebra-cabeça
    try {
      await collectPuzzleMetrics()
      console.log('🧩 Métricas do quebra-cabeça coletadas com sucesso!')
    } catch (error) {
      console.error('❌ Erro ao coletar métricas do quebra-cabeça:', error)
    }
  }, [difficulty, speak, collectPuzzleMetrics])

  // Lidar com drop
  const handleDrop = useCallback((targetIndex) => {
    if (!draggedPiece) return

    const newPlacedPieces = [...placedPieces]
    newPlacedPieces[targetIndex] = draggedPiece

    // Verificar se o posicionamento está correto
    const isCorrectPlacement = draggedPiece.correctPosition === targetIndex
    
    // 🧠 Registrar interação com métricas avançadas
    recordPieceInteraction(
      draggedPiece.id, 
      { x: targetIndex % 2, y: Math.floor(targetIndex / 2) }, 
      'place', 
      isCorrectPlacement
    )

    setPlacedPieces(newPlacedPieces)
    setPuzzlePieces(prev => prev.filter(p => p.id !== draggedPiece.id))
    setDraggedPiece(null)

    // Feedback sonoro para o usuário (substituindo feedback visual)
    if (isCorrectPlacement) {
      speak('Peça colocada corretamente!');
    } else {
      speak('Tente uma posição diferente!');
      // Atualizar estratégia de resolução para trial-error
      setProblemSolvingApproach('trial_error')
    }

    // Verificar se completou
    if (newPlacedPieces.every(piece => piece !== null)) {
      handlePuzzleComplete()
    }
  }, [draggedPiece, placedPieces, recordPieceInteraction, speak, setProblemSolvingApproach, handlePuzzleComplete]);

  // Função para remover peça do quebra-cabeça
  const handleRemovePiece = useCallback((slotIndex) => {
    if (slotIndex < 0 || slotIndex >= placedPieces.length || !placedPieces[slotIndex]) return;

    const removedPiece = placedPieces[slotIndex];
    const newPlacedPieces = [...placedPieces];
    newPlacedPieces[slotIndex] = null;

    setPlacedPieces(newPlacedPieces);
    setPuzzlePieces(prev => [...prev, removedPiece]);

    // Feedback sonoro
    playSound('pop');
    speak('Peça removida');
  }, [placedPieces, playSound, speak]);

  // Próximo quebra-cabeça
  const nextPuzzle = () => {
    // Resetar métricas para nova rodada
    setPieceInteractions([])
    setSpatialStrategies([])
    setProblemSolvingApproach('systematic')
    
    setTimeout(() => {
      generateNewPuzzle()
    }, 100)
  }

  // Função para finalizar sessão e coletar métricas finais
  const handleGameEnd = async () => {
    if (sessionStartTime && pieceInteractions.length > 0) {
      try {
        await collectPuzzleMetrics()
        console.log('🎯 Métricas finais do quebra-cabeça coletadas!')
      } catch (error) {
        console.error('❌ Erro ao coletar métricas finais:', error)
      }
    }
    
    if (onBack) {
      onBack()
    }
  }


  // Tela de início padronizada
  if (showStartScreen) {
    console.log('🧩 QuebraCabeca: Renderizando tela de início padronizada');
    return (
      <GameStartScreen
        gameTitle="Quebra-cabeça Emocional"
        gameDescription="Desenvolva inteligência emocional através de puzzles interativos"
        gameIcon="🧩"
        onStart={startGame}
        onBack={onBack}
        difficulties={[
          { id: 'easy', name: 'Fácil', description: 'Peças grandes\nIdeal para iniciantes', icon: '😊' },
          { id: 'medium', name: 'Médio', description: 'Peças médias\nDesafio equilibrado', icon: '🎯' },
          { id: 'hard', name: 'Avançado', description: 'Peças pequenas\nPara especialistas', icon: '🚀' }
        ]}
      />
    );
  }
  return (
    <div 
      className={`${styles.quebraCabecaGame} ${settings.reducedMotion ? 'reduced-motion' : ''} ${settings.highContrast ? 'high-contrast' : ''}`}
      data-font-size={settings.fontSize}
      data-theme={settings.colorScheme}
      style={{
        fontSize: settings.fontSize === 'small' ? '0.875rem' : 
                 settings.fontSize === 'large' ? '1.25rem' : '1rem'
      }}
    >
      {/* 🧠 Indicador de Análise Cognitiva */}
      {cognitiveAnalysisVisible && (
        <div className="cognitive-analysis-indicator">
          <div className="analysis-content">
            <div className="analysis-icon">🎯🧠</div>
            <div className="analysis-text">
              <div className="analysis-title">Análise Espacial em Progresso</div>
              <div className="analysis-details">
                Avaliando raciocínio espacial e estratégias de resolução...
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 📊 Painel de Insights Cognitivos */}
      {analysisResults && (
        <div className="cognitive-insights-panel">
          <div className="insights-header">
            <span className="insights-icon">🎯</span>
            <span className="insights-title">Análise Cognitiva</span>
          </div>
          <div className="insights-content">
            <div className="insight-item">
              <span className="insight-label">Raciocínio Espacial:</span>
              <span className="insight-value">{analysisResults.spatialReasoning || 'Em análise'}</span>
            </div>
            <div className="insight-item">
              <span className="insight-label">Estratégia:</span>
              <span className="insight-value">{analysisResults.strategy || problemSolvingApproach}</span>
            </div>
            <div className="insight-item">
              <span className="insight-label">Padrão Cognitivo:</span>
              <span className="insight-value">{analysisResults.cognitivePattern || 'Identificando...'}</span>
            </div>
          </div>
        </div>
      )}

      <div className={styles.gameContent}>
        {/* Header do jogo - PADRÃO LETTERRECOGNITION EXATO */}
        <div className={styles.gameHeader}>
          <h1 className={styles.gameTitle}>
            🧩 Quebra-Cabeça Emocional V3
            <div style={{ fontSize: '0.7rem', opacity: 0.8, marginTop: '0.25rem' }}>
              {ACTIVITY_TYPES[gameState.currentActivity.toUpperCase()]?.name || 'Quebra-cabeça Emocional'}
            </div>
          </h1>
          <button
            className={`${styles.headerTtsButton} ${ttsActive ? styles.ttsActive : ''}`}
            onClick={toggleTTS}
            title={ttsActive ? 'Desativar TTS' : 'Ativar TTS'}
            aria-label={ttsActive ? 'Desativar TTS' : 'Ativar TTS'}
          >
            {ttsActive ? '🔊' : '🔇'}
          </button>
        </div>

        {/* Header com estatísticas - PADRÃO LETTERRECOGNITION EXATO */}
        <div className={styles.gameStats}>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.gameStats.score}</div>
            <div className={styles.statLabel}>Pontos</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.round}</div>
            <div className={styles.statLabel}>Rodada</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.gameStats.accuracy}%</div>
            <div className={styles.statLabel}>Precisão</div>
          </div>
        </div>

        {/* Menu de atividades - PADRÃO LETTERRECOGNITION EXATO */}
        <div className={styles.activityMenu}>
          {Object.values(ACTIVITY_TYPES).map((activity) => (
            <button
              key={activity.id}
              className={`${styles.activityButton} ${
                gameState.currentActivity === activity.id ? styles.active : ''
              }`}
              onClick={() => switchActivity(activity.id)}
            >
              <span>{activity.icon}</span>
              <span>{activity.name}</span>
            </button>
          ))}
        </div>

        {/* Renderização da atividade atual - PADRÃO LETTERRECOGNITION EXATO */}
        {gameState.currentActivity === ACTIVITY_TYPES.EMOTION_PUZZLE.id && renderEmotionPuzzle()}
        {gameState.currentActivity === ACTIVITY_TYPES.PIECE_ROTATION.id && renderPieceRotation()}
        {gameState.currentActivity === ACTIVITY_TYPES.PATTERN_PUZZLE.id && renderPatternPuzzle()}
        {gameState.currentActivity === ACTIVITY_TYPES.EMOTION_MATCHING.id && renderEmotionMatching()}
        {gameState.currentActivity === ACTIVITY_TYPES.CREATIVE_ASSEMBLY.id && renderCreativeAssembly()}

        {/* Controles do jogo - PADRÃO LETTERRECOGNITION EXATO */}
        <div className={styles.gameControls}>
          <button className={styles.controlButton} onClick={() => speak('Quebra-cabeça emocional. Monte as peças para formar emoções e desenvolver inteligência emocional.')}>
            🔊 Explicar
          </button>
          <button className={styles.controlButton} onClick={() => speak('Repita as instruções da atividade atual.')}>
            🔄 Repetir
          </button>
          <button className={styles.controlButton} onClick={() => speak('Teste de acessibilidade ativado.')}>
            🧪 Teste TTS
          </button>
          <button className={styles.controlButton} onClick={nextPuzzle}>
            🔄 Reiniciar
          </button>
          <button className={styles.controlButton} onClick={onBack}>
            ⬅️ Voltar
          </button>
        </div>
      </div>
    </div>
  )
}

export default QuebraCabecaGame
