/**
 * 👥 Portal Betina V3 - Gerenciamento de Cadastros
 * Painel administrativo para aprovar/rejeitar cadastros de usuários
 */

import React, { useState, useEffect } from 'react'
import { PRICING_PLANS, APPROVAL_STATUS, APPROVAL_MESSAGES } from '../../../../config/pricingPlans.js'
import styles from './RegistrationManagement.module.css'

const RegistrationManagement = () => {
  const [registrations, setRegistrations] = useState([])
  const [loading, setLoading] = useState(true)
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [selectedRegistration, setSelectedRegistration] = useState(null)
  const [actionLoading, setActionLoading] = useState(false)
  const [summary, setSummary] = useState({})

  // Carregar lista de cadastros
  const loadRegistrations = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/auth/registration/admin/list', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        setRegistrations(data.registrations || [])
        setSummary(data.summary || {})
      } else {
        console.error('Erro ao carregar cadastros:', response.statusText)
      }
    } catch (error) {
      console.error('Erro ao carregar cadastros:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadRegistrations()
  }, [])

  // Filtrar cadastros por status
  const filteredRegistrations = registrations.filter(reg => 
    selectedStatus === 'all' || reg.status === selectedStatus
  )

  // Aprovar cadastro
  const approveRegistration = async (registrationId, adminNotes = '') => {
    setActionLoading(true)
    try {
      const response = await fetch(`/api/auth/registration/admin/approve/${registrationId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify({ adminNotes })
      })

      if (response.ok) {
        await loadRegistrations() // Recarregar lista
        setSelectedRegistration(null)
        alert('Cadastro aprovado com sucesso!')
      } else {
        const error = await response.json()
        alert(`Erro ao aprovar: ${error.message}`)
      }
    } catch (error) {
      console.error('Erro ao aprovar cadastro:', error)
      alert('Erro ao aprovar cadastro')
    } finally {
      setActionLoading(false)
    }
  }

  // Rejeitar cadastro
  const rejectRegistration = async (registrationId, reason, adminNotes = '') => {
    setActionLoading(true)
    try {
      const response = await fetch(`/api/auth/registration/admin/reject/${registrationId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify({ reason, adminNotes })
      })

      if (response.ok) {
        await loadRegistrations() // Recarregar lista
        setSelectedRegistration(null)
        alert('Cadastro rejeitado')
      } else {
        const error = await response.json()
        alert(`Erro ao rejeitar: ${error.message}`)
      }
    } catch (error) {
      console.error('Erro ao rejeitar cadastro:', error)
      alert('Erro ao rejeitar cadastro')
    } finally {
      setActionLoading(false)
    }
  }

  // Formatar data
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('pt-BR')
  }

  // Obter cor do status
  const getStatusColor = (status) => {
    const colors = {
      [APPROVAL_STATUS.PENDING]: '#f59e0b',
      [APPROVAL_STATUS.PAYMENT_PENDING]: '#3b82f6',
      [APPROVAL_STATUS.APPROVED]: '#10b981',
      [APPROVAL_STATUS.REJECTED]: '#ef4444',
      [APPROVAL_STATUS.EXPIRED]: '#6b7280'
    }
    return colors[status] || '#6b7280'
  }

  // Renderizar modal de detalhes
  const renderDetailsModal = () => {
    if (!selectedRegistration) return null

    const plan = PRICING_PLANS[selectedRegistration.selectedPlan]

    return (
      <div className={styles.modalOverlay}>
        <div className={styles.modal}>
          <div className={styles.modalHeader}>
            <h3>Detalhes do Cadastro</h3>
            <button 
              onClick={() => setSelectedRegistration(null)}
              className={styles.closeButton}
            >
              ✕
            </button>
          </div>

          <div className={styles.modalContent}>
            <div className={styles.section}>
              <h4>Dados Pessoais</h4>
              <div className={styles.infoGrid}>
                <div><strong>Nome:</strong> {selectedRegistration.firstName} {selectedRegistration.lastName}</div>
                <div><strong>Email:</strong> {selectedRegistration.email}</div>
                <div><strong>Telefone:</strong> {selectedRegistration.phone}</div>
                <div><strong>CPF:</strong> {selectedRegistration.cpf}</div>
              </div>
            </div>

            <div className={styles.section}>
              <h4>Dados Profissionais</h4>
              <div className={styles.infoGrid}>
                <div><strong>Profissão:</strong> {selectedRegistration.profession}</div>
                <div><strong>Instituição:</strong> {selectedRegistration.institution || 'Não informado'}</div>
                <div><strong>Registro:</strong> {selectedRegistration.registration || 'Não informado'}</div>
                <div><strong>Experiência:</strong> {selectedRegistration.experience || 'Não informado'}</div>
              </div>
            </div>

            <div className={styles.section}>
              <h4>Uso Pretendido</h4>
              <div className={styles.infoGrid}>
                <div><strong>Finalidade:</strong> {selectedRegistration.intendedUse}</div>
                <div><strong>Número de Usuários:</strong> {selectedRegistration.numberOfUsers}</div>
              </div>
            </div>

            <div className={styles.section}>
              <h4>Plano Selecionado</h4>
              <div className={styles.planInfo}>
                <div className={styles.planName}>{plan?.name}</div>
                <div className={styles.planPrice}>R$ {plan?.price.toFixed(2)}/{plan?.period}</div>
                <div className={styles.planDescription}>{plan?.description}</div>
              </div>
            </div>

            <div className={styles.section}>
              <h4>Status e Datas</h4>
              <div className={styles.infoGrid}>
                <div><strong>Status:</strong> 
                  <span 
                    className={styles.statusBadge}
                    style={{ backgroundColor: getStatusColor(selectedRegistration.status) }}
                  >
                    {APPROVAL_MESSAGES[selectedRegistration.status]?.title}
                  </span>
                </div>
                <div><strong>Criado em:</strong> {formatDate(selectedRegistration.createdAt)}</div>
                <div><strong>Atualizado em:</strong> {formatDate(selectedRegistration.updatedAt)}</div>
              </div>
            </div>

            {selectedRegistration.payment && (
              <div className={styles.section}>
                <h4>Informações de Pagamento</h4>
                <div className={styles.infoGrid}>
                  <div><strong>ID Pagamento:</strong> {selectedRegistration.payment.id}</div>
                  <div><strong>Valor:</strong> R$ {selectedRegistration.payment.amount.toFixed(2)}</div>
                  <div><strong>Status:</strong> {selectedRegistration.payment.status}</div>
                  {selectedRegistration.payment.confirmedAt && (
                    <div><strong>Confirmado em:</strong> {formatDate(selectedRegistration.payment.confirmedAt)}</div>
                  )}
                </div>
              </div>
            )}
          </div>

          {selectedRegistration.status === APPROVAL_STATUS.PENDING && (
            <div className={styles.modalActions}>
              <button
                onClick={() => {
                  const notes = prompt('Notas administrativas (opcional):')
                  if (notes !== null) {
                    approveRegistration(selectedRegistration.id, notes)
                  }
                }}
                disabled={actionLoading}
                className={styles.approveButton}
              >
                {actionLoading ? '⏳' : '✅'} Aprovar
              </button>
              
              <button
                onClick={() => {
                  const reason = prompt('Motivo da rejeição:')
                  if (reason) {
                    const notes = prompt('Notas administrativas (opcional):')
                    rejectRegistration(selectedRegistration.id, reason, notes || '')
                  }
                }}
                disabled={actionLoading}
                className={styles.rejectButton}
              >
                {actionLoading ? '⏳' : '❌'} Rejeitar
              </button>
            </div>
          )}
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Carregando cadastros...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2>Gerenciamento de Cadastros</h2>
        <button onClick={loadRegistrations} className={styles.refreshButton}>
          🔄 Atualizar
        </button>
      </div>

      {/* Resumo */}
      <div className={styles.summary}>
        <div className={styles.summaryCard}>
          <div className={styles.summaryNumber}>{summary.pending || 0}</div>
          <div className={styles.summaryLabel}>Pendentes</div>
        </div>
        <div className={styles.summaryCard}>
          <div className={styles.summaryNumber}>{summary.paymentPending || 0}</div>
          <div className={styles.summaryLabel}>Aguardando Pagamento</div>
        </div>
        <div className={styles.summaryCard}>
          <div className={styles.summaryNumber}>{summary.approved || 0}</div>
          <div className={styles.summaryLabel}>Aprovados</div>
        </div>
        <div className={styles.summaryCard}>
          <div className={styles.summaryNumber}>{summary.rejected || 0}</div>
          <div className={styles.summaryLabel}>Rejeitados</div>
        </div>
      </div>

      {/* Filtros */}
      <div className={styles.filters}>
        <select 
          value={selectedStatus} 
          onChange={(e) => setSelectedStatus(e.target.value)}
          className={styles.statusFilter}
        >
          <option value="all">Todos os Status</option>
          <option value={APPROVAL_STATUS.PENDING}>Pendentes</option>
          <option value={APPROVAL_STATUS.PAYMENT_PENDING}>Aguardando Pagamento</option>
          <option value={APPROVAL_STATUS.APPROVED}>Aprovados</option>
          <option value={APPROVAL_STATUS.REJECTED}>Rejeitados</option>
        </select>
      </div>

      {/* Lista de cadastros */}
      <div className={styles.registrationsList}>
        {filteredRegistrations.length === 0 ? (
          <div className={styles.emptyState}>
            <p>Nenhum cadastro encontrado</p>
          </div>
        ) : (
          filteredRegistrations.map(registration => (
            <div key={registration.id} className={styles.registrationCard}>
              <div className={styles.cardHeader}>
                <div className={styles.userInfo}>
                  <h4>{registration.firstName} {registration.lastName}</h4>
                  <p>{registration.email}</p>
                </div>
                <span 
                  className={styles.statusBadge}
                  style={{ backgroundColor: getStatusColor(registration.status) }}
                >
                  {APPROVAL_MESSAGES[registration.status]?.title}
                </span>
              </div>

              <div className={styles.cardContent}>
                <div className={styles.cardInfo}>
                  <span><strong>Profissão:</strong> {registration.profession}</span>
                  <span><strong>Plano:</strong> {PRICING_PLANS[registration.selectedPlan]?.name}</span>
                  <span><strong>Criado:</strong> {formatDate(registration.createdAt)}</span>
                </div>
              </div>

              <div className={styles.cardActions}>
                <button
                  onClick={() => setSelectedRegistration(registration)}
                  className={styles.detailsButton}
                >
                  👁️ Ver Detalhes
                </button>
                
                {registration.status === APPROVAL_STATUS.PENDING && (
                  <>
                    <button
                      onClick={() => approveRegistration(registration.id)}
                      disabled={actionLoading}
                      className={styles.quickApproveButton}
                    >
                      ✅ Aprovar
                    </button>
                    <button
                      onClick={() => {
                        const reason = prompt('Motivo da rejeição:')
                        if (reason) {
                          rejectRegistration(registration.id, reason)
                        }
                      }}
                      disabled={actionLoading}
                      className={styles.quickRejectButton}
                    >
                      ❌ Rejeitar
                    </button>
                  </>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      {renderDetailsModal()}
    </div>
  )
}

export default RegistrationManagement
