/**
 * @file styles.module.css
 * @description Estilos modulares responsivos para o Performance Dashboard
 * @version 4.0.0 - Layout Moderno e Responsivo
 */

/* ========== VARIÁVEIS CSS ========== */
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);

  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-light: #e2e8f0;

  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
}

/* ========== MULTISENSORY METRICS PANEL ========== */
.metricsPanelRoot {
  padding: 2rem;
  margin-bottom: 2rem;
  position: relative;
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.metricsPanelRoot::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.metricsPanelRoot:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.metricsPanelRoot:hover::before {
  opacity: 1;
}

.metricsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.metricsTitle {
  font-size: 1.5rem;
  font-weight: 800;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  letter-spacing: -0.025em;
}

.metricsTitle::before {
  content: '🎯';
  font-size: 1.25rem;
  padding: 0.5rem;
  background: var(--primary-gradient);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
}

.metricsContent {
  min-height: 400px;
  position: relative;
}

.metricsEmptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary);
  text-align: center;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  border-radius: var(--radius-lg);
  border: 2px dashed var(--border-light);
}

.metricsEmptyState::before {
  content: '📊';
  font-size: 4rem;
  opacity: 0.3;
  margin-bottom: 1rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.05); }
}

.metricsChart {
  margin: 2rem 0;
  height: 400px;
  position: relative;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: 1rem;
  box-shadow: var(--shadow-sm);
}

/* ========== LOADING E ESTADOS ========== */
.metricsLoadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: var(--radius-xl);
}

.metricsSpinner {
  width: 3rem;
  height: 3rem;
  border: 4px solid var(--border-light);
  border-top: 4px solid transparent;
  border-radius: 50%;
  background: conic-gradient(from 0deg, transparent, var(--primary-gradient));
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.metricsLoadingText {
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.875rem;
}

.metricsInfoBox {
  padding: 1.5rem;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  border-radius: var(--radius-lg);
  margin-top: 1.5rem;
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
}

/* ========== SISTEMA DE TABS MODERNO ========== */
.metricsTabs {
  display: flex;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: 0.25rem;
  margin-bottom: 2rem;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.metricsTabs::-webkit-scrollbar {
  display: none;
}

.metricsTab {
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--text-secondary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.metricsTab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.metricsTab:hover {
  color: var(--text-primary);
  transform: translateY(-1px);
}

.metricsTab.active {
  color: white;
  background: var(--primary-gradient);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.metricsTab.active::before {
  opacity: 1;
}

/* ========== GRID DE MÉTRICAS ========== */
.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.metricCard {
  padding: 1.5rem;
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  text-align: center;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.metricCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.metricCard:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.metricCard:hover::before {
  opacity: 1;
}

.metricValue {
  font-size: 2rem;
  font-weight: 800;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.metricLabel {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metricsButton {
  padding: 0.75rem 1.5rem;
  background: var(--primary-gradient);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.metricsButton:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.metricsButton:active {
  transform: translateY(0);
}

.metricsButtonSecondary {
  padding: 8px 16px;
  background-color: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
}

.metricsButtonSecondary:hover {
  background-color: #f1f5f9;
}

.icon {
  font-size: 18px;
  margin-right: 8px;
}

.metricsDivider {
  height: 1px;
  background-color: #e2e8f0;
  margin: 16px 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Container principal do dashboard */
.dashboardBase {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  position: relative;
  padding: 2rem;
  z-index: 1;
}

/* Header do dashboard */
.dashboardHeader {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dashboardTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #fff, #f0f9ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.dashboardSubtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 1.5rem;
  font-weight: 300;
}

/* Grid de conteúdo */
.dashboardContent {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.dashboardCard {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.dashboardCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
}

/* Estatísticas */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.statCard {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.statValue {
  font-size: 2rem;
  font-weight: bold;
  color: #4ECDC4;
  margin-bottom: 0.5rem;
}

.statLabel {
  font-size: 0.9rem;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Gráficos */
.chartsSection {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.chartContainer {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 1rem;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.chartTitle {
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  color: white;
  text-align: center;
}

.chartWrapper {
  height: 300px;
  position: relative;
}

/* Controles */
.dashboardControls {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.controlButton {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.controlButton:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.controlButton.active {
  background: linear-gradient(135deg, #10b981, #047857);
  border-color: #10b981;
}

/* Estados */
.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  flex-direction: column;
  gap: 1rem;
}

.errorContainer {
  background: rgba(220, 38, 38, 0.1);
  border: 1px solid rgba(220, 38, 38, 0.3);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  margin: 2rem 0;
}

.errorMessage {
  color: #fca5a5;
  margin-bottom: 1rem;
}

.retryButton {
  background: #dc2626;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retryButton:hover {
  background: #b91c1c;
}

/* Headers de cards */
.cardHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.cardTitle {
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  margin: 0;
}

.cardIcon {
  font-size: 1.5rem;
  opacity: 0.8;
}

/* Responsividade */
@media (max-width: 768px) {
  .dashboardBase {
    padding: 1rem;
  }
  
  .dashboardTitle {
    font-size: 2rem;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .dashboardContent {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .chartsSection {
    grid-template-columns: 1fr;
  }
  
  .statsGrid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
  
  .dashboardControls {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .dashboardBase {
    padding: 0.5rem;
  }
  
  .dashboardHeader {
    padding: 1rem;
  }
  
  .dashboardTitle {
    font-size: 1.5rem;
  }
  
  .dashboardCard {
    padding: 1rem;
  }
}

/* ========== RESPONSIVIDADE MULTISENSORY PANEL ========== */

/* Tablets e telas médias */
@media (max-width: 1024px) {
  .metricsPanelRoot {
    padding: 1.5rem;
  }

  .metricsGrid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.25rem;
  }

  .metricsChart {
    height: 350px;
  }
}

/* Tablets pequenos */
@media (max-width: 768px) {
  .metricsPanelRoot {
    padding: 1.25rem;
    margin-bottom: 1.5rem;
  }

  .metricsHeader {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
    text-align: center;
  }

  .metricsTitle {
    font-size: 1.25rem;
    justify-content: center;
  }

  .metricsTabs {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .metricsTab {
    flex: 1;
    min-width: 120px;
    text-align: center;
  }

  .metricsGrid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
  }

  .metricsChart {
    height: 300px;
    margin: 1.5rem 0;
  }

  .metricsEmptyState {
    padding: 2rem;
  }
}

/* Dispositivos móveis */
@media (max-width: 480px) {
  .metricsPanelRoot {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: var(--radius-lg);
  }

  .metricsTitle {
    font-size: 1.125rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .metricsTitle::before {
    font-size: 1rem;
    padding: 0.375rem;
  }

  .metricsTabs {
    padding: 0.125rem;
    gap: 0.25rem;
  }

  .metricsTab {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    min-width: 100px;
  }

  .metricsGrid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .metricCard {
    padding: 1.25rem;
  }

  .metricValue {
    font-size: 1.75rem;
  }

  .metricsChart {
    height: 250px;
    margin: 1rem 0;
    padding: 0.75rem;
  }

  .metricsEmptyState {
    padding: 1.5rem;
  }

  .metricsEmptyState::before {
    font-size: 3rem;
  }

  .metricsButton {
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
  }
}

/* ========== ACESSIBILIDADE ========== */

/* Redução de movimento para acessibilidade */
@media (prefers-reduced-motion: reduce) {
  .metricsPanelRoot,
  .metricCard,
  .metricsTab,
  .metricsButton {
    transition: none;
    animation: none;
  }

  .metricsPanelRoot:hover,
  .metricCard:hover,
  .metricsTab:hover,
  .metricsButton:hover {
    transform: none;
  }

  .metricsEmptyState::before {
    animation: none;
  }

  .metricsSpinner {
    animation: spin 0.01s linear infinite;
  }
}

/* Alto contraste */
@media (prefers-contrast: high) {
  .metricsPanelRoot,
  .metricCard,
  .metricsInfoBox {
    border: 2px solid var(--text-primary);
  }

  .metricsTab.active {
    border: 2px solid white;
  }

  .metricsButton {
    border: 2px solid transparent;
  }
}

/* Tema escuro para multisensory */
@media (prefers-color-scheme: dark) {
  .metricsPanelRoot {
    background: rgba(30, 41, 59, 0.9);
    border-color: #334155;
  }

  .metricsEmptyState {
    background: rgba(51, 65, 85, 0.6);
    border-color: #475569;
  }

  .metricCard {
    background: rgba(51, 65, 85, 0.8);
    border-color: #475569;
  }

  .metricsInfoBox {
    background: rgba(51, 65, 85, 0.6);
    border-color: #475569;
  }

  .metricsTabs {
    background: rgba(15, 23, 42, 0.8);
  }
}
