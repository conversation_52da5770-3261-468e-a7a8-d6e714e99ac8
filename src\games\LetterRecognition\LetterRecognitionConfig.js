/**
 * 📚 LETTER RECOGNITION CONFIG - Portal Betina V3
 * Configurações centralizadas do jogo de reconhecimento de letras
 * PADRÃO DEFINITIVO PARA TODOS OS JOGOS
 */

// 🔤 BANCO DE LETRAS - Português Brasileiro
export const LETTERS_BANK = [
  { id: 'a', letter: 'A', sound: 'A', example: '🐝 Abelha', color: '#FF6B6B', difficulty: 1 },
  { id: 'b', letter: 'B', sound: 'Bê', example: '⚽ Bola', color: '#4ECDC4', difficulty: 1 },
  { id: 'c', letter: 'C', sound: 'Cê', example: '🏠 Casa', color: '#45B7D1', difficulty: 1 },
  { id: 'd', letter: 'D', sound: 'Dê', example: '🎲 Dado', color: '#FFA07A', difficulty: 1 },
  { id: 'e', letter: 'E', sound: 'É', example: '⭐ Estrela', color: '#98D8C8', difficulty: 2 },
  { id: 'f', letter: 'F', sound: 'Efe', example: '🌸 Flor', color: '#F7DC6F', difficulty: 2 },
  { id: 'g', letter: 'G', sound: 'Gê', example: '🐱 Gato', color: '#BB8FCE', difficulty: 2 },
  { id: 'h', letter: 'H', sound: 'Agá', example: '🏨 Hotel', color: '#85C1E9', difficulty: 3 },
  { id: 'i', letter: 'I', sound: 'I', example: '🏝️ Ilha', color: '#F8C471', difficulty: 3 },
  { id: 'j', letter: 'J', sound: 'Jota', example: '🌻 Jardim', color: '#82E0AA', difficulty: 3 },
  { id: 'k', letter: 'K', sound: 'Cá', example: '🥝 Kiwi', color: '#AED6F1', difficulty: 4 },
  { id: 'l', letter: 'L', sound: 'Ele', example: '🦁 Leão', color: '#F5B7B1', difficulty: 4 }
];

// 🎯 ATIVIDADES DO JOGO - NOMES REDUZIDOS
export const ACTIVITY_TYPES = {
  LETTER_SELECTION: {
    id: 'letter_selection',
    name: 'Letras',
    icon: '🔤',
    description: 'Seleção - Encontre a letra correta baseada no som e exemplo',
    minRounds: 4,
    maxRounds: 7,
    component: 'LetterSelectionActivity'
  },
  WORD_FORMATION: {
    id: 'word_formation',
    name: 'Formação',
    icon: '🔗',
    description: 'Formação - Monte palavras usando as letras aprendidas',
    minRounds: 4,
    maxRounds: 7,
    component: 'WordFormationActivity'
  },
  SEQUENCE_RECOGNITION: {
    id: 'sequence_recognition',
    name: 'Sequência',
    icon: '📝',
    description: 'Reconhecimento - Complete sequências alfabéticas',
    minRounds: 4,
    maxRounds: 7,
    component: 'SequenceRecognitionActivity'
  },
  VISUAL_DISCRIMINATION: {
    id: 'visual_discrimination',
    name: 'Discriminação',
    icon: '👁️',
    description: 'Visual - Identifique letras com formas similares',
    minRounds: 4,
    maxRounds: 7,
    component: 'VisualDiscriminationActivity'
  }
};

// 🎮 CONFIGURAÇÕES DE DIFICULDADE
export const DIFFICULTIES = {
  EASY: {
    id: 'easy',
    name: 'Fácil',
    description: 'Letras básicas\nPara iniciantes',
    icon: '🌱',
    letters: LETTERS_BANK.filter(l => l.difficulty <= 2),
    timeLimit: 30000, // 30 segundos
    attempts: 3,
    optionsCount: 3, // 3 opções de resposta
    hintsEnabled: true
  },
  MEDIUM: {
    id: 'medium',
    name: 'Médio',
    description: 'Mais letras\nPara praticantes',
    icon: '🌿',
    letters: LETTERS_BANK.filter(l => l.difficulty <= 3),
    timeLimit: 25000, // 25 segundos
    attempts: 2,
    optionsCount: 4, // 4 opções de resposta
    hintsEnabled: true
  },
  HARD: {
    id: 'hard',
    name: 'Avançado',
    description: 'Todas letras\nPara especialistas',
    icon: '🌳',
    letters: LETTERS_BANK,
    timeLimit: 20000, // 20 segundos
    attempts: 1,
    optionsCount: 5, // 5 opções de resposta
    hintsEnabled: false
  }
};

// 🏆 SISTEMA DE PONTUAÇÃO
export const SCORING_CONFIG = {
  correctAnswer: 100,
  timeBonus: 50,
  streakMultiplier: 1.5,
  perfectRoundBonus: 200,
  maxScore: 1000,
  penaltyPerMistake: -25
};

// 🎨 CONFIGURAÇÕES VISUAIS (Mobile/Tablet Vertical)
export const VISUAL_CONFIG = {
  layout: {
    orientation: 'portrait',
    targetDevice: ['mobile', 'tablet'],
    containerMaxWidth: '100vw',
    containerMaxHeight: '100vh'
  },
  
  letterCard: {
    minSize: '80px',
    maxSize: '120px',
    borderRadius: '12px',
    fontSize: {
      letter: '2.5rem',
      example: '1rem'
    }
  },
  
  colors: {
    primary: '#4A90E2',
    secondary: '#7ED321',
    success: '#50E3C2',
    error: '#D0021B',
    warning: '#F5A623',
    background: '#F8F9FA',
    cardBackground: '#FFFFFF'
  },
  
  animations: {
    cardFlip: 300,
    success: 500,
    error: 400,
    transition: 200
  }
};

// 🔊 CONFIGURAÇÕES DE ÁUDIO/TTS
export const AUDIO_CONFIG = {
  enableTTS: true,
  autoPlayInstructions: true,
  soundEffects: {
    correct: true,
    incorrect: true,
    complete: true,
    click: true
  },
  
  ttsSettings: {
    rate: 0.8,
    pitch: 1.1,
    volume: 0.8,
    lang: 'pt-BR'
  }
};

// 📊 CONFIGURAÇÕES DE MÉTRICAS
export const METRICS_CONFIG = {
  trackingEnabled: true,
  collectDetailedMetrics: true,
  sessionTimeout: 300000, // 5 minutos
  
  trackedEvents: [
    'letter_selected',
    'correct_answer',
    'incorrect_answer',
    'hint_used',
    'time_elapsed',
    'activity_completed',
    'round_completed'
  ]
};

// 🎯 CONFIGURAÇÃO PRINCIPAL DO JOGO
export const LetterRecognitionConfig = {
  gameId: 'letter-recognition',
  gameName: 'Reconhecimento de Letras',
  version: '3.0.0',
  
  // Referências às configurações
  letters: LETTERS_BANK,
  activities: ACTIVITY_TYPES,
  difficulties: DIFFICULTIES,
  scoring: SCORING_CONFIG,
  visual: VISUAL_CONFIG,
  audio: AUDIO_CONFIG,
  metrics: METRICS_CONFIG,
  
  // Configurações gerais
  minRoundsPerActivity: 4,
  maxRoundsPerActivity: 7,
  enableProgressSaving: true,
  enableOfflineMode: false,
  
  // Configurações terapêuticas
  therapeutic: {
    adaptiveDifficulty: true,
    personalizedFeedback: true,
    progressTracking: true,
    cognitiveAssessment: true
  }
};

// 🎯 BANCO DE PALAVRAS PARA FORMAÇÃO
export const WORD_BANK = {
  EASY: [
    { word: 'MAMA', emoji: '👩', meaning: 'Mamãe' },
    { word: 'PAPA', emoji: '👨', meaning: 'Papai' },
    { word: 'CASA', emoji: '🏠', meaning: 'Casa' },
    { word: 'BOLA', emoji: '⚽', meaning: 'Bola' }
  ],
  MEDIUM: [
    { word: 'GATO', emoji: '🐱', meaning: 'Gato' },
    { word: 'FADA', emoji: '🧚', meaning: 'Fada' },
    { word: 'CAFÉ', emoji: '☕', meaning: 'Café' },
    { word: 'FLOR', emoji: '🌸', meaning: 'Flor' },
    { word: 'PATO', emoji: '🦆', meaning: 'Pato' }
  ],
  HARD: [
    { word: 'JARDIM', emoji: '🌻', meaning: 'Jardim' },
    { word: 'LIVROS', emoji: '📚', meaning: 'Livros' },
    { word: 'ESCOLA', emoji: '🏫', meaning: 'Escola' },
    { word: 'AMIGOS', emoji: '👫', meaning: 'Amigos' },
    { word: 'FAMÍLIA', emoji: '👪', meaning: 'Família' },
    { word: 'ALEGRIA', emoji: '😊', meaning: 'Alegria' }
  ]
};

// 🎯 CONFIGURAÇÃO DE RODADAS
export const ROUNDS_CONFIG = {
  minRounds: 4,
  maxRounds: 7,
  difficultySettings: {
    easy: { rounds: 4, wordsCount: 4 },
    medium: { rounds: 5, wordsCount: 5 },
    hard: { rounds: 7, wordsCount: 6 }
  }
};
