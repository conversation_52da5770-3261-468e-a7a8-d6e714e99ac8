/**
 * 🔢 CONTAGEM DE NÚMEROS V4 - REESTRUTURADO
 * Portal Betina V3 - Jogo educativo estável seguindo padrões de sucesso
 * Baseado nos padrões dos jogos Musical Sequence e Color Match
 */

import React, { useState, useEffect, useCallback, useContext } from 'react';
import { ContagemNumerosConfig } from './ContagemNumerosConfig.js';
import { SystemContext } from '../../components/context/SystemContext.jsx';
import { useAccessibilityContext } from '../../components/context/AccessibilityContext';

// Importa o componente padrão de tela de dificuldade
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';

// Hook unificado para integração com backend
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic.js';

// 🧠 COLETORES AVANÇADOS - Análise cognitiva em tempo real
import { NumberCountingCollectorsHub } from './collectors/index.js';

// 🔄 Importar hook multissensorial
import { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js';

// 🔊 SISTEMA TTS PADRONIZADO - Portal Betina V3
import { useStandardTTS, StandardTTSButton, TTS_MESSAGES } from '../../components/shared/StandardTTS';

// Importa estilos modulares
import styles from './ContagemNumeros.module.css';

// 🎯 SISTEMA DE ATIVIDADES SIMPLIFICADO V4 - ESTÁVEL E INFANTIL
const ACTIVITY_TYPES = {
  VISUAL_COUNTING: { 
    id: 'visual_counting', 
    name: 'Contagem Visual', 
    description: 'Conte os objetos que você vê',
    icon: '👁️'
  },
  SIMPLE_ADDITION: { 
    id: 'simple_addition', 
    name: 'Soma Simples', 
    description: 'Some números pequenos',
    icon: '➕'
  },
  NUMBER_RECOGNITION: { 
    id: 'number_recognition', 
    name: 'Reconhecimento', 
    description: 'Encontre o número correto',
    icon: '🔍'
  },
  QUANTITY_COMPARISON: { 
    id: 'quantity_comparison', 
    name: 'Comparação', 
    description: 'Qual tem mais?',
    icon: '⚖️'
  }
};

//  COMPONENTE PRINCIPAL
const ContagemNumerosGame = ({ onBack }) => {
  const { user, sessionId } = useContext(SystemContext);

  // =====================================================
  // 🔊 SISTEMA TTS PADRONIZADO - Portal Betina V3
  // =====================================================
  const {
    ttsActive,
    toggleTTS,
    speak,
    stopSpeaking,
    speakGameInstructions,
    speakFeedback,
    speakProgress,
    speakGameEnd
  } = useStandardTTS('contagem_numeros');

  // Instruções específicas do jogo
  const gameInstructions = "Conte os objetos na tela e escolha o número correto. Use os botões para selecionar sua resposta.";

  // 🎯 ESTADO SIMPLIFICADO E ESTÁVEL - Baseado nos padrões de sucesso
  const [gameState, setGameState] = useState({
    status: 'start', // 'start', 'playing', 'finished'
    score: 0,
    round: 1,
    totalRounds: 4, // Usar sistema baseado na dificuldade (4-7 rodadas)
    difficulty: 'easy',
    accuracy: 100,
    roundStartTime: null,
    
    // Sistema de atividades controlado pelo usuário
    currentActivity: ACTIVITY_TYPES.VISUAL_COUNTING.id,
    userControlledActivities: true, // Usuário escolhe as atividades
    
    // Dados específicos de atividades - PERSISTENTES
    activityData: {
      visual_counting: {
        objects: [],
        correctCount: 0,
        options: [],
        instruction: ''
      },
      simple_addition: {
        number1: 0,
        number2: 0,
        correctAnswer: 0,
        options: [],
        instruction: ''
      },
      number_recognition: {
        targetNumber: 0,
        options: [],
        instruction: ''
      },
      quantity_comparison: {
        group1: [],
        group2: [],
        correctAnswer: '',
        instruction: ''
      }
    }
  });

  const [showStartScreen, setShowStartScreen] = useState(true);
  const [gameStarted, setGameStarted] = useState(false);
  const [feedback, setFeedback] = useState(null);

  // 🧠 COLETORES
  const [collectorsHub] = useState(() => new NumberCountingCollectorsHub());

  // 🎯 HOOKS INTEGRADOS
  const {
    startUnifiedSession,
    recordInteraction,
    metrics,
    isSessionActive,
    endUnifiedSession
  } = useUnifiedGameLogic('contagem-numeros');

  const {
    initMultisensory,
    multisensoryIntegration
  } = useMultisensoryIntegration(sessionId, {
    gameType: 'contagem_numeros',
    sensorTypes: {
      visual: true,
      haptic: true,
      tts: ttsActive,
      gestural: true,
      biometric: true
    },
    adaptiveMode: true,
    learningStyle: user?.profile?.learningStyle || 'visual'
  });

  // TTS automático ao iniciar o jogo
  useEffect(() => {
    if (gameState.status === 'playing' && ttsActive) {
      setTimeout(() => {
        speakGameInstructions('Contagem de Números', gameInstructions);
      }, 1000);
    }
  }, [gameState.status, ttsActive, speakGameInstructions, gameInstructions]);

  // 🎯 GERAR DADOS DA ATIVIDADE - ESTÁVEL E PERSISTENTE
  const generateActivityData = useCallback((activityId, difficulty) => {
    const config = ContagemNumerosConfig.gameSettings.simpleDifficultyConfigs[difficulty];
    
    switch (activityId) {
      case ACTIVITY_TYPES.VISUAL_COUNTING.id:
        return generateVisualCountingData(config);
      case ACTIVITY_TYPES.SIMPLE_ADDITION.id:
        return generateSimpleAdditionData(config);
      case ACTIVITY_TYPES.NUMBER_RECOGNITION.id:
        return generateNumberRecognitionData(config);
      case ACTIVITY_TYPES.QUANTITY_COMPARISON.id:
        return generateQuantityComparisonData(config);
      default:
        return generateVisualCountingData(config);
    }
  }, []);

  // 🎯 GERADOR DE CONTAGEM VISUAL - SIMPLES E ESTÁVEL
  const generateVisualCountingData = useCallback((config) => {
    const correctCount = Math.floor(Math.random() * (config.range[1] - config.range[0] + 1)) + config.range[0];
    const categories = ContagemNumerosConfig.categories;
    const randomCategory = categories[Math.floor(Math.random() * categories.length)];
    const randomObject = randomCategory.objects[Math.floor(Math.random() * randomCategory.objects.length)];
    
    // Criar objetos para exibição - ARRAY SIMPLES
    const objects = Array.from({ length: correctCount }, (_, index) => ({
      id: index,
      emoji: randomObject.emoji,
      name: randomObject.name
    }));
    
    // Criar opções de resposta
    const options = new Set([correctCount]);
    while (options.size < 4) {
      const wrongOption = Math.floor(Math.random() * config.maxNumber) + 1;
      if (wrongOption !== correctCount && wrongOption > 0) {
        options.add(wrongOption);
      }
    }
    
    return {
      objects,
      correctCount,
      options: Array.from(options).sort(() => Math.random() - 0.5),
      instruction: `Conte quantos ${randomObject.name.toLowerCase()}s você vê na tela`
    };
  }, []);

  // 🎯 GERADOR DE SOMA SIMPLES
  const generateSimpleAdditionData = useCallback((config) => {
    const number1 = Math.floor(Math.random() * config.range[1]) + 1;
    const number2 = Math.floor(Math.random() * (config.range[1] - number1)) + 1;
    const correctAnswer = number1 + number2;
    
    // Criar opções de resposta
    const options = new Set([correctAnswer]);
    while (options.size < 4) {
      const wrongOption = Math.floor(Math.random() * config.maxNumber) + 1;
      if (wrongOption !== correctAnswer && wrongOption > 0) {
        options.add(wrongOption);
      }
    }
    
    return {
      number1,
      number2,
      correctAnswer,
      options: Array.from(options).sort(() => Math.random() - 0.5),
      instruction: `Quanto é ${number1} + ${number2}?`
    };
  }, []);

  // 🎯 GERADOR DE RECONHECIMENTO DE NÚMERO - VERSÃO MELHORADA V2
  const generateNumberRecognitionData = useCallback((config) => {
    // Tipos de desafio variados
    const challengeTypes = ['visual_count', 'written_number', 'sequence', 'comparison', 'pattern'];
    let availableTypes = challengeTypes;
    
    // Filtrar tipos baseado na dificuldade
    if (gameState.difficulty === 'easy') {
      availableTypes = ['visual_count', 'written_number'];
    } else if (gameState.difficulty === 'medium') {
      availableTypes = ['visual_count', 'written_number', 'sequence', 'pattern'];
    }
    
    const challengeType = availableTypes[Math.floor(Math.random() * availableTypes.length)];
    
    switch (challengeType) {
      case 'visual_count': {
        // Mostrar objetos para contar
        const targetNumber = Math.floor(Math.random() * config.maxNumber) + 1;
        const categories = ContagemNumerosConfig.categories;
        const randomCategory = categories[Math.floor(Math.random() * categories.length)];
        const randomObject = randomCategory.objects[Math.floor(Math.random() * randomCategory.objects.length)];
        
        const objects = Array.from({ length: targetNumber }, (_, i) => ({
          id: i,
          emoji: randomObject.emoji,
          name: randomObject.name
        }));
        
        const options = generateNumberOptions(targetNumber, config.maxNumber);
        
        return {
          challengeType: 'visual_count',
          targetNumber,
          objects,
          options,
          instruction: `Conte os ${randomObject.name.toLowerCase()}s e encontre o número correto`
        };
      }
      
      case 'written_number': {
        // Número por extenso
        const targetNumber = Math.floor(Math.random() * config.maxNumber) + 1;
        const numberWords = ['', 'um', 'dois', 'três', 'quatro', 'cinco', 'seis', 'sete', 'oito', 'nove', 'dez'];
        const options = generateNumberOptions(targetNumber, config.maxNumber);
        
        return {
          challengeType: 'written_number',
          targetNumber,
          writtenNumber: numberWords[targetNumber] || targetNumber.toString(),
          options,
          instruction: `Encontre o número que representa: "${numberWords[targetNumber]}"`
        };
      }
      
      case 'sequence': {
        // Sequência numérica
        const targetNumber = Math.floor(Math.random() * (config.maxNumber - 1)) + 2; // mín 2 para ter antecessor
        const beforeNumber = targetNumber - 1;
        const options = generateNumberOptions(targetNumber, config.maxNumber);
        
        return {
          challengeType: 'sequence',
          targetNumber,
          beforeNumber,
          options,
          instruction: `Qual número vem depois de ${beforeNumber}?`
        };
      }
      
      case 'comparison': {
        // Comparação entre números
        const number1 = Math.floor(Math.random() * config.maxNumber) + 1;
        let number2 = Math.floor(Math.random() * config.maxNumber) + 1;
        while (number2 === number1) {
          number2 = Math.floor(Math.random() * config.maxNumber) + 1;
        }
        
        const targetNumber = Math.max(number1, number2);
        const options = generateNumberOptions(targetNumber, config.maxNumber);
        
        return {
          challengeType: 'comparison',
          targetNumber,
          number1,
          number2,
          options,
          instruction: `Qual é o maior: ${number1} ou ${number2}?`
        };
      }
      
      case 'pattern': {
        // Padrões visuais (pontos, dados, dedos)
        const targetNumber = Math.floor(Math.random() * Math.min(6, config.maxNumber)) + 1; // máx 6 para padrões de dados
        const patternTypes = ['dots', 'dice', 'fingers'];
        const patternType = patternTypes[Math.floor(Math.random() * patternTypes.length)];
        const options = generateNumberOptions(targetNumber, config.maxNumber);
        
        return {
          challengeType: 'pattern',
          targetNumber,
          patternType,
          options,
          instruction: `Quantos pontos você vê?`
        };
      }
      
      default: {
        // Fallback para o método original melhorado
        const targetNumber = Math.floor(Math.random() * config.maxNumber) + 1;
        const options = generateNumberOptions(targetNumber, config.maxNumber);
        
        return {
          challengeType: 'basic',
          targetNumber,
          options,
          instruction: `Encontre o número ${targetNumber}`
        };
      }
    }
  }, [gameState.difficulty]);
  
  // 🎯 FUNÇÃO AUXILIAR PARA GERAR OPÇÕES DE NÚMEROS
  const generateNumberOptions = useCallback((correctNumber, maxNumber) => {
    const options = new Set([correctNumber]);
    let attempts = 0;
    
    while (options.size < 4 && attempts < 20) {
      let wrongOption;
      
      // Gerar opções mais inteligentes (próximas ao número correto)
      if (Math.random() < 0.5) {
        // Números próximos (+/- 1 ou 2)
        const offset = (Math.random() < 0.5 ? -1 : 1) * (Math.floor(Math.random() * 2) + 1);
        wrongOption = correctNumber + offset;
      } else {
        // Números aleatórios
        wrongOption = Math.floor(Math.random() * maxNumber) + 1;
      }
      
      if (wrongOption > 0 && wrongOption <= maxNumber && wrongOption !== correctNumber) {
        options.add(wrongOption);
      }
      attempts++;
    }
    
    // Se ainda não temos 4 opções, completar com números aleatórios
    while (options.size < 4) {
      const wrongOption = Math.floor(Math.random() * maxNumber) + 1;
      if (wrongOption !== correctNumber) {
        options.add(wrongOption);
      }
    }
    
    return Array.from(options).sort(() => Math.random() - 0.5);
  }, []);

  // 🎯 GERADOR DE COMPARAÇÃO DE QUANTIDADE V2 - USANDO CONFIG.JS
  const generateQuantityComparisonData = useCallback((config) => {
    // Usar o gerador avançado do arquivo de configuração
    return ContagemNumerosConfig.generateQuantityComparisonData(config.difficulty || gameState.difficulty || 'easy');
  }, [gameState.difficulty]);

  // 🎯 INICIAR JOGO - SIMPLES E DIRETO
  const startGame = useCallback(async (selectedDifficulty) => {
    // Gerar dados iniciais da primeira atividade
    const initialData = generateActivityData(ACTIVITY_TYPES.VISUAL_COUNTING.id, selectedDifficulty);
    
    // 🎯 Definir número de rodadas baseado na dificuldade (4-7 rodadas)
    const roundsConfig = {
      easy: 4,
      medium: 5,
      hard: 7
    };
    const totalRounds = roundsConfig[selectedDifficulty] || 4;

    setGameState(prev => ({
      ...prev,
      status: 'playing',
      difficulty: selectedDifficulty,
      totalRounds: totalRounds,
      round: 1, // Resetar para rodada 1
      roundStartTime: Date.now(),
      activityData: {
        ...prev.activityData,
        [ACTIVITY_TYPES.VISUAL_COUNTING.id]: initialData
      }
    }));

    setShowStartScreen(false);
    setGameStarted(true);

    // Inicializar sistemas
    if (startUnifiedSession) {
      startUnifiedSession(selectedDifficulty);
    }

    try {
      if (initMultisensory && typeof initMultisensory === 'function') {
        await initMultisensory(sessionId || `session_${Date.now()}`, {
          difficulty: selectedDifficulty,
          gameMode: 'number_counting_v4',
          userId: user?.id || 'anonymous'
        });
      }
    } catch (error) {
      console.warn('⚠️ Erro ao inicializar sessão multissensorial:', error);
    }

    // TTS de boas-vindas padronizado
    setTimeout(() => {
      speakGameInstructions('Contagem de Números', `${initialData.instruction}`);
    }, ContagemNumerosConfig.gameSettings.initialTtsDelay);
  }, [generateActivityData, startUnifiedSession, initMultisensory, sessionId, user, speak]);

  // 🎯 PROCESSAR RESPOSTA - LÓGICA SIMPLES
  const handleAnswer = useCallback((answer) => {
    const currentActivityData = gameState.activityData[gameState.currentActivity];
    let isCorrect = false;
    let correctAnswer = null;

    switch (gameState.currentActivity) {
      case ACTIVITY_TYPES.VISUAL_COUNTING.id:
        correctAnswer = currentActivityData.correctCount;
        isCorrect = answer === correctAnswer;
        break;
      case ACTIVITY_TYPES.SIMPLE_ADDITION.id:
        correctAnswer = currentActivityData.correctAnswer;
        isCorrect = answer === correctAnswer;
        break;
      case ACTIVITY_TYPES.NUMBER_RECOGNITION.id:
        correctAnswer = currentActivityData.targetNumber;
        isCorrect = answer === correctAnswer;
        break;
      case ACTIVITY_TYPES.QUANTITY_COMPARISON.id:
        correctAnswer = currentActivityData.correctAnswer;
        isCorrect = answer === correctAnswer;
        break;
      default:
        isCorrect = false;
    }

    // Atualizar pontuação e precisão
    setGameState(prev => {
      const newScore = isCorrect ? prev.score + ContagemNumerosConfig.gameSettings.basePoints : prev.score;
      const newRound = prev.round + 1;
      const totalAttempts = prev.round;
      const correctAnswers = Math.floor(prev.score / ContagemNumerosConfig.gameSettings.basePoints) + (isCorrect ? 1 : 0);
      const newAccuracy = totalAttempts > 0 ? Math.round((correctAnswers / totalAttempts) * 100) : 100;

      return {
        ...prev,
        score: newScore,
        round: newRound,
        accuracy: newAccuracy
      };
    });

    // Feedback
    setFeedback({
      isCorrect,
      message: isCorrect ? 'Muito bem! 🎉' : `Não foi dessa vez. A resposta era ${correctAnswer}`,
      correctAnswer
    });

    // 🔍 COLETA DE DADOS COM COLLECTORS HUB
    const collectionData = {
      activity: gameState.currentActivity,
      isCorrect,
      responseTime: Date.now() - gameState.roundStartTime,
      difficulty: gameState.difficulty,
      round: gameState.round,
      answer: answer,
      correctAnswer: correctAnswer,
      questionData: currentActivityData,
      timestamp: Date.now()
    };

    // Ativar coleta de dados especializada
    try {
      collectorsHub.collectData(collectionData);
    } catch (error) {
      console.warn('⚠️ Erro na coleta de dados dos collectors:', error);
    }

    // TTS feedback padronizado
    if (isCorrect) {
      speakFeedback(true);
    } else {
      speakFeedback(false, `Não foi dessa vez. A resposta correta era ${correctAnswer}`);
    }

    // Próxima rodada
    setTimeout(() => {
      setFeedback(null);
      generateNewRound();
    }, 2500);
  }, [gameState, speak]);

  // 🎯 GERAR NOVA RODADA - SEM MUDANÇA AUTOMÁTICA DE ATIVIDADE
  const generateNewRound = useCallback(() => {
    // Manter a atividade atual - usuário controla mudanças
    const currentActivity = gameState.currentActivity;
    const newData = generateActivityData(currentActivity, gameState.difficulty);

    setGameState(prev => ({
      ...prev,
      round: prev.round + 1,
      roundStartTime: Date.now(),
      activityData: {
        ...prev.activityData,
        [currentActivity]: newData
      }
    }));

    // TTS da nova instrução padronizado
    setTimeout(() => {
      speak(newData.instruction, { rate: 0.8 });
    }, 500);
  }, [gameState, generateActivityData, speak]);

  // 🎯 TROCAR ATIVIDADE - Padrão PadroesVisuais
  const switchActivity = useCallback((activityId) => {
    if (activityId === gameState.currentActivity) return;

    const newData = generateActivityData(activityId, gameState.difficulty);

    setGameState(prev => ({
      ...prev,
      currentActivity: activityId,
      roundStartTime: Date.now(),
      activityData: {
        ...prev.activityData,
        [activityId]: newData
      }
    }));

    // TTS da nova atividade padronizado
    setTimeout(() => {
      speak(newData.instruction, { rate: 0.8 });
    }, 500);
  }, [gameState, generateActivityData, speak]);

  // 🎯 RENDERIZAR ATIVIDADE VISUAL COUNTING - ESTÁVEL
  const renderVisualCounting = () => {
    const data = gameState.activityData.visual_counting;

    // Se não há dados, gerar dados iniciais
    if (!data || !data.objects) {
      const initialData = generateActivityData(ACTIVITY_TYPES.VISUAL_COUNTING.id, gameState.difficulty);
      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          [ACTIVITY_TYPES.VISUAL_COUNTING.id]: initialData
        }
      }));
      return <div>Gerando atividade...</div>;
    }

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>{data.instruction}</h2>
        </div>

        {/* Object Display ESTÁVEL - sem position absolute */}
        <div className={styles.objectsDisplay}>
          {data.objects.map((obj, index) => (
            <div
              key={obj.id}
              className={styles.countingObject}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {obj.emoji}
            </div>
          ))}
        </div>

        {/* Opções de resposta */}
        <div className={styles.answerOptions}>
          {data.options.map((option, index) => (
            <button
              key={index}
              className={styles.answerButton}
              onClick={() => handleAnswer(option)}
            >
              <span className={styles.optionNumber}>{option}</span>
            </button>
          ))}
        </div>
      </div>
    );
  };

  // 🎯 RENDERIZAR SOMA SIMPLES
  const renderSimpleAddition = () => {
    const data = gameState.activityData.simple_addition;

    // Se não há dados, gerar dados iniciais
    if (!data || !data.options) {
      const initialData = generateActivityData(ACTIVITY_TYPES.SIMPLE_ADDITION.id, gameState.difficulty);
      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          [ACTIVITY_TYPES.SIMPLE_ADDITION.id]: initialData
        }
      }));
      return <div>Gerando atividade...</div>;
    }

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>{data.instruction}</h2>
        </div>

        {/* Exibição da soma */}
        <div className={styles.objectsDisplay}>
          <div className={styles.additionDisplay}>
            <span className={styles.additionNumber}>{data.number1}</span>
            <span className={styles.additionOperator}>+</span>
            <span className={styles.additionNumber}>{data.number2}</span>
            <span className={styles.additionOperator}>=</span>
            <span className={styles.additionResult}>?</span>
          </div>
        </div>

        {/* Opções de resposta */}
        <div className={styles.answerOptions}>
          {data.options.map((option, index) => (
            <button
              key={index}
              className={styles.answerButton}
              onClick={() => handleAnswer(option)}
            >
              <span className={styles.optionNumber}>{option}</span>
            </button>
          ))}
        </div>
      </div>
    );
  };

  // 🎯 RENDERIZAR RECONHECIMENTO DE NÚMERO
  // 🎯 RENDERIZAR RECONHECIMENTO DE NÚMERO - VERSÃO MELHORADA V2
  const renderNumberRecognition = () => {
    const data = gameState.activityData.number_recognition;

    // Se não há dados, gerar dados iniciais
    if (!data || !data.options) {
      const initialData = generateActivityData(ACTIVITY_TYPES.NUMBER_RECOGNITION.id, gameState.difficulty);
      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          [ACTIVITY_TYPES.NUMBER_RECOGNITION.id]: initialData
        }
      }));
      return <div>Gerando atividade...</div>;
    }

    const renderChallengeContent = () => {
      switch (data.challengeType) {
        case 'visual_count':
          return (
            <div className={styles.objectsDisplay}>
              <div className={styles.countingObjectsGrid}>
                {data.objects?.map((obj, index) => (
                  <div
                    key={obj.id}
                    className={styles.countingObject}
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    {obj.emoji}
                  </div>
                ))}
              </div>
            </div>
          );
        
        case 'written_number':
          return (
            <div className={styles.objectsDisplay}>
              <div className={styles.writtenNumberDisplay}>
                <span className={styles.writtenNumberText}>"{data.writtenNumber}"</span>
              </div>
            </div>
          );
        
        case 'sequence':
          return (
            <div className={styles.objectsDisplay}>
              <div className={styles.sequenceDisplay}>
                <span className={styles.sequenceNumber}>{data.beforeNumber}</span>
                <span className={styles.sequenceArrow}>→</span>
                <span className={styles.sequencePlaceholder}>?</span>
              </div>
            </div>
          );
        
        case 'comparison':
          return (
            <div className={styles.objectsDisplay}>
              <div className={styles.comparisonDisplay}>
                <span className={styles.comparisonNumber}>{data.number1}</span>
                <span className={styles.comparisonVs}>VS</span>
                <span className={styles.comparisonNumber}>{data.number2}</span>
              </div>
            </div>
          );
        
        case 'pattern':
          return (
            <div className={styles.objectsDisplay}>
              <div className={styles.patternDisplay}>
                {renderPattern(data.patternType, data.targetNumber)}
              </div>
            </div>
          );
        
        default:
          return (
            <div className={styles.objectsDisplay}>
              <div className={styles.targetNumber}>
                {data.targetNumber}
              </div>
            </div>
          );
      }
    };

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>{data.instruction}</h2>
        </div>

        {renderChallengeContent()}

        {/* Opções de resposta */}
        <div className={styles.answerOptions}>
          {data.options.map((option, index) => (
            <button
              key={index}
              className={styles.answerButton}
              onClick={() => handleAnswer(option)}
            >
              <span className={styles.optionNumber}>{option}</span>
            </button>
          ))}
        </div>
      </div>
    );
  };

  // 🎯 FUNÇÃO AUXILIAR PARA RENDERIZAR PADRÕES
  const renderPattern = useCallback((patternType, number) => {
    switch (patternType) {
      case 'dots':
        return (
          <div className={styles.dotsPattern}>
            {Array.from({ length: number }, (_, i) => (
              <span key={i} className={styles.dot}>●</span>
            ))}
          </div>
        );
      
      case 'dice':
        const dicePatterns = {
          1: '⚀', 2: '⚁', 3: '⚂', 4: '⚃', 5: '⚄', 6: '⚅'
        };
        return (
          <div className={styles.dicePattern}>
            <span className={styles.diceEmoji}>{dicePatterns[number] || '⚀'}</span>
          </div>
        );
      
      case 'fingers':
        return (
          <div className={styles.fingersPattern}>
            {Array.from({ length: number }, (_, i) => (
              <span key={i} className={styles.finger}>👆</span>
            ))}
          </div>
        );
      
      default:
        return <span>{number}</span>;
    }
  }, []);

  // 🎯 RENDERIZAR COMPARAÇÃO DE QUANTIDADE V2 - LAYOUT SIMPLES
  const renderQuantityComparison = () => {
    const data = gameState.activityData.quantity_comparison;

    // Se não há dados, gerar dados iniciais
    if (!data || !data.group1 || !data.group2) {
      const initialData = generateActivityData(ACTIVITY_TYPES.QUANTITY_COMPARISON.id, gameState.difficulty);
      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          [ACTIVITY_TYPES.QUANTITY_COMPARISON.id]: initialData
        }
      }));
      return <div>Gerando atividade...</div>;
    }

    // Manipular resposta baseada no tipo de desafio
    const handleComparisonAnswer = (answer) => {
      let isCorrect = false;
      
      switch (data.challengeType) {
        case 'more_than':
        case 'less_than':
          isCorrect = data.correctAnswer === answer;
          break;
        case 'equal_check':
          isCorrect = data.correctAnswer === answer;
          break;
        case 'count_difference':
          isCorrect = data.correctAnswer === answer;
          break;
        default:
          isCorrect = data.correctAnswer === answer;
      }
      
      handleAnswer(isCorrect ? data.correctAnswer : 'wrong');
    };

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>{data.instruction}</h2>
        </div>

        {/* Exibição simples dos grupos */}
        <div className={styles.objectsDisplay}>
          <div className={styles.comparisonGroups}>
            <button
              className={styles.comparisonGroup}
              onClick={() => handleComparisonAnswer('left')}
            >
              <div className={styles.groupLabel}>Grupo A</div>
              <div className={styles.groupObjects}>
                {data.group1.objects.map((obj, index) => (
                  <span 
                    key={`group1-${obj.id}-${index}`} 
                    className={styles.groupObject}
                  >
                    {obj.emoji}
                  </span>
                ))}
              </div>
            </button>

            <button
              className={styles.comparisonGroup}
              onClick={() => handleComparisonAnswer('right')}
            >
              <div className={styles.groupLabel}>Grupo B</div>
              <div className={styles.groupObjects}>
                {data.group2.objects.map((obj, index) => (
                  <span 
                    key={`group2-${obj.id}-${index}`} 
                    className={styles.groupObject}
                  >
                    {obj.emoji}
                  </span>
                ))}
              </div>
            </button>
          </div>

          {/* Botões especiais para desafios de igualdade */}
          {data.challengeType === 'equal_check' && (
            <div className={styles.equalityButtons}>
              <button
                className={styles.answerButton}
                onClick={() => handleComparisonAnswer('equal')}
              >
                ✅ SIM, são iguais
              </button>
              <button
                className={styles.answerButton}
                onClick={() => handleComparisonAnswer('different')}
              >
                ❌ NÃO, são diferentes
              </button>
            </div>
          )}

          {/* Entrada numérica para desafios de diferença */}
          {data.challengeType === 'count_difference' && (
            <div className={styles.answerGrid}>
              {[0, 1, 2, 3, 4, 5, 6].map(num => (
                <button
                  key={num}
                  className={styles.answerButton}
                  onClick={() => handleComparisonAnswer(num.toString())}
                >
                  {num}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  };

  // 🎯 RENDERIZAR INTERFACE PRINCIPAL - Padrão dos jogos de sucesso
  const renderCurrentActivity = () => {
    switch (gameState.currentActivity) {
      case ACTIVITY_TYPES.VISUAL_COUNTING.id:
        return renderVisualCounting();
      case ACTIVITY_TYPES.SIMPLE_ADDITION.id:
        return renderSimpleAddition();
      case ACTIVITY_TYPES.NUMBER_RECOGNITION.id:
        return renderNumberRecognition();
      case ACTIVITY_TYPES.QUANTITY_COMPARISON.id:
        return renderQuantityComparison();
      default:
        return renderVisualCounting();
    }
  };

  // 🎯 TELA DE INÍCIO
  if (showStartScreen) {
    return (
      <GameStartScreen
        gameTitle="Contagem de Números"
        gameDescription="Aprenda matemática de forma divertida e interativa"
        gameIcon="🔢"
        onStart={startGame}
        onBack={onBack}
        difficulties={ContagemNumerosConfig.difficulties.map(diff => ({
          id: diff.id,
          name: diff.name,
          description: diff.description,
          icon: diff.id === 'easy' ? '😊' : diff.id === 'medium' ? '🎯' : '🚀'
        }))}
      />
    );
  }

  // 🎯 INTERFACE PRINCIPAL - REPLICANDO LAYOUT DO LETTERRECOGNITION
  return (
    <div className={styles.contagemNumerosGame}>
      <div className={styles.gameContent}>
        {/* Header do jogo - PADRÃO LETTERRECOGNITION EXATO */}
        <div className={styles.gameHeader}>
          <h1 className={styles.gameTitle}>
            🔢 Contagem de Números V4
            <div style={{ fontSize: '0.7rem', opacity: 0.8, marginTop: '0.25rem' }}>
              {Object.values(ACTIVITY_TYPES).find(type => type.id === gameState.currentActivity)?.name || 'Atividade'}
            </div>
          </h1>
          <StandardTTSButton
            ttsActive={ttsActive}
            toggleTTS={toggleTTS}
            size="normal"
            position="header"
          />
        </div>

        {/* Header com estatísticas - padrão MemoryGame */}
        <div className={styles.gameStats}>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.score}</div>
            <div className={styles.statLabel}>Pontos</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.round}</div>
            <div className={styles.statLabel}>Rodada</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.accuracy || 100}%</div>
            <div className={styles.statLabel}>Precisão</div>
          </div>
        </div>

        {/* Menu de atividades - PADRÃO LETTERRECOGNITION */}
        <div className={styles.activityMenu}>
          {Object.values(ACTIVITY_TYPES).map((activity) => (
            <button
              key={activity.id}
              className={`${styles.activityButton} ${
                gameState.currentActivity === activity.id ? styles.active : ''
              }`}
              onClick={() => switchActivity(activity.id)}
              title={activity.description}
            >
              <span>{activity.icon}</span>
              <span>{activity.name}</span>
            </button>
          ))}
        </div>

        {/* Renderização da atividade atual - PADRÃO LETTERRECOGNITION EXATO */}
        <div className={styles.gameArea}>
          {renderCurrentActivity()}
        </div>

        {/* Controles do jogo - PADRÃO TTS PADRONIZADO */}
        <div className={styles.gameControls}>
          <button className={styles.controlButton} onClick={() => speakGameInstructions('Contagem de Números', 'Aprenda matemática de forma divertida contando objetos e resolvendo operações.')}>
            🔊 Explicar
          </button>
          <button className={styles.controlButton} onClick={() => setShowStartScreen(true)}>
            🔄 Reiniciar
          </button>
          <button className={styles.controlButton} onClick={() => { speak(TTS_MESSAGES.NAVIGATION.LEAVING_GAME); setTimeout(onBack, 1000); }}>
            ⬅️ Voltar
          </button>
        </div>
      </div>

      {/* Feedback - se houver */}
      {feedback && (
        <div className={`${styles.feedbackOverlay} ${feedback.isCorrect ? styles.success : styles.error}`}>
          <div className={styles.feedbackContent}>
            <div className={styles.feedbackMessage}>{feedback.message}</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ContagemNumerosGame;
