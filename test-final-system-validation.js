#!/usr/bin/env node

/**
 * @file test-final-system-validation.js
 * @description Teste final de validação do sistema após todas as correções
 * @version 1.0.0
 */

import fs from 'fs';

console.log('🎯 TESTE FINAL: Validação Completa do Sistema');
console.log('=' .repeat(70));

const games = [
  'LetterRecognition',
  'ContagemNumeros', 
  'MemoryGame',
  'ColorMatch',
  'ImageAssociation',
  'QuebraCabeca',
  'CreativePainting',
  'MusicalSequence',
  'PadroesVisuais'
];

// Função para contar atividades filhas
function countChildActivities(gameName) {
  const gameFile = `src/games/${gameName}/${gameName}Game.jsx`;
  
  if (!fs.existsSync(gameFile)) {
    return { error: 'Arquivo não encontrado' };
  }

  const content = fs.readFileSync(gameFile, 'utf8');
  
  // Procurar por ACTIVITY_TYPES com regex mais robusta
  const activityTypesMatch = content.match(/const\s+ACTIVITY_TYPES\s*=\s*\{([\s\S]*?)\n\};/);

  let activities = [];
  if (activityTypesMatch) {
    const activitiesBlock = activityTypesMatch[1];

    // Procurar por cada atividade individual (nome: { ... })
    const activityPattern = /(\w+):\s*\{[\s\S]*?id:\s*['"`]([^'"`]+)['"`][\s\S]*?\}/g;
    const activityMatches = [...activitiesBlock.matchAll(activityPattern)];

    activities = activityMatches.map(match => ({
      key: match[1],
      id: match[2],
      name: match[1].replace(/_/g, ' ').toLowerCase()
    }));
  }

  return {
    gameName,
    activitiesCount: activities.length,
    activities,
    hasActivityTypes: !!activityTypesMatch
  };
}

// Função para contar coletores ativos
function countActiveCollectors(gameName) {
  const indexFile = `src/games/${gameName}/collectors/index.js`;
  
  if (!fs.existsSync(indexFile)) {
    return { error: 'Arquivo index.js não encontrado' };
  }

  const content = fs.readFileSync(indexFile, 'utf8');
  
  // Contar coletores no constructor/objeto
  const constructorMatch = content.match(/this\.(?:_)?collectors\s*=\s*\{([^}]+)\}/s);
  let activeCollectors = 0;
  
  if (constructorMatch) {
    const collectorsBlock = constructorMatch[1];
    const collectorLines = collectorsBlock
      .split('\n')
      .filter(line => {
        const trimmed = line.trim();
        return trimmed && 
               !trimmed.startsWith('//') && 
               !trimmed.startsWith('/*') &&
               trimmed.includes('new ') &&
               trimmed.includes('Collector');
      });
    
    activeCollectors = collectorLines.length;
  }
  
  return {
    gameName,
    activeCollectors,
    hasCollectors: activeCollectors > 0
  };
}

// Executar validação completa
console.log('\n🔍 Executando validação completa...\n');

const results = {};
let totalActivities = 0;
let totalCollectors = 0;
let issuesFound = [];

games.forEach(gameName => {
  console.log(`🎮 ${gameName}:`);
  console.log('-'.repeat(50));
  
  // Contar atividades filhas
  const activitiesResult = countChildActivities(gameName);
  const collectorsResult = countActiveCollectors(gameName);
  
  results[gameName] = {
    activities: activitiesResult,
    collectors: collectorsResult
  };

  if (activitiesResult.error) {
    console.log(`❌ Atividades: ${activitiesResult.error}`);
    issuesFound.push(`${gameName}: ${activitiesResult.error}`);
  } else {
    console.log(`🎯 Atividades filhas: ${activitiesResult.activitiesCount}`);
    console.log(`📋 Lista: ${activitiesResult.activities.map(a => a.name).join(', ')}`);
    totalActivities += activitiesResult.activitiesCount;
    
    // Verificar se está na faixa ideal (4-5 atividades)
    if (activitiesResult.activitiesCount < 4) {
      issuesFound.push(`${gameName}: Poucas atividades (${activitiesResult.activitiesCount} < 4)`);
    } else if (activitiesResult.activitiesCount > 6) {
      issuesFound.push(`${gameName}: Muitas atividades (${activitiesResult.activitiesCount} > 6)`);
    }
  }

  if (collectorsResult.error) {
    console.log(`❌ Coletores: ${collectorsResult.error}`);
    issuesFound.push(`${gameName}: ${collectorsResult.error}`);
  } else {
    console.log(`📊 Coletores ativos: ${collectorsResult.activeCollectors}`);
    totalCollectors += collectorsResult.activeCollectors;
    
    // Verificar se está na faixa ideal (8-12 coletores)
    if (collectorsResult.activeCollectors < 6) {
      issuesFound.push(`${gameName}: Poucos coletores (${collectorsResult.activeCollectors} < 6)`);
    } else if (collectorsResult.activeCollectors > 15) {
      issuesFound.push(`${gameName}: Muitos coletores (${collectorsResult.activeCollectors} > 15)`);
    }
  }
  
  console.log('');
});

// Resumo final
console.log('='.repeat(70));
console.log('📊 RESUMO FINAL DA VALIDAÇÃO');
console.log('='.repeat(70));

// Estatísticas gerais
console.log('\n📈 ESTATÍSTICAS GERAIS:');
console.log(`   • Total de jogos analisados: ${games.length}`);
console.log(`   • Total de atividades filhas: ${totalActivities}`);
console.log(`   • Total de coletores ativos: ${totalCollectors}`);
console.log(`   • Média de atividades por jogo: ${(totalActivities / games.length).toFixed(1)}`);
console.log(`   • Média de coletores por jogo: ${(totalCollectors / games.length).toFixed(1)}`);

// Estimativa de métricas
const estimatedMetrics = totalCollectors * 5 * 5; // coletores * métricas * ações
console.log(`   • Estimativa de métricas por sessão: ~${estimatedMetrics}`);

// Ranking de atividades
console.log('\n🏆 RANKING POR ATIVIDADES FILHAS:');
const activitiesRanking = Object.entries(results)
  .filter(([_, result]) => !result.activities.error)
  .sort(([_, a], [__, b]) => b.activities.activitiesCount - a.activities.activitiesCount);

activitiesRanking.forEach(([gameName, result], index) => {
  const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '📊';
  const status = result.activities.activitiesCount >= 4 && result.activities.activitiesCount <= 6 ? '✅' : '⚠️';
  console.log(`${medal} ${index + 1}. ${gameName}: ${result.activities.activitiesCount} atividades ${status}`);
});

// Ranking de coletores
console.log('\n🏆 RANKING POR COLETORES ATIVOS:');
const collectorsRanking = Object.entries(results)
  .filter(([_, result]) => !result.collectors.error)
  .sort(([_, a], [__, b]) => b.collectors.activeCollectors - a.collectors.activeCollectors);

collectorsRanking.forEach(([gameName, result], index) => {
  const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '📊';
  const status = result.collectors.activeCollectors >= 6 && result.collectors.activeCollectors <= 15 ? '✅' : '⚠️';
  console.log(`${medal} ${index + 1}. ${gameName}: ${result.collectors.activeCollectors} coletores ${status}`);
});

// Problemas encontrados
console.log('\n🚨 PROBLEMAS ENCONTRADOS:');
if (issuesFound.length === 0) {
  console.log('✅ Nenhum problema crítico encontrado!');
  console.log('🎉 Sistema está 100% validado e pronto para uso!');
} else {
  console.log(`❌ ${issuesFound.length} problema(s) encontrado(s):`);
  issuesFound.forEach((issue, index) => {
    console.log(`   ${index + 1}. ${issue}`);
  });
}

// Status final
console.log('\n🎯 STATUS FINAL DO SISTEMA:');
const totalIssues = issuesFound.length;
const successRate = ((games.length * 2 - totalIssues) / (games.length * 2)) * 100;

console.log(`   • Taxa de sucesso: ${successRate.toFixed(1)}%`);
console.log(`   • Jogos funcionais: ${games.length - issuesFound.filter(i => i.includes('não encontrado')).length}/${games.length}`);
console.log(`   • Sistema de coletores: ${totalCollectors > 0 ? '✅ Ativo' : '❌ Inativo'}`);
console.log(`   • Sistema de atividades: ${totalActivities > 0 ? '✅ Ativo' : '❌ Inativo'}`);

if (successRate >= 90) {
  console.log('\n🎉 SISTEMA APROVADO PARA PRODUÇÃO!');
  console.log('✅ Portal Betina V3 está pronto para uso terapêutico');
} else if (successRate >= 75) {
  console.log('\n⚠️ SISTEMA PRECISA DE AJUSTES MENORES');
  console.log('🔧 Algumas correções necessárias antes da produção');
} else {
  console.log('\n❌ SISTEMA PRECISA DE CORREÇÕES CRÍTICAS');
  console.log('🚨 Muitos problemas encontrados - revisão necessária');
}

console.log('\n✅ VALIDAÇÃO COMPLETA FINALIZADA!');
console.log('\n💡 PRÓXIMOS PASSOS:');
if (issuesFound.length > 0) {
  console.log('   1. Corrigir problemas identificados');
  console.log('   2. Executar nova validação');
  console.log('   3. Testar no navegador');
} else {
  console.log('   1. Testar sistema no navegador');
  console.log('   2. Validar métricas sendo coletadas');
  console.log('   3. Preparar para produção');
}
