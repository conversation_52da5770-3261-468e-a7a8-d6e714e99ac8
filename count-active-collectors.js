#!/usr/bin/env node

/**
 * @file count-active-collectors.js
 * @description Conta os coletores realmente ativos em cada jogo
 * @version 1.0.0
 */

import fs from 'fs';

console.log('🔍 CONTAGEM REAL DE COLETORES ATIVOS');
console.log('=' .repeat(70));

const games = [
  'LetterRecognition',
  'ContagemNumeros', 
  'MemoryGame',
  'ColorMatch',
  'ImageAssociation',
  'QuebraCabeca',
  'CreativePainting',
  'MusicalSequence',
  'PadroesVisuais'
];

function countActiveCollectors(gameName) {
  const indexFile = `src/games/${gameName}/collectors/index.js`;
  
  if (!fs.existsSync(indexFile)) {
    return { error: 'Arquivo index.js não encontrado' };
  }

  const content = fs.readFileSync(indexFile, 'utf8');
  
  // Contar imports de coletores
  const importMatches = [...content.matchAll(/import.*from\s+['"]\.\//g)];
  const importCount = importMatches.length;
  
  // Contar coletores no constructor/objeto (buscar tanto _collectors quanto collectors)
  const constructorMatch = content.match(/this\.(?:_)?collectors\s*=\s*\{([^}]+)\}/s);
  let activeCollectors = 0;
  let collectorNames = [];

  if (constructorMatch) {
    const collectorsBlock = constructorMatch[1];
    // Contar linhas que definem coletores (não comentários)
    const collectorLines = collectorsBlock
      .split('\n')
      .filter(line => {
        const trimmed = line.trim();
        return trimmed &&
               !trimmed.startsWith('//') &&
               !trimmed.startsWith('/*') &&
               trimmed.includes('new ') &&
               trimmed.includes('Collector');
      });

    activeCollectors = collectorLines.length;
    collectorNames = collectorLines.map(line => {
      const match = line.match(/(\w+):\s*new\s+(\w+Collector)/);
      return match ? match[2] : 'Unknown';
    });
  }
  
  // Verificar se há hub ou classe principal
  const hasHub = content.includes('class ') && content.includes('Hub');
  const hasCollectorsProperty = content.includes('get collectors()');
  
  return {
    gameName,
    importCount,
    activeCollectors,
    collectorNames,
    hasHub,
    hasCollectorsProperty,
    fileSize: content.length
  };
}

// Analisar todos os jogos
console.log('\n📊 Contagem de coletores por jogo:\n');

const results = {};
let totalCollectors = 0;

games.forEach(gameName => {
  console.log(`🎮 ${gameName}:`);
  console.log('-'.repeat(50));
  
  const result = results[gameName] = countActiveCollectors(gameName);
  
  if (result.error) {
    console.log(`❌ ${result.error}`);
    return;
  }

  console.log(`📥 Imports encontrados: ${result.importCount}`);
  console.log(`⚡ Coletores ativos: ${result.activeCollectors}`);
  console.log(`🏗️ Tem Hub: ${result.hasHub ? '✅' : '❌'}`);
  console.log(`🔗 Tem getter collectors: ${result.hasCollectorsProperty ? '✅' : '❌'}`);
  
  if (result.collectorNames.length > 0) {
    console.log('📋 Coletores ativos:');
    result.collectorNames.forEach((name, index) => {
      console.log(`   ${index + 1}. ${name}`);
    });
  }
  
  totalCollectors += result.activeCollectors;
  console.log('');
});

// Resumo final
console.log('='.repeat(70));
console.log('📊 RESUMO FINAL');
console.log('='.repeat(70));

// Ranking por número de coletores
const ranking = Object.entries(results)
  .filter(([_, result]) => !result.error)
  .sort(([_, a], [__, b]) => b.activeCollectors - a.activeCollectors);

console.log('\n🏆 RANKING POR NÚMERO DE COLETORES:');
ranking.forEach(([gameName, result], index) => {
  const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '📊';
  console.log(`${medal} ${index + 1}. ${gameName}: ${result.activeCollectors} coletores`);
});

// Estatísticas
const validResults = Object.values(results).filter(r => !r.error);
const avgCollectors = totalCollectors / validResults.length;

console.log('\n📈 ESTATÍSTICAS:');
console.log(`   • Total de coletores ativos: ${totalCollectors}`);
console.log(`   • Média por jogo: ${avgCollectors.toFixed(1)} coletores`);
console.log(`   • Jogos analisados: ${validResults.length}`);

// Identificar jogos que precisam rebalanceamento
console.log('\n⚖️ ANÁLISE DE REBALANCEAMENTO:');

const lowCollectorGames = ranking.filter(([_, result]) => result.activeCollectors < 8);
const highCollectorGames = ranking.filter(([_, result]) => result.activeCollectors > 15);

if (lowCollectorGames.length > 0) {
  console.log('\n📈 JOGOS QUE PRECISAM DE MAIS COLETORES (< 8):');
  lowCollectorGames.forEach(([gameName, result]) => {
    const needed = 8 - result.activeCollectors;
    console.log(`   • ${gameName}: ${result.activeCollectors} coletores (precisa +${needed})`);
  });
}

if (highCollectorGames.length > 0) {
  console.log('\n📉 JOGOS COM MUITOS COLETORES (> 15):');
  highCollectorGames.forEach(([gameName, result]) => {
    const excess = result.activeCollectors - 12;
    console.log(`   • ${gameName}: ${result.activeCollectors} coletores (pode reduzir -${excess})`);
  });
}

// Recomendações
console.log('\n💡 RECOMENDAÇÕES DE REBALANCEAMENTO:');

const targetRange = { min: 8, max: 12 };
const needsAdjustment = ranking.filter(([_, result]) => 
  result.activeCollectors < targetRange.min || result.activeCollectors > targetRange.max
);

if (needsAdjustment.length === 0) {
  console.log('✅ Todos os jogos estão na faixa ideal (8-12 coletores)');
} else {
  console.log(`⚠️ ${needsAdjustment.length} jogos precisam de ajuste:`);
  
  needsAdjustment.forEach(([gameName, result]) => {
    if (result.activeCollectors < targetRange.min) {
      const needed = targetRange.min - result.activeCollectors;
      console.log(`   📈 ${gameName}: Adicionar ${needed} coletores (${result.activeCollectors} → ${targetRange.min})`);
    } else {
      const excess = result.activeCollectors - targetRange.max;
      console.log(`   📉 ${gameName}: Remover ${excess} coletores (${result.activeCollectors} → ${targetRange.max})`);
    }
  });
}

// Estimativa de métricas após rebalanceamento
console.log('\n📊 ESTIMATIVA DE MÉTRICAS APÓS REBALANCEAMENTO:');
const currentMetrics = totalCollectors * 5 * 5; // coletores * métricas * ações
const targetCollectors = validResults.length * 10; // 10 coletores por jogo
const targetMetrics = targetCollectors * 5 * 5;

console.log(`   • Métricas atuais: ~${currentMetrics} por sessão completa`);
console.log(`   • Métricas após rebalanceamento: ~${targetMetrics} por sessão completa`);
console.log(`   • Diferença: ${targetMetrics > currentMetrics ? '+' : ''}${targetMetrics - currentMetrics} métricas`);

console.log('\n✅ ANÁLISE DE COLETORES FINALIZADA!');
