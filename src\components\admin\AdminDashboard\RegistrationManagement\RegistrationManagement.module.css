/**
 * 🎨 Portal Betina V3 - Estilos do Gerenciamento de Cadastros
 */

.container {
  padding: 24px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  min-height: 100vh;
  color: #ffffff;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 2px solid #3b82f6;
}

.header h2 {
  color: #ffffff;
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
}

.refreshButton {
  background: #3b82f6;
  color: #ffffff;
  border: none;
  padding: 12px 20px;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.refreshButton:hover {
  background: #2563eb;
  transform: translateY(-2px);
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(59, 130, 246, 0.3);
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.summaryCard {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  transition: all 0.3s ease;
}

.summaryCard:hover {
  border-color: #3b82f6;
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.2);
}

.summaryNumber {
  font-size: 2.5rem;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 8px;
}

.summaryLabel {
  color: #d1d5db;
  font-size: 0.9rem;
  font-weight: 500;
}

.filters {
  margin-bottom: 24px;
  display: flex;
  gap: 16px;
  align-items: center;
}

.statusFilter {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  color: #ffffff;
  padding: 12px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.statusFilter:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.registrationsList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.emptyState {
  text-align: center;
  padding: 60px 20px;
  color: #9ca3af;
}

.registrationCard {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s ease;
}

.registrationCard:hover {
  border-color: #3b82f6;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.userInfo h4 {
  color: #ffffff;
  margin: 0 0 4px 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.userInfo p {
  color: #9ca3af;
  margin: 0;
  font-size: 0.9rem;
}

.statusBadge {
  color: #ffffff;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.cardContent {
  margin-bottom: 20px;
}

.cardInfo {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.cardInfo span {
  color: #e5e7eb;
  font-size: 0.9rem;
}

.cardInfo strong {
  color: #ffffff;
}

.cardActions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.detailsButton,
.quickApproveButton,
.quickRejectButton {
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.detailsButton {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
  border: 1px solid #3b82f6;
}

.detailsButton:hover {
  background: #3b82f6;
  color: #ffffff;
}

.quickApproveButton {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
  border: 1px solid #10b981;
}

.quickApproveButton:hover {
  background: #10b981;
  color: #ffffff;
}

.quickRejectButton {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid #ef4444;
}

.quickRejectButton:hover {
  background: #ef4444;
  color: #ffffff;
}

.quickApproveButton:disabled,
.quickRejectButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 20px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  border: 2px solid #3b82f6;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 2px solid #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

.modalHeader h3 {
  color: #ffffff;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.closeButton {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.closeButton:hover {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.modalContent {
  padding: 32px;
}

.section {
  margin-bottom: 32px;
}

.section h4 {
  color: #3b82f6;
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
  padding-bottom: 8px;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.infoGrid div {
  color: #e5e7eb;
  font-size: 0.9rem;
  line-height: 1.5;
}

.infoGrid strong {
  color: #ffffff;
}

.planInfo {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  padding: 20px;
}

.planName {
  color: #ffffff;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.planPrice {
  color: #3b82f6;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 8px;
}

.planDescription {
  color: #d1d5db;
  font-size: 0.9rem;
  line-height: 1.4;
}

.modalActions {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 24px 32px;
  border-top: 2px solid rgba(255, 255, 255, 0.1);
}

.approveButton,
.rejectButton {
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.approveButton {
  background: #10b981;
  color: #ffffff;
}

.approveButton:hover {
  background: #059669;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.rejectButton {
  background: #ef4444;
  color: #ffffff;
}

.rejectButton:hover {
  background: #dc2626;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

.approveButton:disabled,
.rejectButton:disabled {
  background: #6b7280;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}
