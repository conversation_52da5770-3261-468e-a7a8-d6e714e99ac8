/**
 * @file ColorMatchConfig.js
 * @description Configurações do Jogo de Combinação de Cores V3
 * @version 3.1.0
 */

/**
 * Configurações base para jogos
 */
export const BaseGameConfig = {
  difficulties: [
    { id: 'easy', name: '<PERSON><PERSON><PERSON><PERSON>', description: 'Ideal para iniciantes' },
    { id: 'medium', name: '<PERSON><PERSON><PERSON>', description: 'Desafio equilibrado' },
    { id: 'hard', name: '<PERSON>n<PERSON><PERSON>', description: 'Para especialistas' }
  ],
  gameSettings: {
    basePoints: 10,
    streakBonus: 5,
    maxWrongOptions: 3,
    feedbackDuration: 2000,
    celebrationDuration: 3000,
    adaptiveDifficulty: true,
    showProgressIndicators: true,
    enableHints: true,
    enableEncouragement: true
  },
  accessibility: {
    tts: {
      enabled: true,
      speed: 0.8,
      pitch: 1.0,
      languages: ['pt-BR'],
      autoRepeat: false,
      contextualHelp: true
    },
    visual: {
      highContrast: false,
      largeText: false,
      reducedMotion: false,
      colorBlindSupport: true
    },
    motor: {
      largeButtons: false,
      keyboardNavigation: true,
      touchOptimized: true
    }
  },
  encouragingMessages: [
    'Muito bem! Você está indo ótimo! 🎉',
    'Excelente! Continue assim! ⭐',
    'Fantástico! Suas habilidades estão crescendo! 👏'
  ]
};

/**
 * Função para criar tipos de atividades
 */
export const createActivityType = (id, name, icon, description, difficultyConfig) => ({
  id,
  name,
  icon,
  description,
  difficulty: difficultyConfig
});

export const ColorMatchConfig = {
  ...BaseGameConfig,
  gameSettings: {
    ...BaseGameConfig.gameSettings,
    timeLimit: 30000, // 30 segundos
    activityBonus: 3,
    activityRotationInterval: 1,
    maxConsecutiveErrors: 3
  },
  difficulties: {
    very_easy: {
      id: 'very_easy',
      name: 'Muito Fácil',
      gridSize: 4,
      minRoundsPerActivity: 4,
      maxRoundsPerActivity: 7,
      totalRounds: 4,
      colorNames: ['Vermelho', 'Azul'],
      colors: [
        { name: 'Vermelho', hex: '#FF0000', rgb: 'rgb(255, 0, 0)' },
        { name: 'Azul', hex: '#0000FF', rgb: 'rgb(0, 0, 255)' }
      ],
      itemsPerColor: 2,
      totalItems: 4
    },
    easy: {
      id: 'easy',
      name: 'Fácil',
      gridSize: 6,
      minRoundsPerActivity: 4,
      maxRoundsPerActivity: 7,
      totalRounds: 5,
      colorNames: ['Vermelho', 'Azul', 'Verde'],
      colors: [
        { name: 'Vermelho', hex: '#FF0000', rgb: 'rgb(255, 0, 0)' },
        { name: 'Azul', hex: '#0000FF', rgb: 'rgb(0, 0, 255)' },
        { name: 'Verde', hex: '#00FF00', rgb: 'rgb(0, 255, 0)' }
      ],
      itemsPerColor: 3,
      totalItems: 9
    },
    medium: {
      id: 'medium',
      name: 'Médio',
      gridSize: 8,
      minRoundsPerActivity: 4,
      maxRoundsPerActivity: 7,
      totalRounds: 6,
      colorNames: ['Vermelho', 'Azul', 'Verde', 'Amarelo', 'Roxo'],
      colors: [
        { name: 'Vermelho', hex: '#FF0000', rgb: 'rgb(255, 0, 0)' },
        { name: 'Azul', hex: '#0000FF', rgb: 'rgb(0, 0, 255)' },
        { name: 'Verde', hex: '#00FF00', rgb: 'rgb(0, 255, 0)' },
        { name: 'Amarelo', hex: '#FFFF00', rgb: 'rgb(255, 255, 0)' },
        { name: 'Roxo', hex: '#800080', rgb: 'rgb(128, 0, 128)' }
      ],
      itemsPerColor: 3,
      totalItems: 15
    },
    hard: {
      id: 'hard',
      name: 'Avançado',
      gridSize: 8,
      minRoundsPerActivity: 4,
      maxRoundsPerActivity: 7,
      totalRounds: 7,
      colorNames: ['Vermelho', 'Azul', 'Verde', 'Amarelo', 'Roxo', 'Laranja', 'Rosa'],
      colors: [
        { name: 'Vermelho', hex: '#FF0000', rgb: 'rgb(255, 0, 0)' },
        { name: 'Azul', hex: '#0000FF', rgb: 'rgb(0, 0, 255)' },
        { name: 'Verde', hex: '#00FF00', rgb: 'rgb(0, 255, 0)' },
        { name: 'Amarelo', hex: '#FFFF00', rgb: 'rgb(255, 255, 0)' },
        { name: 'Roxo', hex: '#800080', rgb: 'rgb(128, 0, 128)' },
        { name: 'Laranja', hex: '#FFA500', rgb: 'rgb(255, 165, 0)' },
        { name: 'Rosa', hex: '#FFC0CB', rgb: 'rgb(255, 192, 203)' }
      ],
      itemsPerColor: 4,
      totalItems: 24
    },
    very_hard: {
      id: 'very_hard',
      name: 'Muito Avançado',
      gridSize: 10,
      minRoundsPerActivity: 4,
      maxRoundsPerActivity: 7,
      totalRounds: 7,
      colorNames: ['Carmesim', 'Índigo', 'Turquesa', 'Magenta', 'Ciano', 'Coral', 'Lavanda', 'Dourado'],
      colors: [
        { name: 'Carmesim', hex: '#DC143C', rgb: 'rgb(220, 20, 60)' },
        { name: 'Índigo', hex: '#4B0082', rgb: 'rgb(75, 0, 130)' },
        { name: 'Turquesa', hex: '#40E0D0', rgb: 'rgb(64, 224, 208)' },
        { name: 'Magenta', hex: '#FF00FF', rgb: 'rgb(255, 0, 255)' },
        { name: 'Ciano', hex: '#00FFFF', rgb: 'rgb(0, 255, 255)' },
        { name: 'Coral', hex: '#FF7F50', rgb: 'rgb(255, 127, 80)' },
        { name: 'Lavanda', hex: '#E6E6FA', rgb: 'rgb(230, 230, 250)' },
        { name: 'Dourado', hex: '#FFD700', rgb: 'rgb(255, 215, 0)' }
      ],
      itemsPerColor: 5,
      totalItems: 40
    }
  },
  activityTypes: {
    FIND_THE_COLOR: createActivityType(
      'find_the_color',
      'Encontrar a Cor',
      '🎨',
      'Encontre todos os itens que correspondem à cor exibida',
      {
        easy: { gridSize: 6, itemsPerColor: 3 },
        medium: { gridSize: 8, itemsPerColor: 3 },
        hard: { gridSize: 8, itemsPerColor: 4 }
      }
    ),
    NAME_THE_COLOR: createActivityType(
      'name_the_color',
      'Nomear a Cor',
      '✍️',
      'Digite o nome da cor exibida na tela',
      {
        easy: { colorCount: 3 },
        medium: { colorCount: 5 },
        hard: { colorCount: 7 }
      }
    ),
    MATCH_THE_NAME: createActivityType(
      'match_the_name',
      'Combinar o Nome',
      '🔗',
      'Escolha a cor que corresponde ao nome exibido',
      {
        easy: { options: 3 },
        medium: { options: 5 },
        hard: { options: 7 }
      }
    ),
    MEMORY_MATCH: createActivityType(
      'memory_match',
      'Jogo da Memória',
      '🧠',
      'Encontre pares de cores idênticas',
      {
        easy: { pairs: 4, showTime: 5000 },
        medium: { pairs: 6, showTime: 4000 },
        hard: { pairs: 8, showTime: 3000 }
      }
    ),
    SEQUENCE_REPEAT: createActivityType(
      'sequence_repeat',
      'Repetir Sequência',
      '🔄',
      'Repita a sequência de cores exibida',
      {
        easy: { sequenceLength: 3, showTime: 6000 },
        medium: { sequenceLength: 4, showTime: 5000 },
        hard: { sequenceLength: 5, showTime: 4000 }
      }
    ),
    COLOR_MIXING: createActivityType(
      'color_mixing',
      'Mistura de Cores',
      '🎨',
      'Combine cores para criar a cor alvo',
      {
        easy: { components: 2, tolerance: 20 },
        medium: { components: 3, tolerance: 15 },
        hard: { components: 4, tolerance: 10 }
      }
    )
  },
  activityMessages: {
    find_the_color: {
      instructions: [
        'Encontre todos os itens da cor {color}!',
        'Quantos itens da cor {color} você consegue encontrar?',
        'Procure pela cor {color} na tela!'
      ],
      encouragement: [
        'Ótima escolha!',
        'Você é excelente em encontrar cores!',
        'Perfeito! Continue assim!'
      ]
    },
    name_the_color: {
      instructions: [
        'Digite o nome da cor exibida!',
        'Qual é o nome desta cor?',
        'Nomeie a cor que você vê!'
      ],
      encouragement: [
        'Nomeação perfeita!',
        'Você sabe nomear as cores!',
        'Ótimo trabalho com as cores!'
      ]
    },
    match_the_name: {
      instructions: [
        'Escolha a cor que corresponde ao nome {color}!',
        'Combine a cor com o nome {color}!',
        'Qual cor corresponde a {color}?'
      ],
      encouragement: [
        'Combinação perfeita!',
        'Você é ótimo em combinar nomes!',
        'Excelente escolha de cor!'
      ]
    },
    memory_match: {
      instructions: [
        'Encontre os pares de cores idênticas!',
        'Memorize e combine as cores!',
        'Ache os pares de cores!'
      ],
      encouragement: [
        'Memória incrível!',
        'Você é ótimo no jogo da memória!',
        'Pares perfeitos!'
      ]
    },
    sequence_repeat: {
      instructions: [
        'Repita a sequência de cores!',
        'Siga a ordem das cores exibidas!',
        'Memorize e repita as cores!'
      ],
      encouragement: [
        'Sequência perfeita!',
        'Você é ótimo com sequências!',
        'Ótima memória para cores!'
      ]
    },
    color_mixing: {
      instructions: [
        'Combine as cores para criar {color}!',
        'Misture as cores para obter {color}!',
        'Crie a cor {color} com a combinação certa!'
      ],
      encouragement: [
        'Mistura perfeita!',
        'Você é um mestre das cores!',
        'Ótima combinação de cores!'
      ]
    }
  },
  categories: [
    {
      id: 'fruits',
      name: 'Frutas',
      emoji: '🍎',
      objects: [
        { id: 'apple', emoji: '🍎', name: 'Maçã' },
        { id: 'banana', emoji: '🍌', name: 'Banana' },
        { id: 'orange', emoji: '🍊', name: 'Laranja' },
        { id: 'grapes', emoji: '🍇', name: 'Uvas' }
      ]
    },
    {
      id: 'animals',
      name: 'Animais',
      emoji: '🐶',
      objects: [
        { id: 'dog', emoji: '🐶', name: 'Cachorro' },
        { id: 'cat', emoji: '🐱', name: 'Gato' },
        { id: 'rabbit', emoji: '🐰', name: 'Coelho' },
        { id: 'bear', emoji: '🐻', name: 'Urso' }
      ]
    }
  ],
  gameInfo: {
    title: 'ColorMatch V3',
    description: 'Desenvolva percepção visual com 6 atividades de cores',
    icon: '🎨',
    category: 'visual_perception',
    ageRange: '3-12',
    skills: [
      'percepção visual',
      'reconhecimento de cores',
      'memória visual',
      'atenção seletiva',
      'processamento visual'
    ],
    version: '3.1.0',
    activities: 6
  }
};