#!/usr/bin/env node

/**
 * @file test-session-initialization-fix.js
 * @description Teste para verificar se os erros de inicialização foram corrigidos
 * @version 1.0.0
 */

import fs from 'fs';

console.log('🔧 TESTE: Verificação de Correções de Inicialização');
console.log('=' .repeat(70));

const games = [
  'LetterRecognition',
  'ContagemNumeros', 
  'MemoryGame',
  'ColorMatch',
  'ImageAssociation',
  'QuebraCabeca',
  'CreativePainting',
  'MusicalSequence',
  'PadroesVisuais'
];

// Verificar se sessionId está sendo usado corretamente
function checkSessionIdUsage(gameName) {
  const gameFile = `src/games/${gameName}/${gameName}Game.jsx`;
  
  if (!fs.existsSync(gameFile)) {
    return { error: 'Arquivo não encontrado' };
  }

  const content = fs.readFileSync(gameFile, 'utf8');
  const lines = content.split('\n');
  
  const results = {
    gameName,
    hasUseUnifiedGameLogic: false,
    sessionIdFromUnified: false,
    sessionIdRef: false,
    useMultisensoryIntegrationLine: null,
    sessionIdDefinitionLine: null,
    sessionIdUsageLine: null,
    potentialIssues: [],
    status: 'OK'
  };

  // Verificar se usa useUnifiedGameLogic
  const unifiedLogicMatch = content.match(/useUnifiedGameLogic\s*\(/);
  if (unifiedLogicMatch) {
    results.hasUseUnifiedGameLogic = true;
    
    // Verificar se sessionId está sendo extraído do useUnifiedGameLogic
    const unifiedLogicBlock = content.match(/const\s*\{[^}]*sessionId[^}]*\}\s*=\s*useUnifiedGameLogic/);
    if (unifiedLogicBlock) {
      results.sessionIdFromUnified = true;
      results.sessionIdDefinitionLine = lines.findIndex(line => line.includes('sessionId') && line.includes('useUnifiedGameLogic')) + 1;
    }
  }

  // Verificar se usa sessionIdRef
  const sessionIdRefMatch = content.match(/sessionIdRef\s*=\s*useRef/);
  if (sessionIdRefMatch) {
    results.sessionIdRef = true;
    results.sessionIdDefinitionLine = lines.findIndex(line => line.includes('sessionIdRef') && line.includes('useRef')) + 1;
  }

  // Verificar se sessionId vem do SystemContext
  const systemContextMatch = content.match(/const\s*\{[^}]*sessionId[^}]*\}\s*=\s*useContext\s*\(\s*SystemContext\s*\)/);
  if (systemContextMatch) {
    results.sessionIdFromUnified = true; // Considerar como válido
    results.sessionIdDefinitionLine = lines.findIndex(line => line.includes('sessionId') && line.includes('SystemContext')) + 1;
  }

  // Verificar uso em useMultisensoryIntegration
  const multisensoryMatch = content.match(/useMultisensoryIntegration\s*\(\s*([^,)]+)/);
  if (multisensoryMatch) {
    const sessionParam = multisensoryMatch[1].trim();
    results.useMultisensoryIntegrationLine = lines.findIndex(line => line.includes('useMultisensoryIntegration(')) + 1;
    results.sessionIdUsageLine = results.useMultisensoryIntegrationLine;
    
    // Verificar se o parâmetro está correto
    if (sessionParam === 'sessionId' && !results.sessionIdFromUnified) {
      results.potentialIssues.push('sessionId usado mas não extraído do useUnifiedGameLogic');
      results.status = 'ERROR';
    } else if (sessionParam === 'sessionIdRef.current' && !results.sessionIdRef) {
      results.potentialIssues.push('sessionIdRef.current usado mas sessionIdRef não definido');
      results.status = 'ERROR';
    } else if (sessionParam !== 'sessionId' && sessionParam !== 'sessionIdRef.current') {
      results.potentialIssues.push(`Parâmetro sessionId suspeito: ${sessionParam}`);
      results.status = 'WARNING';
    }
  }

  // Verificar ordem de definição
  if (results.sessionIdUsageLine && results.sessionIdDefinitionLine) {
    if (results.sessionIdUsageLine < results.sessionIdDefinitionLine) {
      results.potentialIssues.push('sessionId usado antes de ser definido');
      results.status = 'ERROR';
    }
  }

  return results;
}

// Verificar configurações de dificuldade
function checkDifficultyConfig(gameName) {
  const gameFile = `src/games/${gameName}/${gameName}Game.jsx`;
  const configFile = `src/games/${gameName}/${gameName}Config.js`;
  
  const results = {
    gameName,
    gameFileExists: fs.existsSync(gameFile),
    configFileExists: fs.existsSync(configFile),
    difficultyIssues: [],
    status: 'OK'
  };

  if (!results.gameFileExists || !results.configFileExists) {
    results.status = 'MISSING_FILES';
    return results;
  }

  const gameContent = fs.readFileSync(gameFile, 'utf8');
  const configContent = fs.readFileSync(configFile, 'utf8');

  // Verificar se o jogo usa dificuldades em maiúsculo
  const gameUsesUppercase = gameContent.includes("'EASY'") || gameContent.includes('"EASY"');
  const gameUsesLowercase = gameContent.includes("'easy'") || gameContent.includes('"easy"');

  // Verificar se o config tem dificuldades em maiúsculo ou minúsculo
  const configHasUppercase = configContent.includes('EASY:') || configContent.includes('"EASY"') || configContent.includes("'EASY'");
  const configHasLowercase = configContent.includes('easy:') || configContent.includes('"easy"') || configContent.includes("'easy'");

  if (gameUsesUppercase && !configHasUppercase && configHasLowercase) {
    results.difficultyIssues.push('Jogo usa MAIÚSCULO mas config usa minúsculo');
    results.status = 'ERROR';
  } else if (gameUsesLowercase && !configHasLowercase && configHasUppercase) {
    results.difficultyIssues.push('Jogo usa minúsculo mas config usa MAIÚSCULO');
    results.status = 'ERROR';
  }

  return results;
}

// Executar verificações
console.log('\n🔍 Verificando inicialização de sessionId...\n');

const sessionResults = {};
const difficultyResults = {};
let totalErrors = 0;
let totalWarnings = 0;

games.forEach(gameName => {
  console.log(`📋 ${gameName}:`);
  console.log('-'.repeat(50));
  
  // Verificar sessionId
  const sessionResult = sessionResults[gameName] = checkSessionIdUsage(gameName);
  
  if (sessionResult.error) {
    console.log(`❌ ${sessionResult.error}`);
  } else {
    console.log(`🎣 useUnifiedGameLogic: ${sessionResult.hasUseUnifiedGameLogic ? '✅' : '❌'}`);
    console.log(`📊 sessionId extraído: ${sessionResult.sessionIdFromUnified ? '✅' : sessionResult.sessionIdRef ? '✅ (Ref)' : '❌'}`);
    console.log(`🔧 useMultisensoryIntegration: linha ${sessionResult.useMultisensoryIntegrationLine || 'N/A'}`);
    
    if (sessionResult.potentialIssues.length > 0) {
      console.log('⚠️ Issues encontrados:');
      sessionResult.potentialIssues.forEach(issue => {
        console.log(`   • ${issue}`);
      });
    }
    
    const statusIcon = sessionResult.status === 'OK' ? '✅' : sessionResult.status === 'WARNING' ? '⚠️' : '❌';
    console.log(`📈 Status: ${statusIcon} ${sessionResult.status}`);
    
    if (sessionResult.status === 'ERROR') totalErrors++;
    if (sessionResult.status === 'WARNING') totalWarnings++;
  }
  
  // Verificar dificuldades
  const difficultyResult = difficultyResults[gameName] = checkDifficultyConfig(gameName);
  
  if (difficultyResult.status === 'ERROR') {
    console.log('🎯 Dificuldades: ❌ ERRO');
    difficultyResult.difficultyIssues.forEach(issue => {
      console.log(`   • ${issue}`);
    });
    totalErrors++;
  } else if (difficultyResult.status === 'MISSING_FILES') {
    console.log('🎯 Dificuldades: ⚠️ Arquivos não encontrados');
    totalWarnings++;
  } else {
    console.log('🎯 Dificuldades: ✅ OK');
  }
  
  console.log('');
});

// Resumo final
console.log('='.repeat(70));
console.log('📊 RESUMO DAS CORREÇÕES');
console.log('='.repeat(70));

console.log('\n🎯 PROBLEMAS DE SESSÃO:');
const sessionErrors = Object.entries(sessionResults).filter(([_, result]) => result.status === 'ERROR');
const sessionWarnings = Object.entries(sessionResults).filter(([_, result]) => result.status === 'WARNING');

if (sessionErrors.length === 0 && sessionWarnings.length === 0) {
  console.log('✅ Todos os jogos têm inicialização de sessão correta!');
} else {
  sessionErrors.forEach(([gameName, result]) => {
    console.log(`❌ ${gameName}: ${result.potentialIssues.join(', ')}`);
  });
  sessionWarnings.forEach(([gameName, result]) => {
    console.log(`⚠️ ${gameName}: ${result.potentialIssues.join(', ')}`);
  });
}

console.log('\n🎯 PROBLEMAS DE DIFICULDADE:');
const difficultyErrors = Object.entries(difficultyResults).filter(([_, result]) => result.status === 'ERROR');

if (difficultyErrors.length === 0) {
  console.log('✅ Todas as configurações de dificuldade estão corretas!');
} else {
  difficultyErrors.forEach(([gameName, result]) => {
    console.log(`❌ ${gameName}: ${result.difficultyIssues.join(', ')}`);
  });
}

console.log('\n📈 ESTATÍSTICAS:');
console.log(`   • Total de erros: ${totalErrors}`);
console.log(`   • Total de warnings: ${totalWarnings}`);
console.log(`   • Jogos verificados: ${games.length}`);

const successRate = ((games.length * 2 - totalErrors - totalWarnings) / (games.length * 2)) * 100;
console.log(`   • Taxa de sucesso: ${successRate.toFixed(1)}%`);

if (totalErrors === 0) {
  console.log('\n🎉 TODAS AS CORREÇÕES APLICADAS COM SUCESSO!');
  console.log('✅ Nenhum erro crítico de inicialização encontrado');
  console.log('✅ Sistema pronto para execução');
} else {
  console.log('\n⚠️ AINDA HÁ PROBLEMAS A CORRIGIR:');
  console.log(`❌ ${totalErrors} erro(s) crítico(s) encontrado(s)`);
  console.log(`⚠️ ${totalWarnings} warning(s) encontrado(s)`);
}

console.log('\n✅ VERIFICAÇÃO DE CORREÇÕES FINALIZADA!');
console.log('\n💡 PRÓXIMOS PASSOS:');
console.log('   1. Corrigir erros críticos restantes');
console.log('   2. Testar jogos no navegador');
console.log('   3. Verificar se métricas estão sendo coletadas');
console.log('   4. Validar dashboard de métricas');
