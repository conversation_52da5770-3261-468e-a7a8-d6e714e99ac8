/**
 * 📝 Portal Betina V3 - Formulário de Cadastro
 * Componente para registro de novos usuários com seleção de planos
 */

import React, { useState, useEffect } from 'react'
import { PRICING_PLANS, REGISTRATION_FIELDS, calculatePrice, generatePixCode } from '../../../config/pricingPlans.js'
import styles from './RegistrationForm.module.css'

const RegistrationForm = ({ onClose, onSuccess }) => {
  const [currentStep, setCurrentStep] = useState(1)
  const [selectedPlan, setSelectedPlan] = useState('premium')
  const [formData, setFormData] = useState({})
  const [errors, setErrors] = useState({})
  const [isLoading, setIsLoading] = useState(false)
  const [pixData, setPixData] = useState(null)
  const [registrationId, setRegistrationId] = useState(null)

  const totalSteps = 4

  // Validação de campo individual
  const validateField = (name, value) => {
    const field = Object.values(REGISTRATION_FIELDS).find(section => 
      Object.keys(section).includes(name)
    )?.[name]

    if (!field) return null

    if (field.required && !value) {
      return `${field.label} é obrigatório`
    }

    if (field.validation) {
      const rules = field.validation.split('|')
      for (const rule of rules) {
        if (rule === 'email' && value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          return 'Email inválido'
        }
        if (rule === 'phone' && value && !/^\(\d{2}\)\s\d{4,5}-\d{4}$/.test(value)) {
          return 'Telefone inválido (formato: (11) 99999-9999)'
        }
        if (rule === 'cpf' && value && !/^\d{3}\.\d{3}\.\d{3}-\d{2}$/.test(value)) {
          return 'CPF inválido (formato: 000.000.000-00)'
        }
        if (rule.startsWith('min:')) {
          const min = parseInt(rule.split(':')[1])
          if (value && value.length < min) {
            return `${field.label} deve ter pelo menos ${min} caracteres`
          }
        }
        if (rule.startsWith('max:')) {
          const max = parseInt(rule.split(':')[1])
          if (value && value.length > max) {
            return `${field.label} deve ter no máximo ${max} caracteres`
          }
        }
      }
    }

    return null
  }

  // Atualizar campo do formulário
  const updateField = (name, value) => {
    setFormData(prev => ({ ...prev, [name]: value }))
    
    // Validar campo em tempo real
    const error = validateField(name, value)
    setErrors(prev => ({
      ...prev,
      [name]: error
    }))
  }

  // Validar step atual
  const validateCurrentStep = () => {
    const newErrors = {}
    let fieldsToValidate = []

    switch (currentStep) {
      case 1:
        fieldsToValidate = Object.keys(REGISTRATION_FIELDS.personal)
        break
      case 2:
        fieldsToValidate = Object.keys(REGISTRATION_FIELDS.professional).filter(key => 
          REGISTRATION_FIELDS.professional[key].required
        )
        break
      case 3:
        fieldsToValidate = Object.keys(REGISTRATION_FIELDS.usage).filter(key => 
          REGISTRATION_FIELDS.usage[key].required
        )
        break
    }

    fieldsToValidate.forEach(field => {
      const error = validateField(field, formData[field])
      if (error) newErrors[field] = error
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Próximo step
  const nextStep = () => {
    if (validateCurrentStep()) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps))
    }
  }

  // Step anterior
  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  // Submeter formulário
  const handleSubmit = async () => {
    if (!validateCurrentStep()) return

    setIsLoading(true)
    try {
      // Simular envio para API
      const registrationData = {
        ...formData,
        selectedPlan,
        timestamp: new Date().toISOString(),
        status: 'pending'
      }

      // Simular resposta da API
      const response = await new Promise(resolve => {
        setTimeout(() => {
          resolve({
            success: true,
            registrationId: `REG-${Date.now()}`,
            message: 'Cadastro enviado com sucesso!'
          })
        }, 2000)
      })

      if (response.success) {
        setRegistrationId(response.registrationId)
        
        // Gerar código PIX
        const plan = PRICING_PLANS[selectedPlan]
        const pix = generatePixCode(plan.price, selectedPlan, response.registrationId)
        setPixData(pix)
        
        setCurrentStep(4) // Ir para step de pagamento
      }
    } catch (error) {
      console.error('Erro no cadastro:', error)
      setErrors({ submit: 'Erro ao enviar cadastro. Tente novamente.' })
    } finally {
      setIsLoading(false)
    }
  }

  // Renderizar campo do formulário
  const renderField = (sectionKey, fieldKey) => {
    const section = REGISTRATION_FIELDS[sectionKey]
    const field = section[fieldKey]
    const value = formData[fieldKey] || ''
    const error = errors[fieldKey]

    if (field.type === 'select') {
      return (
        <div key={fieldKey} className={styles.fieldGroup}>
          <label className={styles.label}>
            {field.label} {field.required && <span className={styles.required}>*</span>}
          </label>
          <select
            value={value}
            onChange={(e) => updateField(fieldKey, e.target.value)}
            className={`${styles.select} ${error ? styles.error : ''}`}
          >
            <option value="">Selecione...</option>
            {field.options.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
          {error && <span className={styles.errorText}>{error}</span>}
        </div>
      )
    }

    return (
      <div key={fieldKey} className={styles.fieldGroup}>
        <label className={styles.label}>
          {field.label} {field.required && <span className={styles.required}>*</span>}
        </label>
        <input
          type="text"
          value={value}
          onChange={(e) => updateField(fieldKey, e.target.value)}
          placeholder={field.placeholder}
          className={`${styles.input} ${error ? styles.error : ''}`}
        />
        {error && <span className={styles.errorText}>{error}</span>}
      </div>
    )
  }

  // Renderizar seleção de planos
  const renderPlanSelection = () => (
    <div className={styles.planSelection}>
      <h3>Escolha seu Plano</h3>
      <div className={styles.plansGrid}>
        {Object.entries(PRICING_PLANS).map(([planId, plan]) => (
          <div
            key={planId}
            className={`${styles.planCard} ${selectedPlan === planId ? styles.selected : ''} ${plan.popular ? styles.popular : ''}`}
            onClick={() => setSelectedPlan(planId)}
          >
            {plan.popular && <div className={styles.popularBadge}>Mais Popular</div>}
            <h4>{plan.name}</h4>
            <div className={styles.price}>
              R$ {plan.price.toFixed(2)}
              <span className={styles.period}>/{plan.period}</span>
            </div>
            <p className={styles.description}>{plan.description}</p>
            <ul className={styles.features}>
              {plan.features.slice(0, 4).map((feature, index) => (
                <li key={index}>{feature}</li>
              ))}
              {plan.features.length > 4 && (
                <li className={styles.moreFeatures}>
                  +{plan.features.length - 4} recursos adicionais
                </li>
              )}
            </ul>
          </div>
        ))}
      </div>
    </div>
  )

  // Renderizar step de pagamento
  const renderPaymentStep = () => (
    <div className={styles.paymentStep}>
      <div className={styles.successMessage}>
        <h3>🎉 Cadastro Enviado com Sucesso!</h3>
        <p>Seu cadastro foi enviado para análise. Para ativar sua conta, realize o pagamento via PIX:</p>
      </div>

      <div className={styles.paymentInfo}>
        <div className={styles.planSummary}>
          <h4>{PRICING_PLANS[selectedPlan].name}</h4>
          <div className={styles.amount}>R$ {PRICING_PLANS[selectedPlan].price.toFixed(2)}</div>
        </div>

        {pixData && (
          <div className={styles.pixSection}>
            <h4>Pagamento via PIX</h4>
            <div className={styles.pixCode}>
              <label>Código PIX:</label>
              <div className={styles.codeContainer}>
                <input
                  type="text"
                  value={pixData.code}
                  readOnly
                  className={styles.pixInput}
                />
                <button
                  onClick={() => navigator.clipboard.writeText(pixData.code)}
                  className={styles.copyButton}
                >
                  📋 Copiar
                </button>
              </div>
            </div>
            <p className={styles.pixInstructions}>
              1. Copie o código PIX acima<br/>
              2. Abra o app do seu banco<br/>
              3. Escolha a opção PIX → Colar código<br/>
              4. Confirme o pagamento
            </p>
            <div className={styles.registrationId}>
              <strong>ID do Cadastro:</strong> {registrationId}
            </div>
          </div>
        )}
      </div>

      <div className={styles.nextSteps}>
        <h4>Próximos Passos:</h4>
        <ol>
          <li>Realize o pagamento via PIX</li>
          <li>Aguarde a confirmação (até 24h)</li>
          <li>Receba o email de ativação</li>
          <li>Acesse os dashboards premium</li>
        </ol>
      </div>
    </div>
  )

  return (
    <div className={styles.overlay}>
      <div className={styles.modal}>
        <div className={styles.header}>
          <h2>Cadastro Portal Betina V3</h2>
          <button onClick={onClose} className={styles.closeButton}>✕</button>
        </div>

        <div className={styles.progressBar}>
          {Array.from({ length: totalSteps }, (_, i) => (
            <div
              key={i}
              className={`${styles.progressStep} ${i + 1 <= currentStep ? styles.active : ''}`}
            >
              {i + 1}
            </div>
          ))}
        </div>

        <div className={styles.content}>
          {currentStep === 1 && (
            <div className={styles.step}>
              <h3>Dados Pessoais</h3>
              {Object.keys(REGISTRATION_FIELDS.personal).map(fieldKey =>
                renderField('personal', fieldKey)
              )}
            </div>
          )}

          {currentStep === 2 && (
            <div className={styles.step}>
              <h3>Informações Profissionais</h3>
              {Object.keys(REGISTRATION_FIELDS.professional).map(fieldKey =>
                renderField('professional', fieldKey)
              )}
            </div>
          )}

          {currentStep === 3 && (
            <div className={styles.step}>
              <h3>Plano e Uso</h3>
              {Object.keys(REGISTRATION_FIELDS.usage).map(fieldKey =>
                renderField('usage', fieldKey)
              )}
              {renderPlanSelection()}
            </div>
          )}

          {currentStep === 4 && renderPaymentStep()}
        </div>

        {currentStep < 4 && (
          <div className={styles.footer}>
            {currentStep > 1 && (
              <button onClick={prevStep} className={styles.prevButton}>
                ← Anterior
              </button>
            )}
            
            {currentStep < 3 && (
              <button onClick={nextStep} className={styles.nextButton}>
                Próximo →
              </button>
            )}
            
            {currentStep === 3 && (
              <button
                onClick={handleSubmit}
                disabled={isLoading}
                className={styles.submitButton}
              >
                {isLoading ? '⏳ Enviando...' : '✅ Finalizar Cadastro'}
              </button>
            )}
          </div>
        )}

        {currentStep === 4 && (
          <div className={styles.footer}>
            <button onClick={onClose} className={styles.closeModalButton}>
              Fechar
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

export default RegistrationForm
