/**
 * 🎨 ADMIN DASHBOARD V3 - UI/UX MODERNO
 * @file AdminDashboard.jsx
 * @description Dashboard Administrativo Principal - Portal Betina V3
 * @version 3.0.0
 * @admin true
 * @features Dark Mode, Glassmorphism, Animations, Responsive Design
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react'
// Dashboards Principais
import PerformanceDashboard from '../../dashboard/PerformanceDashboard/PerformanceDashboard'
import AdvancedAIReport from '../../dashboard/AdvancedAIReport/AdvancedAIReport'
import NeuropedagogicalDashboard from '../../dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard'
// Dashboards Administrativos
import { IntegratedSystemDashboard } from './IntegratedSystemDashboard/IntegratedSystemDashboard'
import { SystemHealthMonitor } from './SystemHealthMonitor/SystemHealthMonitor'
import { AnalyzersMonitor } from './AnalyzersMonitor/AnalyzersMonitor'
import { UserManagement } from './UserManagement/UserManagement'
import RegistrationManagement from './RegistrationManagement/RegistrationManagement'
import SystemLogs from './SystemLogs/SystemLogs'
import styles from './AdminDashboard.module.css'

const AdminDashboard = ({ onBack }) => {
  // Estados principais
  const [activeTab, setActiveTab] = useState('system')
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [loginInput, setLoginInput] = useState('')
  const [loginError, setLoginError] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  // Estados para funcionalidades modernas
  const [lastActivity, setLastActivity] = useState(new Date())
  const [systemStatus, setSystemStatus] = useState('online')
  const [notifications, setNotifications] = useState([])
  const [isFullscreen, setIsFullscreen] = useState(false)

  // Função de login melhorada com token do banco de dados
  const handleLogin = useCallback(async (e) => {
    e.preventDefault()
    setIsLoading(true)
    setLoginError('')

    try {
      // Tentar autenticação com token do banco de dados
      const response = await fetch('/api/auth/admin-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          password: loginInput,
          adminKey: 'betina2025_admin_key'
        })
      })

      if (response.ok) {
        const { token, user } = await response.json()
        
        // Salvar token e dados de autenticação
        localStorage.setItem('admin_token', token)
        localStorage.setItem('adminAuth', 'true')
        localStorage.setItem('adminLoginTime', new Date().toISOString())
        localStorage.setItem('adminUser', JSON.stringify(user))

        setIsAuthenticated(true)

        // Adicionar notificação de sucesso
        setNotifications(prev => [...prev, {
          id: Date.now(),
          type: 'success',
          message: '✅ Login realizado com token do banco de dados!',
          timestamp: new Date()
        }])
      } else {
        // Fallback para senha hardcoded se API falhar
        if (loginInput === 'betina2025') {
          setIsAuthenticated(true)
          localStorage.setItem('adminAuth', 'true')
          localStorage.setItem('adminLoginTime', new Date().toISOString())

          setNotifications(prev => [...prev, {
            id: Date.now(),
            type: 'warning',
            message: '⚠️ Login com fallback (API indisponível)',
            timestamp: new Date()
          }])
        } else {
          setLoginError('Credenciais inválidas. Tente novamente.')

          // Shake animation no input
          const input = document.querySelector(`.${styles.passwordInput}`)
          if (input) {
            input.style.animation = 'none'
            setTimeout(() => {
              input.style.animation = 'errorShake 0.5s ease-in-out'
            }, 10)
          }
        }
      }
    } catch (error) {
      console.warn('Erro na autenticação, usando fallback:', error)
      
      // Fallback para senha hardcoded se houver erro
      if (loginInput === 'betina2025') {
        setIsAuthenticated(true)
        localStorage.setItem('adminAuth', 'true')
        localStorage.setItem('adminLoginTime', new Date().toISOString())

        setNotifications(prev => [...prev, {
          id: Date.now(),
          type: 'warning',
          message: '⚠️ Login offline (sem conexão com API)',
          timestamp: new Date()
        }])
      } else {
        setLoginError('Erro de conexão. Tente novamente.')
      }
    } finally {
      setIsLoading(false)
    }
  }, [loginInput])

  // Função de logout melhorada com integração API
  const handleLogout = useCallback(async () => {
    setIsLoading(true)

    try {
      // Tentar logout via API se houver token
      const token = localStorage.getItem('adminToken')
      if (token) {
        try {
          await fetch('/api/auth/logout', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            }
          })
          console.log('✅ Logout admin via API bem-sucedido')
        } catch (apiError) {
          console.warn('⚠️ Erro na API de logout admin, continuando com logout local:', apiError.message)
        }
      }

      // Logout local (sempre executar)
      setIsAuthenticated(false)
      localStorage.removeItem('adminAuth')
      localStorage.removeItem('adminToken')
      localStorage.removeItem('adminLoginTime')
      setLoginInput('')
      setActiveTab('system')
      setNotifications([])

      // Feedback visual
      setNotifications(prev => [...prev, {
        id: Date.now(),
        type: 'success',
        message: '✅ Logout realizado com sucesso',
        timestamp: new Date()
      }])

    } catch (error) {
      console.error('❌ Erro durante logout admin:', error)
      // Mesmo com erro, fazer logout local
      setIsAuthenticated(false)
      localStorage.removeItem('adminAuth')
      localStorage.removeItem('adminToken')
      localStorage.removeItem('adminLoginTime')
      setLoginInput('')
      setActiveTab('system')
      setNotifications([])
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Função para trocar de aba com animação
  const handleTabChange = useCallback((tabId) => {
    if (tabId !== activeTab) {
      setActiveTab(tabId)
      setLastActivity(new Date())
    }
  }, [activeTab])

  // Função para toggle fullscreen
  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen()
      setIsFullscreen(true)
    } else {
      document.exitFullscreen()
      setIsFullscreen(false)
    }
  }, [])

  // Monitorar status do sistema
  useEffect(() => {
    const checkSystemStatus = () => {
      // Simular verificação de status
      const isOnline = navigator.onLine
      setSystemStatus(isOnline ? 'online' : 'offline')
    }

    checkSystemStatus()
    const interval = setInterval(checkSystemStatus, 30000) // Check every 30s

    window.addEventListener('online', checkSystemStatus)
    window.addEventListener('offline', checkSystemStatus)

    return () => {
      clearInterval(interval)
      window.removeEventListener('online', checkSystemStatus)
      window.removeEventListener('offline', checkSystemStatus)
    }
  }, [])

  // Auto-logout após inatividade (30 minutos)
  useEffect(() => {
    if (!isAuthenticated) return

    const checkInactivity = () => {
      const loginTime = localStorage.getItem('adminLoginTime')
      if (loginTime) {
        const timeDiff = Date.now() - new Date(loginTime).getTime()
        const thirtyMinutes = 30 * 60 * 1000

        if (timeDiff > thirtyMinutes) {
          handleLogout()
          alert('Sessão expirada por inatividade. Faça login novamente.')
        }
      }
    }

    const interval = setInterval(checkInactivity, 60000) // Check every minute
    return () => clearInterval(interval)
  }, [isAuthenticated, handleLogout])

  // Verificar autenticação salva
  useEffect(() => {
    const savedAuth = localStorage.getItem('adminAuth')
    if (savedAuth === 'true') {
      setIsAuthenticated(true)
      const loginTime = localStorage.getItem('adminLoginTime')
      if (loginTime) {
        setLastActivity(new Date(loginTime))
      }
    }
  }, [])

  // Configuração das abas com informações detalhadas
  const tabs = useMemo(() => [
    {
      id: 'system',
      label: 'Sistema Integrado',
      icon: '🖥️',
      description: 'Dashboard principal com métricas gerais',
      color: '#6366f1'
    },
    {
      id: 'health',
      label: 'Saúde do Sistema',
      icon: '🏥',
      description: '',
      color: '#10b981'
    },
    {
      id: 'analyzers',
      label: 'Analisadores',
      icon: '🔬',
      description: '',
      color: '#f59e0b'
    },
    {
      id: 'users',
      label: 'Usuários',
      icon: '👥',
      description: 'Gerenciamento de usuários e permissões',
      color: '#8b5cf6'
    },
    {
      id: 'registrations',
      label: 'Cadastros',
      icon: '📝',
      description: 'Gerenciamento de cadastros e pagamentos',
      color: '#06b6d4'
    },
    {
      id: 'logs',
      label: 'Logs',
      icon: '📋',
      description: '',
      color: '#ef4444'
    }
  ], [])

  // Função para obter informações da aba ativa
  const activeTabInfo = useMemo(() => {
    return tabs.find(tab => tab.id === activeTab)
  }, [tabs, activeTab])

  // Tela de login clean e moderna
  if (!isAuthenticated) {
    return (
      <div className={styles.cleanLoginContainer}>
        <div className={styles.cleanLoginBox}>
          <div className={styles.cleanLoginHeader}>
            <h2>🔐 Dashboard Admin</h2>
            <p>Acesso administrativo</p>
          </div>

          <form onSubmit={handleLogin} className={styles.cleanLoginForm}>
            <div className={styles.cleanInputGroup}>
              <input
                id="password"
                type="password"
                value={loginInput}
                onChange={(e) => setLoginInput(e.target.value)}
                placeholder="Senha de administrador"
                disabled={isLoading}
                className={styles.cleanPasswordInput}
                autoComplete="current-password"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !isLoading && loginInput.trim()) {
                    handleLogin(e)
                  }
                }}
              />
            </div>

            {loginError && (
              <div className={styles.cleanError}>
                {loginError}
              </div>
            )}

            <button
              type="submit"
              disabled={isLoading || !loginInput.trim()}
              className={styles.cleanLoginButton}
            >
              {isLoading ? 'Verificando...' : 'Entrar'}
            </button>
          </form>

          <div className="login-footer">
            <small>
              🔒 Conexão segura •
              🕒 Última atualização: {new Date().toLocaleTimeString()}
            </small>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.adminContainer}>
      {/* Header Admin Moderno */}
      <header className={styles.adminHeader}>
        <div className={styles.headerLeft}>
          <h1>🛠️ Painel Administrativo</h1>
          <span className={styles.version}>Portal Betina V3</span>
          <div className="system-info">
            <span className={`status-badge ${systemStatus}`}>
              {systemStatus === 'online' ? '🟢 Online' : '🔴 Offline'}
            </span>
          </div>
        </div>

        <div className={styles.headerRight}>
          <div className="header-controls">
            <button
              onClick={toggleFullscreen}
              className="control-button"
              title={isFullscreen ? "Sair do modo tela cheia" : "Modo tela cheia"}
            >
              {isFullscreen ? '🗗' : '🗖'}
            </button>

            <div className="notifications-badge">
              🔔
              {notifications.length > 0 && (
                <span className="notification-count">{notifications.length}</span>
              )}
            </div>
          </div>

          <span className={styles.adminUser}>
            👤 Administrador
            <small>Ativo desde {lastActivity.toLocaleTimeString()}</small>
          </span>

          <button
            onClick={handleLogout}
            className={styles.logoutButton}
            title="Sair do painel administrativo"
          >
            🚪 Sair
          </button>
        </div>
      </header>

      {/* Navigation Tabs Moderna */}
      <nav className={styles.tabNavigation}>
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => handleTabChange(tab.id)}
            className={`${styles.tabButton} ${activeTab === tab.id ? styles.active : ''}`}
            title={tab.description}
            style={{
              '--tab-color': tab.color
            }}
          >
            <span className={styles.tabIcon}>{tab.icon}</span>
            <span className={styles.tabLabel}>{tab.label}</span>
            {activeTab === tab.id && (
              <span className="active-indicator">●</span>
            )}
          </button>
        ))}

        {/* Indicador de aba ativa */}
        <div className="tab-indicator" style={{
          '--active-color': activeTabInfo?.color || '#6366f1'
        }} />
      </nav>

      {/* Content Area Moderna */}
      <main className={styles.adminContent}>
        {activeTab === 'system' && (
          <div className={styles.tabContent}>
            <IntegratedSystemDashboard />
          </div>
        )}

        {activeTab === 'health' && (
          <div className={styles.tabContent}>
            <h2>
              🏥 Monitoramento de Saúde
              <span className="tab-description">{activeTabInfo?.description}</span>
            </h2>
            <SystemHealthMonitor />
          </div>
        )}

        {activeTab === 'analyzers' && (
          <div className={styles.tabContent}>
            <h2>
              🔬 Monitor de Analisadores
              <span className="tab-description">{activeTabInfo?.description}</span>
            </h2>
            <AnalyzersMonitor />
          </div>
        )}

        {activeTab === 'users' && (
          <div className={styles.tabContent}>
            <UserManagement />
          </div>
        )}

        {activeTab === 'registrations' && (
          <div className={styles.tabContent}>
            <RegistrationManagement />
          </div>
        )}

        {activeTab === 'logs' && (
          <div className={styles.tabContent}>
            <h2 style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              📋 Logs do Sistema
              <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
                <label style={{ display: 'flex', alignItems: 'center', gap: '5px', fontSize: '14px' }}>
                  <input type="checkbox" />
                  Auto-refresh
                </label>
                <button style={{ padding: '5px 10px', borderRadius: '4px', border: 'none', background: '#10b981', color: 'white', cursor: 'pointer' }}>
                  📥 Exportar
                </button>
                <button style={{ padding: '5px 10px', borderRadius: '4px', border: 'none', background: '#ef4444', color: 'white', cursor: 'pointer' }}>
                  🗑️ Limpar
                </button>
                <button style={{ padding: '5px 10px', borderRadius: '4px', border: 'none', background: '#6366f1', color: 'white', cursor: 'pointer' }}>
                  � Atualizar
                </button>
              </div>
            </h2>
            <SystemLogs />
          </div>
        )}
      </main>

      {/* Footer Moderno */}
      <footer className={styles.adminFooter}>
        <div className={styles.footerInfo}>
          <span>
            Portal Betina V3 - Sistema Administrativo
            <small>v3.0.0</small>
          </span>
          <div className="footer-stats">
            <span>Aba ativa: {activeTabInfo?.label}</span>
            <span>Status: {systemStatus}</span>
            <span>Última atualização: {new Date().toLocaleString()}</span>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default AdminDashboard
