/**
 * 🔍 PADRÕES VISUAIS V3 - JOGO DE PADRÕES COM MÚLTIPLAS ATIVIDADES
 * Portal Betina V3 - Jogo educativo com 6 atividades diversificadas
 */

import React, { useState, useEffect, useContext, useCallback, useMemo, useRef } from 'react';
import { PadroesVisuaisConfig, ACTIVITY_CONFIG, ACTIVITY_DIFFICULTY_CONFIG } from './PadroesVisuaisConfig';
import { PadroesVisuaisMetrics } from './PadroesVisuaisMetrics';
import { SystemContext } from '../../components/context/SystemContext.jsx';
import { AdvancedMetricsEngine } from '../../api/services/algorithms/AdvancedMetricsEngine.js';
import { useAccessibilityContext } from '../../components/context/AccessibilityContext';
import { v4 as uuidv4 } from 'uuid';
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';
import { PadroesVisuaisCollectorsHub } from './collectors/index.js';
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic.js';
import { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js';
import { useTherapeuticOrchestrator } from '../../hooks/useTherapeuticOrchestrator.js';
import styles from './PadroesVisuais.module.css';

// 🎯 SISTEMA DE ATIVIDADES REDESENHADO V3 - PADRÕES VISUAIS
// Cada atividade testa diferentes aspectos do reconhecimento de padrões visuais
const ACTIVITY_TYPES = {
  SEQUENCE_PATTERNS: {
    id: 'sequence_patterns',
    name: 'Padrões Sequenciais',
    icon: '🔄',
    description: 'Teste de reconhecimento de sequências visuais',
    cognitiveFunction: 'sequential_pattern_recognition',
    component: 'SequencePatternsActivity'
  },
  GEOMETRIC_PATTERNS: {
    id: 'geometric_patterns',
    name: 'Padrões Geométricos',
    icon: '🔺',
    description: 'Teste de reconhecimento de formas e geometria',
    cognitiveFunction: 'geometric_spatial_recognition',
    component: 'GeometricPatternsActivity'
  },
  COLOR_PATTERNS: {
    id: 'color_patterns',
    name: 'Padrões de Cores',
    icon: '🌈',
    description: 'Teste de reconhecimento de padrões cromáticos',
    cognitiveFunction: 'chromatic_pattern_recognition',
    component: 'ColorPatternsActivity'
  },
  SYMMETRY_PATTERNS: {
    id: 'symmetry_patterns',
    name: 'Padrões de Simetria',
    icon: '⚖️',
    description: 'Teste de reconhecimento de simetria e reflexão',
    cognitiveFunction: 'symmetry_spatial_processing',
    component: 'SymmetryPatternsActivity'
  },
  COMPLETION_PATTERNS: {
    id: 'completion_patterns',
    name: 'Completar Padrões',
    icon: '🧩',
    description: 'Teste de completar padrões visuais complexos',
    cognitiveFunction: 'pattern_completion_inference',
    component: 'CompletionPatternsActivity'
  }
};

function PadroesVisuaisGame({ onBack }) {
  const { user, ttsEnabled = true } = useContext(SystemContext);
  const { settings } = useAccessibilityContext();

  // Estados TTS
  const [ttsActive, setTtsActive] = useState(true);

  // Referência para métricas
  const metricsRef = useRef(null);

  // Inicializar collectorsHub
  const collectorsHub = useMemo(() => new PadroesVisuaisCollectorsHub(), []);

  // 🎯 ESTADO EXPANDIDO COM SISTEMA DE ATIVIDADES V3
  const [gameState, setGameState] = useState({
    status: 'start', // 'start', 'playing', 'paused', 'finished'
    score: 0,
    round: 1,
    totalRounds: 10,
    difficulty: 'easy',
    accuracy: 100,
    roundStartTime: null,

    // 🎯 Sistema de atividades redesenhado (5 atividades distintas)
    currentActivity: ACTIVITY_TYPES.SEQUENCE_PATTERNS.id,
    activityCycle: [
      ACTIVITY_TYPES.SEQUENCE_PATTERNS.id,
      ACTIVITY_TYPES.GEOMETRIC_PATTERNS.id,
      ACTIVITY_TYPES.COLOR_PATTERNS.id,
      ACTIVITY_TYPES.SYMMETRY_PATTERNS.id,
      ACTIVITY_TYPES.COMPLETION_PATTERNS.id
    ],
    activityIndex: 0,
    roundsPerActivity: 4,
    activityRoundCount: 0,
    activitiesCompleted: [],

    // 🎯 Dados específicos de atividades
    activityData: {
      sequenceReproduction: {
        sequence: [],
        userSequence: [],
        showingSequence: false
      },
      patternCompletion: {
        pattern: [],
        missingElements: [],
        userAnswers: []
      },
      patternConstruction: {
        rules: null,
        availableElements: [],
        constructedPattern: []
      },
      visualClassification: {
        items: [],
        categories: [],
        userClassification: {}
      },
      patternTransformation: {
        originalPattern: [],
        transformationRule: null,
        userResult: []
      },
      anomalyDetection: {
        pattern: [],
        anomalies: [],
        foundAnomalies: []
      }
    },

    // 🎯 Feedback e animações
    showFeedback: false,
    feedbackType: null,
    feedbackMessage: '',
    showCelebration: false,

    // 🎯 Métricas comportamentais
    responseTime: 0,
    hesitationCount: 0,
    helpUsed: false,
    consecutiveCorrect: 0,
    totalAttempts: 0,
    correctAttempts: 0
  });

  // Estados específicos das atividades V3
  const [sequenceToReproduce, setSequenceToReproduce] = useState([]);
  const [patternToComplete, setPatternToComplete] = useState(null);
  const [constructionRules, setConstructionRules] = useState(null);
  const [classificationCriteria, setClassificationCriteria] = useState(null);
  const [transformationTarget, setTransformationTarget] = useState(null);
  const [anomalyPattern, setAnomalyPattern] = useState(null);
  const [playerConstruction, setPlayerConstruction] = useState([]);
  const [selectedTransformation, setSelectedTransformation] = useState(null);
  const [classificationGroups, setClassificationGroups] = useState([]);
  
  // Estados adicionais para novas funcionalidades
  const [transformationResult, setTransformationResult] = useState([]);
  const [classificationResults, setClassificationResults] = useState({});
  const [selectedForClassification, setSelectedForClassification] = useState(null);
  const [detectedAnomalies, setDetectedAnomalies] = useState([]);

  // Estados do jogo original
  const [showStartScreen, setShowStartScreen] = useState(true);
  const [gameStarted, setGameStarted] = useState(false);
  const [gameSequence, setGameSequence] = useState([]);
  const [playerSequence, setPlayerSequence] = useState([]);
  const [isShowingSequence, setIsShowingSequence] = useState(false);
  const [isPlayerTurn, setIsPlayerTurn] = useState(false);
  const [currentLevel, setCurrentLevel] = useState(1);
  const [difficulty, setDifficulty] = useState('easy');
  const [feedback, setFeedback] = useState(null);
  const [playingShape, setPlayingShape] = useState(null);
  const [consecutiveSuccesses, setConsecutiveSuccesses] = useState(0);
  const [startTime, setStartTime] = useState(null);
  const [draggedShape, setDraggedShape] = useState(null);
  const [countdown, setCountdown] = useState(null);
  const [gameStats, setGameStats] = useState({
    score: 0,
    correctSequences: 0,
    totalAttempts: 0,
    level: 1,
    streak: 0,
    stars: 0,
  });

  const [analysisResults, setAnalysisResults] = useState(null);
  const [attemptCount, setAttemptCount] = useState(0);
  const [advancedMetricsEngine] = useState(() => new AdvancedMetricsEngine());
  const [sessionInteractions, setSessionInteractions] = useState([]);
  const [sessionSequences, setSessionSequences] = useState([]);

  // Integração com o sistema unificado Portal Betina V3
  const {
    startUnifiedSession,
    recordInteraction,
    endUnifiedSession,
    updateMetrics,
    portalReady,
    sessionId,
    isSessionActive,
    gameState: unifiedGameState,
    sessionMetrics
  } = useUnifiedGameLogic('padroes_visuais');

  // 🔄 Hook multissensorial integrado
  const {
    initializeSession: initMultisensory,
    recordInteraction: recordMultisensoryInteraction,
    finalizeSession: finalizeMultisensory,
    updateData: updateMultisensoryData,
    multisensoryData,
    isInitialized: multisensoryInitialized
  } = useMultisensoryIntegration(sessionId, {
    gameType: 'padroes_visuais',
    collectorsHub,
    sensorTypes: {
      visual: true,
      haptic: true,
      tts: ttsEnabled,
      gestural: true,
      biometric: true
    },
    adaptiveMode: true,
    learningStyle: user?.profile?.learningStyle || 'visual'
  });

  // 🎯 Hook orquestrador terapêutico
  const {
    processGameMetrics: recordTherapeuticActivity,
    getRecommendations: getTherapeuticRecommendations,
    setUserContext: setTherapeuticContext
  } = useTherapeuticOrchestrator({ userId: user?.id });

  // Conectar métricas ao backend após inicialização
  useEffect(() => {
    if (metricsRef.current && recordInteraction && updateMetrics) {
      metricsRef.current.connectToBackend({
        recordInteraction,
        updateMetrics
      });
    }
  }, [recordInteraction, updateMetrics]);

  // Inicializar sistema multissensorial quando sessionId estiver disponível
  useEffect(() => {
    if (sessionId && typeof sessionId === 'string' && sessionId.length > 0 && gameStarted && !multisensoryInitialized) {
      const initializeMultisensorial = async () => {
        try {
          await initMultisensory();
          console.log('✅ Sistema multissensorial inicializado com sessionId:', sessionId);
        } catch (error) {
          console.error('❌ Erro ao inicializar sistema multissensorial:', error);
        }
      };
      initializeMultisensorial();
    }
  }, [sessionId, gameStarted, multisensoryInitialized, initMultisensory]);

  // Estados para controle de TTS
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [currentSpeechButton, setCurrentSpeechButton] = useState(null);
  const speechTimeoutRef = useRef(null);

  // Função TTS padronizada com controle de loop
  const speak = useCallback((text, options = {}) => {
    if (!ttsActive || !('speechSynthesis' in window) || isSpeaking) {
      return;
    }

    // Limpar timeout anterior
    if (speechTimeoutRef.current) {
      clearTimeout(speechTimeoutRef.current);
    }

    window.speechSynthesis.cancel();
    setIsSpeaking(true);
    setCurrentSpeechButton(options.buttonId || null);

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'pt-BR';
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;

    utterance.onend = () => {
      setIsSpeaking(false);
      setCurrentSpeechButton(null);
      if (options.onEnd) options.onEnd();
    };

    utterance.onerror = () => {
      setIsSpeaking(false);
      setCurrentSpeechButton(null);
    };

    // Timeout de segurança para evitar loops
    speechTimeoutRef.current = setTimeout(() => {
      window.speechSynthesis.cancel();
      setIsSpeaking(false);
      setCurrentSpeechButton(null);
    }, 15000); // 15 segundos máximo

    window.speechSynthesis.speak(utterance);
  }, [ttsActive, isSpeaking]);

  // Toggle TTS com controle de loop
  const toggleTTS = useCallback(() => {
    // Parar qualquer fala em andamento
    if ('speechSynthesis' in window) {
      window.speechSynthesis.cancel();
    }

    setIsSpeaking(false);
    setCurrentSpeechButton(null);

    if (speechTimeoutRef.current) {
      clearTimeout(speechTimeoutRef.current);
    }

    setTtsActive(prev => {
      const newState = !prev;
      localStorage.setItem('padroesVisuaisTTS', newState.toString());

      // Só falar se estiver ativando
      if (newState) {
        setTimeout(() => {
          speak('TTS ativado', { rate: 1.0 });
        }, 100);
      }

      return newState;
    });
  }, [speak]);

  // 🎯 FUNÇÕES DE CONTROLE DO JOGO PADRONIZADAS

  // Inicializar métricas
  useEffect(() => {
    if (!metricsRef.current) {
      metricsRef.current = PadroesVisuaisMetrics;
    }
  }, []);

  // =====================================================
  // 🎯 FUNÇÕES DE GERAÇÃO REDESENHADAS - PADRÕES VISUAIS ESPECÍFICOS
  // =====================================================

  // 🔄 PADRÕES SEQUENCIAIS - Reconhecimento de sequências visuais
  const generateSequencePatterns = useCallback(() => {
    console.log('🔄 Generating sequence patterns activity');

    const sequenceTypes = {
      easy: [
        { type: 'alternating', sequence: ['🔴', '🔵', '🔴', '🔵'], next: '🔴', explanation: 'Padrão alternado: vermelho, azul' },
        { type: 'repeating', sequence: ['🟡', '🟡', '🟢', '🟡', '🟡'], next: '🟢', explanation: 'Padrão repetitivo: 2 amarelos, 1 verde' }
      ],
      medium: [
        { type: 'progressive', sequence: ['🔴', '🔴🔵', '🔴🔵🟡'], next: '🔴🔵🟡🟢', explanation: 'Sequência progressiva crescente' },
        { type: 'abc_pattern', sequence: ['🔴', '🔵', '🟡', '🔴', '🔵'], next: '🟡', explanation: 'Padrão ABC repetitivo' }
      ],
      hard: [
        { type: 'fibonacci_visual', sequence: ['🔴', '🔵', '🔴🔵', '🔴🔵🔴', '🔴🔵🔴🔴🔵'], next: '🔴🔵🔴🔴🔵🔴🔵🔴', explanation: 'Sequência Fibonacci visual' },
        { type: 'complex_alternation', sequence: ['🔴🔵', '🟡🟢', '🔴🔵', '🟣🟠'], next: '🔴🔵', explanation: 'Alternância complexa de pares' }
      ]
    };

    const currentLevel = gameState.difficulty.toLowerCase() || 'easy';
    const levelSequences = sequenceTypes[currentLevel] || sequenceTypes.easy;
    const selectedSequence = levelSequences[Math.floor(Math.random() * levelSequences.length)];

    // Criar opções de resposta
    const wrongOptions = ['🟣', '🟠', '🔴🟢', '🔵🟡'].filter(opt => opt !== selectedSequence.next);
    const options = [selectedSequence.next, ...wrongOptions.slice(0, 2)].sort(() => Math.random() - 0.5);

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        sequencePatterns: {
          sequence: selectedSequence.sequence,
          correctNext: selectedSequence.next,
          options,
          explanation: selectedSequence.explanation,
          userAnswer: null,
          level: currentLevel,
          showingSequence: true
        }
      }
    }));

    // Mostrar sequência por tempo baseado na dificuldade
    const showTime = currentLevel === 'easy' ? 3000 : currentLevel === 'medium' ? 2500 : 2000;
    setTimeout(() => {
      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          sequencePatterns: {
            ...prev.activityData.sequencePatterns,
            showingSequence: false
          }
        }
      }));
    }, showTime);

    speak(`Atividade: Padrões Sequenciais - Nível ${currentLevel}. Observe a sequência e identifique o próximo elemento.`);
  }, [gameState.difficulty, speak]);

  // 🔺 PADRÕES GEOMÉTRICOS - Reconhecimento de formas e geometria
  const generateGeometricPatterns = useCallback(() => {
    console.log('🔺 Generating geometric patterns activity');

    const geometricTypes = {
      easy: [
        {
          shapes: ['🔴', '🔺', '🟦', '🔴', '🔺'],
          missing: 2,
          correct: '🟦',
          explanation: 'Padrão geométrico: círculo, triângulo, quadrado'
        },
        {
          shapes: ['⭐', '⭐', '🔶', '⭐', '⭐'],
          missing: 2,
          correct: '🔶',
          explanation: 'Padrão: 2 estrelas, 1 losango'
        }
      ],
      medium: [
        {
          shapes: ['🔴', '🔺', '🔴🔺', '🟦', '🔺🟦'],
          missing: 4,
          correct: '🔴🟦',
          explanation: 'Combinação progressiva de formas'
        },
        {
          shapes: ['⬜', '⬛', '⬜⬛', '🔶', '⬜🔶'],
          missing: 3,
          correct: '⬛🔶',
          explanation: 'Padrão de combinação geométrica'
        }
      ],
      hard: [
        {
          shapes: ['🔴🔺🟦', '🔺🟦🔴', '🟦🔴🔺', '🔴🔺🟦'],
          missing: 1,
          correct: '🔺🟦🔴',
          explanation: 'Rotação cíclica de formas geométricas'
        }
      ]
    };

    const currentLevel = gameState.difficulty.toLowerCase() || 'easy';
    const levelPatterns = geometricTypes[currentLevel] || geometricTypes.easy;
    const selectedPattern = levelPatterns[Math.floor(Math.random() * levelPatterns.length)];

    // Criar opções de resposta
    const wrongOptions = ['🟣', '🔸', '⬜⬛', '🔶🔺'].filter(opt => opt !== selectedPattern.correct);
    const options = [selectedPattern.correct, ...wrongOptions.slice(0, 2)].sort(() => Math.random() - 0.5);

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        geometricPatterns: {
          shapes: selectedPattern.shapes,
          missingIndex: selectedPattern.missing,
          correctAnswer: selectedPattern.correct,
          options,
          explanation: selectedPattern.explanation,
          userAnswer: null,
          level: currentLevel
        }
      }
    }));

    speak(`Atividade: Padrões Geométricos - Nível ${currentLevel}. Identifique a forma que completa o padrão geométrico.`);
  }, [gameState.difficulty, speak]);

  // 🌈 PADRÕES DE CORES - Reconhecimento de padrões cromáticos
  const generateColorPatterns = useCallback(() => {
    console.log('🌈 Generating color patterns activity');

    const colorTypes = {
      easy: [
        {
          colors: ['🔴', '🟡', '🔵', '🔴', '🟡'],
          missing: 4,
          correct: '🔵',
          explanation: 'Sequência de cores primárias: vermelho, amarelo, azul'
        },
        {
          colors: ['🟢', '🟢', '🟣', '🟢', '🟢'],
          missing: 2,
          correct: '🟣',
          explanation: 'Padrão: 2 verdes, 1 roxo'
        }
      ],
      medium: [
        {
          colors: ['🔴🟡', '🔵🟢', '🟣🟠', '🔴🟡', '🔵🟢'],
          missing: 4,
          correct: '🟣🟠',
          explanation: 'Sequência de pares de cores complementares'
        },
        {
          colors: ['🌈', '🔴', '🌈', '🟡', '🌈'],
          missing: 4,
          correct: '🔵',
          explanation: 'Arco-íris alternado com cores primárias'
        }
      ],
      hard: [
        {
          colors: ['🔴🟡🔵', '🟡🔵🟢', '🔵🟢🟣', '🟢🟣🟠'],
          missing: 3,
          correct: '🟣🟠🔴',
          explanation: 'Progressão cromática com sobreposição'
        }
      ]
    };

    const currentLevel = gameState.difficulty.toLowerCase() || 'easy';
    const levelPatterns = colorTypes[currentLevel] || colorTypes.easy;
    const selectedPattern = levelPatterns[Math.floor(Math.random() * levelPatterns.length)];

    // Criar opções de resposta
    const wrongOptions = ['⚫', '⚪', '🟤', '🔴🟢'].filter(opt => opt !== selectedPattern.correct);
    const options = [selectedPattern.correct, ...wrongOptions.slice(0, 2)].sort(() => Math.random() - 0.5);

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        colorPatterns: {
          colors: selectedPattern.colors,
          missingIndex: selectedPattern.missing,
          correctAnswer: selectedPattern.correct,
          options,
          explanation: selectedPattern.explanation,
          userAnswer: null,
          level: currentLevel
        }
      }
    }));

    speak(`Atividade: Padrões de Cores - Nível ${currentLevel}. Identifique a cor que completa o padrão cromático.`);
  }, [gameState.difficulty, speak]);

  // ⚖️ PADRÕES DE SIMETRIA - Reconhecimento de simetria e reflexão
  const generateSymmetryPatterns = useCallback(() => {
    console.log('⚖️ Generating symmetry patterns activity');

    const symmetryTypes = {
      easy: [
        {
          pattern: ['🔴', '🔵', '🟡', '🔵', '🔴'],
          type: 'horizontal',
          isSymmetric: true,
          explanation: 'Simetria horizontal perfeita'
        },
        {
          pattern: ['🔺', '🟦', '🔺', '🟦', '🔺'],
          type: 'horizontal',
          isSymmetric: true,
          explanation: 'Padrão simétrico horizontal'
        },
        {
          pattern: ['🔴', '🔵', '🟡', '🟢', '🟣'],
          type: 'none',
          isSymmetric: false,
          explanation: 'Sem simetria - padrão aleatório'
        }
      ],
      medium: [
        {
          pattern: [['🔴', '🔵'], ['🟡', '🟢'], ['🔴', '🔵']],
          type: 'vertical',
          isSymmetric: true,
          explanation: 'Simetria vertical em matriz'
        },
        {
          pattern: [['🔺', '🟦'], ['🟦', '🔺']],
          type: 'diagonal',
          isSymmetric: true,
          explanation: 'Simetria diagonal'
        }
      ],
      hard: [
        {
          pattern: [['🔴', '🔵', '🔴'], ['🟡', '🟢', '🟡'], ['🔴', '🔵', '🔴']],
          type: 'radial',
          isSymmetric: true,
          explanation: 'Simetria radial complexa'
        }
      ]
    };

    const currentLevel = gameState.difficulty.toLowerCase() || 'easy';
    const levelPatterns = symmetryTypes[currentLevel] || symmetryTypes.easy;
    const selectedPattern = levelPatterns[Math.floor(Math.random() * levelPatterns.length)];

    // Criar opções de resposta
    const options = ['Simétrico', 'Não Simétrico'].sort(() => Math.random() - 0.5);

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        symmetryPatterns: {
          pattern: selectedPattern.pattern,
          symmetryType: selectedPattern.type,
          isSymmetric: selectedPattern.isSymmetric,
          options,
          explanation: selectedPattern.explanation,
          userAnswer: null,
          level: currentLevel
        }
      }
    }));

    speak(`Atividade: Padrões de Simetria - Nível ${currentLevel}. Analise se o padrão apresenta simetria.`);
  }, [gameState.difficulty, speak]);

  // 🧩 COMPLETAR PADRÕES - Completar padrões visuais complexos
  const generateCompletionPatterns = useCallback(() => {
    console.log('🧩 Generating completion patterns activity');

    const completionTypes = {
      easy: [
        {
          pattern: ['🔴', '🔵', '🔴', '🔵', null],
          missing: 4,
          correct: '🔴',
          explanation: 'Complete o padrão alternado simples'
        },
        {
          pattern: ['🟡', '🟡', '🟢', '🟡', '🟡', null],
          missing: 5,
          correct: '🟢',
          explanation: 'Padrão: 2 amarelos, 1 verde'
        }
      ],
      medium: [
        {
          pattern: ['🔴', '🔵🟡', '🔴🔵', '🟡🔴', null],
          missing: 4,
          correct: '🔵🟡',
          explanation: 'Padrão de rotação com múltiplos elementos'
        },
        {
          pattern: [['🔴', '🔵'], ['🟡', null], ['🔴', '🔵']],
          missing: [1, 1],
          correct: '🟢',
          explanation: 'Complete a matriz simétrica'
        }
      ],
      hard: [
        {
          pattern: ['🔴🔵🟡', '🔵🟡🟢', '🟡🟢🟣', null],
          missing: 3,
          correct: '🟢🟣🟠',
          explanation: 'Sequência progressiva complexa com sobreposição'
        }
      ]
    };

    const currentLevel = gameState.difficulty.toLowerCase() || 'easy';
    const levelPatterns = completionTypes[currentLevel] || completionTypes.easy;
    const selectedPattern = levelPatterns[Math.floor(Math.random() * levelPatterns.length)];

    // Criar opções de resposta
    const wrongOptions = ['🟤', '⚫', '⚪', '🔶🔸'].filter(opt => opt !== selectedPattern.correct);
    const options = [selectedPattern.correct, ...wrongOptions.slice(0, 3)].sort(() => Math.random() - 0.5);

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        completionPatterns: {
          pattern: selectedPattern.pattern,
          missingIndex: selectedPattern.missing,
          correctAnswer: selectedPattern.correct,
          options,
          explanation: selectedPattern.explanation,
          userAnswer: null,
          level: currentLevel
        }
      }
    }));

    speak(`Atividade: Completar Padrões - Nível ${currentLevel}. Complete o padrão visual identificando o elemento faltante.`);
  }, [gameState.difficulty, speak]);

  // Função para gerar nova rodada
  const generateNewRound = useCallback(() => {
    const currentActivity = gameState.currentActivity;

    setGameState(prev => ({
      ...prev,
      roundStartTime: Date.now(),
      showFeedback: false,
      feedbackType: null,
      feedbackMessage: ''
    }));

    // Gerar dados específicos da atividade redesenhada
    switch (currentActivity) {
      case ACTIVITY_TYPES.SEQUENCE_PATTERNS.id:
        generateSequencePatterns();
        break;
      case ACTIVITY_TYPES.GEOMETRIC_PATTERNS.id:
        generateGeometricPatterns();
        break;
      case ACTIVITY_TYPES.COLOR_PATTERNS.id:
        generateColorPatterns();
        break;
      case ACTIVITY_TYPES.SYMMETRY_PATTERNS.id:
        generateSymmetryPatterns();
        break;
      case ACTIVITY_TYPES.COMPLETION_PATTERNS.id:
        generateCompletionPatterns();
        break;
      default:
        generateSequencePatterns();
    }
  }, [
    gameState.currentActivity,
    generateSequencePatterns,
    generateGeometricPatterns,
    generateColorPatterns,
    generateSymmetryPatterns,
    generateCompletionPatterns
  ]);

  // 🔄 Função para trocar atividade manualmente
  const switchToActivity = useCallback((activityId) => {
    console.log('🔄 Switching to activity:', activityId);

    setGameState(prev => ({
      ...prev,
      currentActivity: activityId,
      activityData: {
        ...prev.activityData,
        [activityId]: {} // Reset da atividade
      }
    }));

    // Gerar novo conteúdo para a atividade
    switch (activityId) {
      case ACTIVITY_TYPES.SEQUENCE_PATTERNS.id:
        generateSequencePatterns();
        break;
      case ACTIVITY_TYPES.GEOMETRIC_PATTERNS.id:
        generateGeometricPatterns();
        break;
      case ACTIVITY_TYPES.COLOR_PATTERNS.id:
        generateColorPatterns();
        break;
      case ACTIVITY_TYPES.SYMMETRY_PATTERNS.id:
        generateSymmetryPatterns();
        break;
      case ACTIVITY_TYPES.COMPLETION_PATTERNS.id:
        generateCompletionPatterns();
        break;
      default:
        generateSequencePatterns();
    }

    // Anunciar nova atividade
    const activity = Object.values(ACTIVITY_TYPES).find(a => a.id === activityId);
    if (activity && speak) {
      speak(`Atividade alterada para: ${activity.name}. ${activity.description}`, { rate: 0.8 });
    }
  }, [generateSequencePatterns, generateGeometricPatterns, generateColorPatterns, generateSymmetryPatterns, generateCompletionPatterns, speak]);

  // Função para gerar sequência - DEFINIDA ANTES DO generateNewLevel
  const generateSequence = useCallback((length) => {
    const sequence = [];
    let lastShapeIndex = -1;
    for (let i = 0; i < length; i++) {
      let randomIndex;
      do {
        randomIndex = Math.floor(Math.random() * PadroesVisuaisConfig.shapes.length);
      } while (randomIndex === lastShapeIndex && PadroesVisuaisConfig.shapes.length > 1);
      lastShapeIndex = randomIndex;
      sequence.push(PadroesVisuaisConfig.shapes[randomIndex].id);
    }
    return sequence;
  }, []);

  // Função para mostrar sequência - DEFINIDA ANTES DO generateNewLevel
  const showSequence = useCallback(async () => {
    setIsShowingSequence(true);
    setIsPlayerTurn(false);
    setPlayerSequence([]);
    setFeedback(null);
    const difficultyData = PadroesVisuaisConfig.difficulties.find(d => d.id === difficulty);
    const baseTime = difficultyData.showTime || 8000;
    const sequenceBonus = gameSequence.length * 1500;
    const showTime = baseTime + sequenceBonus;
    console.log(`📝 Mostrando sequência de ${gameSequence.length} formas por ${showTime}ms`);
    const formDescription = gameSequence.length === 1 ? 'forma' : 'formas';
    const memorizeMessage = `Atenção! Memorize esta sequência de ${gameSequence.length} ${formDescription} coloridas. Você tem ${Math.ceil(showTime / 1000)} segundos para memorizar.`;
    speak(memorizeMessage, {
      rate: 1.0,
      onEnd: () => console.log('Instrução de memorização anunciada'),
    });
    setCountdown(Math.ceil(showTime / 1000));
    const countdownInterval = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(countdownInterval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    setTimeout(() => {
      clearInterval(countdownInterval);
      setCountdown(null);
      setIsShowingSequence(false);
      setIsPlayerTurn(true);
      const turnMessage = `Agora é sua vez! Recrie a sequência arrastando as formas para os slots corretos.`;
      speak(turnMessage, {
        rate: 0.9,
        onEnd: () => console.log('Vez do jogador anunciada'),
      });
    }, showTime);
  }, [gameSequence, difficulty, speak]);

  // Função para gerar novo nível - DEFINIDA ANTES DO startGame
  const generateNewLevel = useCallback(() => {
    const difficultyData = PadroesVisuaisConfig.difficulties.find(d => d.id === difficulty);
    const sequenceLength = Math.min(difficultyData.sequenceLength + Math.floor(currentLevel / 3), 8);
    console.log(`🎯 Gerando novo nível - Dificuldade: ${difficulty}, Nível: ${currentLevel}, Tamanho calculado: ${sequenceLength}`);
    const newSequence = generateSequence(sequenceLength);
    console.log(`✨ Nova sequência gerada: ${JSON.stringify(newSequence)}, Comprimento: ${newSequence.length}`);
    setGameSequence(newSequence);
    setPlayerSequence([]);
    setFeedback(null);
    setTimeout(() => showSequence(), 1000);
  }, [difficulty, currentLevel, generateSequence, showSequence]);

  // Função para iniciar o jogo
  const startGame = useCallback(async (selectedDifficulty) => {
    try {
      // Esconder tela inicial primeiro
      setShowStartScreen(false);
      setGameStarted(true);

      setGameState(prev => ({
        ...prev,
        status: 'playing',
        difficulty: selectedDifficulty,
        roundStartTime: Date.now()
      }));

      // Inicializar sessão unificada
      if (startUnifiedSession) {
        await startUnifiedSession({
          gameType: 'padroes_visuais',
          difficulty: selectedDifficulty,
          userId: user?.id || 'anonymous'
        });
      }

      // O sistema multissensorial será inicializado no useEffect quando sessionId estiver disponível

      // Configurar contexto terapêutico se usuário válido
      if (user?.id && user.id !== 'anonymous' && user.id !== '') {
        setTherapeuticContext(user.id);
      }

      // Gerar primeira atividade
      generateNewLevel();

      speak('Jogo iniciado! Vamos começar com padrões visuais.');
    } catch (error) {
      console.error('Erro ao iniciar jogo:', error);
    }
  }, [startUnifiedSession, setTherapeuticContext, user, speak, generateNewLevel]);

  // Função para trocar atividade - LIVRE PARA TROCAR
  const changeActivity = useCallback((activityId) => {
    // Permitir troca livre de atividades
    if (gameState.currentActivity === activityId) {
      return; // Já está na atividade selecionada
    }

    setGameState(prev => ({
      ...prev,
      currentActivity: activityId,
      activityRoundCount: 0,
      activityIndex: prev.activityCycle.indexOf(activityId)
    }));

    const activity = Object.values(ACTIVITY_TYPES).find(a => a.id === activityId);
    speak(`Mudando para: ${activity?.name}`);

    // Gerar novo nível para a nova atividade
    generateNewLevel();
  }, [gameState.currentActivity, speak, generateNewLevel]);

  // Função helper para obter emoji da forma
  const getShapeEmoji = useCallback((shapeId) => {
    const shape = PadroesVisuaisConfig.shapes.find(s => s.id === shapeId);
    return shape ? shape.emoji : shapeId;
  }, []);

  // Funções de acessibilidade TTS
  const explainGame = useCallback(() => {
    const explanation = `
      Bem-vindo ao Padrões Visuais! Este jogo ajuda a desenvolver sua memória visual, sequenciação, concentração e reconhecimento de padrões lógicos.
      Você observará sequências de formas coloridas e deverá reproduzi-las na ordem correta.
      Use as formas disponíveis para arrastar e soltar nos slots corretos.
      Você pode escolher entre três níveis de dificuldade: fácil, médio e avançado.
      Boa sorte e divirta-se!
    `;
    speak(explanation, {
      rate: 0.9,
      buttonId: 'explain',
      onEnd: () => console.log('Explicação do jogo anunciada'),
    });
  }, [speak]);

  const repeatInstruction = useCallback(() => {
    let instruction = '';
    if (isShowingSequence) {
      instruction = `Observe e memorize esta sequência de ${gameSequence.length} formas coloridas. ${countdown ? `Você tem ${countdown} segundos restantes para memorizar.` : ''}`;
    } else if (isPlayerTurn) {
      instruction = `Agora é sua vez! Recrie a sequência arrastando as formas para os slots corretos na ordem que você memorizou. São ${gameSequence.length} formas no total.`;
    } else {
      instruction = 'Preparando a próxima sequência. Aguarde um momento.';
    }
    speak(instruction, {
      rate: 0.9,
      buttonId: 'repeat',
      onEnd: () => console.log('Instrução repetida'),
    });
  }, [isShowingSequence, isPlayerTurn, gameSequence, countdown, speak]);

  const playFeedback = useCallback((message, type = 'neutral') => {
    const prefix = type === 'success' ? 'Parabéns! ' : type === 'error' ? 'Ops! ' : '';
    speak(prefix + message, {
      rate: type === 'success' ? 1.1 : 0.9,
      pitch: type === 'success' ? 1.2 : type === 'error' ? 0.8 : 1,
      onEnd: () => console.log(`Feedback ${type} anunciado`),
    });
  }, [speak]);

  const announceStats = useCallback(() => {
    const statsText = `
      Suas estatísticas atuais:
      Nível ${currentLevel}.
      Pontuação: ${gameStats.score} pontos.
      Sequência atual de acertos: ${gameStats.streak}.
      Precisão: ${getAccuracy()} por cento.
      Total de sequências corretas: ${gameStats.correctSequences} de ${gameStats.totalAttempts} tentativas.
    `;
    speak(statsText, {
      rate: 0.8,
      buttonId: 'stats',
      onEnd: () => console.log('Estatísticas anunciadas'),
    });
  }, [currentLevel, gameStats, speak]);

  const announceSequence = useCallback(() => {
    if (gameSequence.length === 0) {
      speak('Nenhuma sequência disponível no momento.');
      return;
    }
    const shapes = gameSequence.map(shapeId => {
      const shape = PadroesVisuaisConfig.shapes.find(s => s.id === shapeId);
      return shape ? shape.name : 'forma desconhecida';
    });
    const sequenceText = `
      A sequência atual tem ${gameSequence.length} formas:
      ${shapes.join(', ')}.
      ${isShowingSequence ? 'Continue observando para memorizar.' : 'Agora recrie esta sequência na ordem correta.'}
    `;
    speak(sequenceText, {
      rate: 0.8,
      buttonId: 'sequence',
      onEnd: () => console.log('Sequência anunciada'),
    });
  }, [gameSequence, isShowingSequence, speak]);

  const giveHint = useCallback(() => {
    let hint = '';
    if (isShowingSequence) {
      hint = `Dica: Tente criar uma história ou padrão mental com as ${gameSequence.length} formas para facilitar a memorização. Observe as cores e posições com atenção.`;
    } else if (isPlayerTurn) {
      const filledSlots = playerSequence.filter(shape => shape !== undefined).length;
      const remainingSlots = gameSequence.length - filledSlots;
      hint = remainingSlots > 0
        ? `Dica: Você já colocou ${filledSlots} formas. Ainda faltam ${remainingSlots} formas para completar a sequência. Lembre-se da ordem que você memorizou.`
        : 'Dica: Você preencheu todos os slots! Verifique se a ordem está correta antes de confirmar.';
    } else {
      hint = 'Dica: Use o tempo de preparação para se concentrar e se preparar para a próxima sequência.';
    }
    speak(hint, {
      rate: 0.8,
      pitch: 1.1,
      buttonId: 'hint',
      onEnd: () => console.log('Dica fornecida'),
    });
  }, [isShowingSequence, isPlayerTurn, gameSequence, playerSequence, speak]);

  // Funções de métricas e análise
  const processFinalMetrics = useCallback(async () => {
    try {
      console.log('🏁 Processando métricas finais da sessão de Padrões Visuais...');
      const finalSessionData = {
        sessionId,
        userId: user?.id || 'anonymous',
        gameType: 'PadroesVisuais',
        duration: Date.now() - startTime,
        interactions: sessionInteractions,
        sequences: sessionSequences,
        finalStats: gameStats,
        patternData: {
          finalLevel: currentLevel,
          difficulty,
          consecutiveSuccesses,
          totalSequences: sessionSequences.length,
        },
        accuracy: gameStats.correctSequences / Math.max(1, gameStats.totalAttempts),
        averageResponseTime: sessionInteractions.length > 0
          ? sessionInteractions.reduce((sum, i) => sum + (i.responseTime || 0), 0) / sessionInteractions.length
          : 0,
        completeness: 1,
        attentionScore: calculateAttentionScore(),
        sessionComplete: true,
      };
      const finalMetrics = await advancedMetricsEngine.processAdvancedMetrics(
        finalSessionData,
        { id: user?.id || 'anonymous', preferences: {} },
        []
      );
      console.log('📊 Métricas Finais de Padrões Visuais:', finalMetrics);
      if (finalMetrics.metrics.patterns) {
        console.log('🎨 Resumo Final - Análise de Padrões Visuais:', {
          sessionId,
          totalInteractions: sessionInteractions.length,
          totalSequences: sessionSequences.length,
          finalAccuracy: gameStats.correctSequences / Math.max(1, gameStats.totalAttempts),
          patternRecognition: finalMetrics.metrics.patterns.patternRecognition,
          visualMemory: finalMetrics.metrics.patterns.visualMemory,
          spatialProcessing: finalMetrics.metrics.patterns.spatialProcessing,
          insights: finalMetrics.insights,
          recommendations: finalMetrics.recommendations,
        });
      }
    } catch (error) {
      console.warn('Erro ao processar métricas finais:', error);
    }
  }, [sessionId, user, startTime, sessionInteractions, sessionSequences, gameStats, currentLevel, difficulty, consecutiveSuccesses, advancedMetricsEngine]);

  const processAdvancedMetrics = useCallback(async (currentInteraction) => {
    try {
      console.log('🔬 Processando métricas avançadas de padrões visuais...');
      const sessionData = {
        sessionId,
        userId: user?.id || 'anonymous',
        gameType: 'PadroesVisuais',
        duration: Date.now() - startTime,
        interactions: sessionInteractions,
        sequences: sessionSequences,
        patternData: {
          currentLevel,
          difficulty,
          consecutiveSuccesses,
          currentSequence: gameSequence,
          playerResponse: playerSequence,
        },
        accuracy: gameStats.correctSequences / Math.max(1, gameStats.totalAttempts),
        averageResponseTime: sessionInteractions.length > 0
          ? sessionInteractions.reduce((sum, i) => sum + (i.responseTime || 0), 0) / sessionInteractions.length
          : 0,
        completeness: currentInteraction.correct ? 1 : 0,
        attentionScore: calculateAttentionScore(),
      };
      const advancedMetrics = await advancedMetricsEngine.processAdvancedMetrics(
        sessionData,
        { id: user?.id || 'anonymous', preferences: {} },
        []
      );
      console.log('📊 Métricas Avançadas de Padrões Visuais:', advancedMetrics);
      if (advancedMetrics.metrics.patterns) {
        console.log('🎨 Análise de Padrões Visuais:', {
          recognitionAccuracy: advancedMetrics.metrics.patterns.patternRecognition?.overallAccuracy,
          visualMemoryCapacity: advancedMetrics.metrics.patterns.visualMemory?.memoryCapacity,
          spatialProcessing: advancedMetrics.metrics.patterns.spatialProcessing?.spatialOrientation,
          errorPatterns: advancedMetrics.metrics.patterns.errorPatterns,
        });
      }
      if (advancedMetrics.insights?.length > 0) {
        console.log('💡 Insights de Padrões:', advancedMetrics.insights);
      }
      if (advancedMetrics.recommendations?.length > 0) {
        console.log('🎯 Recomendações para Padrões:', advancedMetrics.recommendations);
      }
    } catch (error) {
      console.warn('Erro ao processar métricas avançadas:', error);
    }
  }, [sessionId, user, startTime, sessionInteractions, sessionSequences, gameStats, currentLevel, difficulty, consecutiveSuccesses, gameSequence, playerSequence, advancedMetricsEngine]);

  const calculateAttentionScore = useCallback(() => {
    if (sessionInteractions.length === 0) return 0.5;
    const recentInteractions = sessionInteractions.slice(-5);
    const avgResponseTime = recentInteractions.reduce((sum, i) => sum + (i.responseTime || 0), 0) / recentInteractions.length;
    const accuracy = recentInteractions.filter(i => i.correct).length / recentInteractions.length;
    const timeScore = Math.max(0, Math.min(1, (10000 - avgResponseTime) / 10000));
    return (accuracy + timeScore) / 2;
  }, [sessionInteractions]);

  const recordPatternInteraction = useCallback(async (playerSequence, gameSequence, isCorrect, completionTime) => {
    const interactionData = {
      playerSequence,
      correctSequence: gameSequence,
      isCorrect,
      completionTime,
      timestamp: Date.now(),
      attemptNumber: attemptCount + 1,
      difficulty,
      level: currentLevel,
      sequenceLength: gameSequence.length,
    };
    try {
      await recordMultisensoryInteraction('pattern_interaction', {
        selectedPattern: playerSequence,
        targetPattern: gameSequence,
        isCorrect,
        responseTime: completionTime,
        difficulty,
        level: currentLevel,
        patternType: 'sequence_reproduction',
      });
      await collectorsHub.collectSequenceData({
        ...interactionData,
        gameState: {
          score: gameStats.score,
          totalAttempts: gameStats.totalAttempts,
          correctSequences: gameStats.correctSequences,
          streak: gameStats.streak,
        },
      });
      const newAttemptCount = attemptCount + 1;
      setAttemptCount(newAttemptCount);
      if (newAttemptCount % 3 === 0) {
        await performCognitiveAnalysis();
      }
    } catch (error) {
      console.error('Erro ao coletar dados do padrão:', error);
    }
  }, [recordMultisensoryInteraction, collectorsHub, attemptCount, difficulty, currentLevel, gameStats]);

  const performCognitiveAnalysis = useCallback(async () => {
    try {
      setCognitiveAnalysisVisible(true);
      const analysisData = await collectorsHub.performCompleteAnalysis();
      setAnalysisResults(analysisData);
      setTimeout(() => setCognitiveAnalysisVisible(false), 3000);
    } catch (error) {
      console.error('Erro na análise cognitiva de padrões:', error);
      setCognitiveAnalysisVisible(false);
    }
  }, [collectorsHub]);

  const getAccuracy = useCallback(() => {
    if (gameStats.totalAttempts === 0) return 100;
    return Math.round((gameStats.correctSequences / gameStats.totalAttempts) * 100);
  }, [gameStats]);

  const playShape = useCallback(async (shapeId) => {
    setPlayingShape(shapeId);
    const shape = PadroesVisuaisConfig.shapes.find(s => s.id === shapeId);
    if (!shape) {
      console.warn(`Forma não encontrada: ${shapeId}`);
      setPlayingShape(null);
      return;
    }
    console.log(`🎵 Reproduzindo forma: ${shape.name}`);
    setTimeout(() => setPlayingShape(null), 600);
  }, []);

  const handleDragStart = (e, shapeId) => {
    setDraggedShape(shapeId);
    e.dataTransfer.setData('text/plain', shapeId);
  };

  const handleDragEnd = () => {
    setDraggedShape(null);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDrop = (e, position) => {
    e.preventDefault();
    const shapeId = e.dataTransfer.getData('text/plain');
    if (shapeId && position !== undefined) {
      const newPlayerSequence = [...playerSequence];
      newPlayerSequence[position] = shapeId;
      setPlayerSequence(newPlayerSequence);
      checkSequenceCompletion(newPlayerSequence);
    }
  };

  const checkSequenceCompletion = useCallback((currentSequence) => {
    const filledSlots = currentSequence.filter(shape => shape !== undefined);
    if (filledSlots.length === gameSequence.length) {
      const completionTime = startTime ? Date.now() - startTime : 0;
      const isCorrect = gameSequence.every((shape, index) => currentSequence[index] === shape);
      recordPatternInteraction(currentSequence, gameSequence, isCorrect, completionTime);
      if (isCorrect) {
        handleCorrectSequence();
      } else {
        handleIncorrectSequence();
      }
    }
  }, [gameSequence, startTime, recordPatternInteraction]);

  const clearPlayerSequence = () => {
    setPlayerSequence([]);
  };

  const handleCorrectSequence = useCallback(() => {
    const basePoints = 20;
    const levelBonus = currentLevel * 10;
    const lengthBonus = gameSequence.length * 5;
    const points = basePoints + levelBonus + lengthBonus;
    const responseTime = Date.now() - startTime;
    const interaction = {
      timestamp: Date.now(),
      correct: true,
      sequenceCorrect: true,
      responseTime,
      sequenceLength: gameSequence.length,
      level: currentLevel,
      difficulty,
      gameSequence: [...gameSequence],
      playerSequence: [...playerSequence],
      points,
      consecutiveSuccesses: consecutiveSuccesses + 1,
    };
    setSessionInteractions(prev => [...prev, interaction]);
    setSessionSequences(prev => [...prev, gameSequence]);
    setGameStats(prev => ({
      ...prev,
      score: prev.score + points,
      correctSequences: prev.correctSequences + 1,
      totalAttempts: prev.totalAttempts + 1,
      level: prev.level + 1,
      streak: prev.streak + 1,
    }));
    setCurrentLevel(prev => prev + 1);
    setConsecutiveSuccesses(prev => prev + 1);
    if ((consecutiveSuccesses + 1) % 3 === 0) {
      processAdvancedMetrics(interaction);
    }
    const message = PadroesVisuaisConfig.encouragingMessages[
      Math.floor(Math.random() * PadroesVisuaisConfig.encouragingMessages.length)
    ];
    playFeedback(`${message} Você ganhou ${points} pontos!`, 'success');
    setTimeout(() => {
      setFeedback(null);
      generateNewLevel();
    }, 3000);
  }, [gameSequence, playerSequence, currentLevel, difficulty, consecutiveSuccesses, startTime, generateNewLevel, playFeedback, processAdvancedMetrics]);

  const handleIncorrectSequence = useCallback(() => {
    const responseTime = Date.now() - startTime;
    const interaction = {
      timestamp: Date.now(),
      correct: false,
      sequenceCorrect: false,
      responseTime,
      sequenceLength: gameSequence.length,
      level: currentLevel,
      difficulty,
      gameSequence: [...gameSequence],
      playerSequence: [...playerSequence],
      points: 0,
      consecutiveSuccesses: 0,
      errorDetails: analyzeSequenceError(gameSequence, playerSequence),
    };
    setSessionInteractions(prev => [...prev, interaction]);
    setSessionSequences(prev => [...prev, gameSequence]);
    setGameStats(prev => ({
      ...prev,
      totalAttempts: prev.totalAttempts + 1,
      streak: 0,
    }));
    setConsecutiveSuccesses(0);
    processAdvancedMetrics(interaction);
    playFeedback('Sequência incorreta. Observe novamente a sequência correta!', 'error');
    setTimeout(() => {
      setIsShowingSequence(true);
      setIsPlayerTurn(false);
      setTimeout(() => {
        setPlayerSequence([]);
        setFeedback(null);
        setIsShowingSequence(false);
        setIsPlayerTurn(true);
      }, 5000);
    }, 1000);
  }, [gameSequence, playerSequence, currentLevel, difficulty, startTime, playFeedback, processAdvancedMetrics]);

  const analyzeSequenceError = (correctSequence, playerSequence) => {
    const errors = {
      positionErrors: [],
      shapeErrors: [],
      orderErrors: [],
      totalErrors: 0,
    };
    correctSequence.forEach((correctShape, index) => {
      const playerShape = playerSequence[index];
      if (playerShape !== correctShape) {
        errors.totalErrors++;
        errors.positionErrors.push(index);
        if (playerShape && correctSequence.includes(playerShape)) {
          errors.orderErrors.push({
            position: index,
            expected: correctShape,
            actual: playerShape,
            type: 'order',
          });
        } else if (playerShape) {
          errors.shapeErrors.push({
            position: index,
            expected: correctShape,
            actual: playerShape,
            type: 'shape',
          });
        } else {
          errors.shapeErrors.push({
            position: index,
            expected: correctShape,
            actual: null,
            type: 'missing',
          });
        }
      }
    });
    return errors;
  };

  const initializeActivity = useCallback((activityIndex) => {
    const activityKeys = Object.keys(ACTIVITY_CONFIG);
    const activityKey = activityKeys[activityIndex];
    
    setGameState(prev => ({
      ...prev,
      activityIndex: activityIndex
    }));
    setSequenceToReproduce([]);
    setPatternToComplete(null);
    setConstructionRules(null);
    setClassificationCriteria(null);
    setTransformationTarget(null);
    setAnomalyPattern(null);
    setPlayerConstruction([]);
    setSelectedTransformation(null);
    setClassificationGroups([]);
    setFeedback(null);
    if (activityKey) {
      console.log(`🎯 Inicializando atividade: ${activityKey}`);
    }
  }, []);

  useEffect(() => {
    if (gameStarted && !showStartScreen) {
      initializeActivity(0);
    }
  }, [gameStarted, showStartScreen, initializeActivity]);

  // Auto-start desabilitado para mostrar tela de dificuldade
  /*
  useEffect(() => {
    const autoStart = () => {
      setShowStartScreen(false);
      setGameStarted(true);
      setDifficulty('easy');
      setStartTime(Date.now());
    };
    const timer = setTimeout(autoStart, 1000);
    return () => clearTimeout(timer);
  }, []);
  */

  useEffect(() => {
    if (currentLevel > 10 && gameStarted) {
      const finalizeMultisensorySession = async () => {
        try {
          const multisensoryReport = await finalizeMultisensory({
            finalScore: gameStats.score,
            finalAccuracy: gameStats.correctSequences / gameStats.totalAttempts,
            totalInteractions: gameStats.totalAttempts,
            sessionDuration: Date.now() - startTime,
            difficulty,
          });
          console.log('🔄 PadroesVisuais: Relatório multissensorial final:', multisensoryReport);
        } catch (error) {
          console.warn('⚠️ PadroesVisuais: Erro ao finalizar sessão multissensorial:', error);
        }
      };
      finalizeMultisensorySession();
    }
  }, [currentLevel, gameStarted, finalizeMultisensory, gameStats, startTime, difficulty]);

  useEffect(() => {
    const savedTTSState = localStorage.getItem('padroesVisuaisTTS');
    if (savedTTSState !== null) {
      setTtsActive(savedTTSState === 'true');
    }
  }, []);

  useEffect(() => {
    return () => {
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
      console.log('🔇 TTS parado ao sair do jogo Padrões Visuais');
    };
  }, []);

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden && 'speechSynthesis' in window) {
        window.speechSynthesis.cancel();
        setIsSpeaking(false);
        setCurrentSpeechButton(null);
      }
    };
    const handlePageHide = () => {
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
    };
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('pagehide', handlePageHide);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('pagehide', handlePageHide);
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, []);

  // =====================================================
  // 🎨 INTERFACE MODERNA DAS ATIVIDADES
  // =====================================================
  
  const renderEnhancedActivityInterface = () => {
    const activityKeys = Object.keys(ACTIVITY_CONFIG);
    const activityKey = activityKeys[gameState.activityIndex || 0];
    
    // 🎯 LAYOUT BASEADO NO COLORMATCH - ESTRUTURA PADRÃO PORTAL BETINA V3
    return (
      <div className={styles.gameContainer}>
        {/* Question Area - Cabeçalho e instrução */}
        <div className={styles.questionArea}>
          <div className={styles.questionHeader}>
            <h2 className={styles.activityTitle}>
              {ACTIVITY_CONFIG[activityKey]?.icon || '🎯'} {ACTIVITY_CONFIG[activityKey]?.name || 'Padrões Visuais'}
            </h2>
            <p className={styles.activityDescription}>
              {ACTIVITY_CONFIG[activityKey]?.description || 'Reconheça e complete padrões visuais'}
            </p>
          </div>
        </div>

        {/* Objects Display - Conteúdo principal da atividade */}
        <div className={styles.objectsDisplay}>
          {renderActivityContent(activityKey)}
        </div>

        {/* Answer Options - Opções de resposta */}
        <div className={styles.answerOptions}>
          {renderAnswerOptions(activityKey)}
        </div>
      </div>
    );
  };

  const renderActivityContent = (activityKey) => {
    switch (activityKey) {
      case 'reproducao_sequencias':
        return (
          <div className={styles.sequenceContent}>
            {/* Sequência para memorizar */}
            {isShowingSequence && gameSequence.length > 0 && (
              <div className={styles.targetDisplay}>
                <h4 className={styles.displayTitle}>📝 Memorize esta sequência:</h4>
                <div className={styles.objectGrid}>
                  {gameSequence.map((shape, index) => (
                    <div 
                      key={index} 
                      className={`${styles.objectItem} ${styles.memorizing} ${playingShape === shape ? styles.active : ''}`}
                      style={{ 
                        animationDelay: `${index * 0.2}s`,
                        transform: playingShape === shape ? 'scale(1.2)' : 'scale(1)'
                      }}
                    >
                      {getShapeEmoji(shape)}
                    </div>
                  ))}
                </div>
                {countdown && (
                  <div className={styles.timer}>
                    ⏱️ {countdown} segundos restantes
                  </div>
                )}
              </div>
            )}
            
            {/* Área de reprodução */}
            {isPlayerTurn && (
              <div className={styles.reproductionDisplay}>
                <h4 className={styles.displayTitle}>🎯 Recrie a sequência:</h4>
                <div className={styles.objectGrid}>
                  {Array.from({ length: gameSequence.length }, (_, index) => (
                    <div 
                      key={index} 
                      className={`${styles.objectSlot} ${playerSequence[index] ? styles.filled : styles.empty}`}
                      onDrop={(e) => handleDrop(e, index)}
                      onDragOver={handleDragOver}
                      data-slot={index}
                    >
                      <div className={styles.slotNumber}>{index + 1}</div>
                      <div className={styles.slotContent}>
                        {playerSequence[index] ? getShapeEmoji(playerSequence[index]) : '?'}
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className={styles.progressBar}>
                  <div className={styles.progressFill} style={{
                    width: `${(playerSequence.filter(slot => slot).length / gameSequence.length) * 100}%`
                  }}></div>
                  <span className={styles.progressText}>
                    {playerSequence.filter(slot => slot).length} / {gameSequence.length}
                  </span>
                </div>
              </div>
            )}
          </div>
        );
        
      case 'completar_padroes':
        return (
          <div className={styles.patternCompletion}>
            <h4 className={styles.displayTitle}>🧩 Complete o Padrão</h4>
            <div className={styles.objectGrid}>
              <div className={styles.objectItem}>🔴</div>
              <div className={styles.objectItem}>🔵</div>
              <div className={styles.objectItem}>🔴</div>
              <div className={styles.objectItem}>🔵</div>
              <div className={`${styles.objectItem} ${styles.missing}`}>?</div>
            </div>
          </div>
        );
        
      case 'construcao_padroes':
        return (
          <div className={styles.constructionContent}>
            <div className={styles.targetPattern}>
              <h4 className={styles.displayTitle}>🎯 Padrão objetivo:</h4>
              <div className={styles.objectGrid}>
                <div className={styles.objectItem}>{getShapeEmoji('circle')}</div>
                <div className={styles.objectItem}>{getShapeEmoji('square')}</div>
                <div className={styles.objectItem}>{getShapeEmoji('triangle')}</div>
              </div>
            </div>
            
            <div className={styles.constructionArea}>
              <h4 className={styles.displayTitle}>🔨 Sua construção:</h4>
              <div className={styles.objectGrid}>
                {Array.from({ length: 3 }, (_, index) => (
                  <div 
                    key={index}
                    className={`${styles.objectSlot} ${playerConstruction[index] ? styles.filled : styles.empty}`}
                    onDrop={(e) => handleDrop(e, index)}
                    onDragOver={handleDragOver}
                    data-slot={index}
                  >
                    <div className={styles.slotNumber}>{index + 1}</div>
                    <div className={styles.slotContent}>
                      {playerConstruction[index] ? getShapeEmoji(playerConstruction[index]) : '?'}
                    </div>
                  </div>
                ))}
              </div>
              
              <div className={styles.progressBar}>
                <div className={styles.progressFill} style={{
                  width: `${((playerConstruction || []).filter(slot => slot).length / 3) * 100}%`
                }}></div>
                <span className={styles.progressText}>
                  {(playerConstruction || []).filter(slot => slot).length} / 3
                </span>
              </div>
            </div>
          </div>
        );
        
      case 'classificacao_visual':
        return (
          <div className={styles.classificationContent}>
            <h4 className={styles.displayTitle}>🗂️ Classifique por Categorias</h4>
            <div className={styles.objectGrid}>
              {/* Elementos a serem classificados */}
              <div className={styles.objectItem}>🔴</div>
              <div className={styles.objectItem}>🔺</div>
              <div className={styles.objectItem}>🔵</div>
              <div className={styles.objectItem}>🟦</div>
              <div className={styles.objectItem}>🟢</div>
              <div className={styles.objectItem}>🔶</div>
            </div>
            
            {/* Categorias */}
            <div className={styles.categoriesDisplay}>
              <div className={styles.category}>
                <h5>Círculos</h5>
                <div className={styles.categorySlots}>
                  {/* Slots para círculos */}
                </div>
              </div>
              <div className={styles.category}>
                <h5>Formas Angulares</h5>
                <div className={styles.categorySlots}>
                  {/* Slots para formas angulares */}
                </div>
              </div>
            </div>
          </div>
        );
        
      case 'transformacao_padroes':
        return (
          <div className={styles.transformationContent}>
            <h4 className={styles.displayTitle}>🔄 Aplique a Transformação</h4>
            <div className={styles.transformationDisplay}>
              <div className={styles.originalPattern}>
                <h5>Padrão Original:</h5>
                <div className={styles.objectGrid}>
                  <div className={styles.objectItem}>🔴</div>
                  <div className={styles.objectItem}>🔵</div>
                  <div className={styles.objectItem}>🟢</div>
                </div>
              </div>
              
              <div className={styles.transformationRule}>
                <div className={styles.ruleIcon}>➡️</div>
                <p>Rotacione 90° à direita</p>
              </div>
              
              <div className={styles.resultPattern}>
                <h5>Resultado:</h5>
                <div className={styles.objectGrid}>
                  {transformationResult.map((item, index) => (
                    <div key={index} className={`${styles.objectSlot} ${item ? styles.filled : styles.empty}`}>
                      {item || '?'}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );
        
      case 'deteccao_anomalias':
        return (
          <div className={styles.anomalyContent}>
            <h4 className={styles.displayTitle}>🔍 Encontre as Anomalias</h4>
            <div className={styles.objectGrid}>
              <div className={styles.objectItem}>🔴</div>
              <div className={styles.objectItem}>🔴</div>
              <div className={`${styles.objectItem} ${styles.anomaly}`}>🔵</div>
              <div className={styles.objectItem}>🔴</div>
              <div className={styles.objectItem}>🔴</div>
              <div className={`${styles.objectItem} ${styles.anomaly}`}>🟢</div>
              <div className={styles.objectItem}>🔴</div>
              <div className={styles.objectItem}>🔴</div>
            </div>
            
            <div className={styles.anomalyCounter}>
              <p>🎯 Anomalias encontradas: {detectedAnomalies.length} / 2</p>
            </div>
          </div>
        );
        
      default:
        return (
          <div className={styles.defaultContent}>
            <h4 className={styles.displayTitle}>🎮 Selecione uma Atividade</h4>
            <p>Escolha uma das atividades de padrões visuais para começar</p>
          </div>
        );
    }
  };

  const renderAnswerOptions = (activityKey) => {
    switch (activityKey) {
      case 'reproducao_sequencias':
        return isPlayerTurn ? (
          <div className={styles.answerGrid}>
            <h4 className={styles.optionsTitle}>🎨 Formas Disponíveis:</h4>
            <div className={styles.optionsContainer}>
              {PadroesVisuaisConfig.shapes.map((shape) => (
                <button
                  key={shape.id}
                  className={`${styles.answerOption} ${draggedShape === shape.id ? styles.dragging : ''}`}
                  draggable
                  onDragStart={(e) => handleDragStart(e, shape.id)}
                  onDragEnd={handleDragEnd}
                  onClick={() => {
                    const emptySlot = playerSequence.findIndex(slot => !slot);
                    if (emptySlot !== -1) {
                      handleAddShapeToPattern(shape.id, emptySlot);
                    }
                  }}
                  title={`Forma: ${shape.name}`}
                >
                  <div className={styles.optionContent}>
                    <div className={styles.optionEmoji}>{shape.emoji}</div>
                    <div className={styles.optionLabel}>{shape.name}</div>
                  </div>
                </button>
              ))}
            </div>
            <div className={styles.gameHint}>
              💡 Clique ou arraste as formas para os slots na ordem correta
            </div>
          </div>
        ) : null;
        
      case 'completar_padroes':
        return (
          <div className={styles.answerGrid}>
            <h4 className={styles.optionsTitle}>Escolha a resposta:</h4>
            <div className={styles.optionsContainer}>
              {['🔴', '🔵', '🟢', '🟡'].map((option, index) => (
                <button 
                  key={index}
                  className={styles.answerOption}
                  onClick={() => selectPattern(option)}
                >
                  <div className={styles.optionContent}>
                    <div className={styles.optionEmoji}>{option}</div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        );
        
      case 'construcao_padroes':
        return (
          <div className={styles.answerGrid}>
            <h4 className={styles.optionsTitle}>🎨 Formas disponíveis:</h4>
            <div className={styles.optionsContainer}>
              {PadroesVisuaisConfig.shapes.map((shape) => (
                <button
                  key={shape.id}
                  className={`${styles.answerOption} ${draggedShape === shape.id ? styles.dragging : ''}`}
                  draggable
                  onDragStart={(e) => handleDragStart(e, shape.id)}
                  onDragEnd={handleDragEnd}
                  onClick={() => {
                    const emptySlot = (playerConstruction || []).findIndex(slot => !slot);
                    if (emptySlot !== -1) {
                      const newConstruction = [...(playerConstruction || [])];
                      newConstruction[emptySlot] = shape.id;
                      setPlayerConstruction(newConstruction);
                    }
                  }}
                  title={`Forma: ${shape.name}`}
                >
                  <div className={styles.optionContent}>
                    <div className={styles.optionEmoji}>{shape.emoji}</div>
                    <div className={styles.optionLabel}>{shape.name}</div>
                  </div>
                </button>
              ))}
            </div>
            <div className={styles.gameHint}>
              💡 Arraste as formas para os slots na ordem correta para recriar o padrão
            </div>
          </div>
        );
        
      case 'classificacao_visual':
        return (
          <div className={styles.answerGrid}>
            <h4 className={styles.optionsTitle}>📂 Categorias:</h4>
            <div className={styles.categoriesContainer}>
              <div className={styles.categoryBox}>
                <h5>Círculos</h5>
                <div className={styles.categoryDropZone}
                     onDrop={(e) => handleCategoryDrop(e, 'circles')}
                     onDragOver={handleDragOver}>
                  {/* Elementos classificados como círculos */}
                </div>
              </div>
              <div className={styles.categoryBox}>
                <h5>Formas Angulares</h5>
                <div className={styles.categoryDropZone}
                     onDrop={(e) => handleCategoryDrop(e, 'angular')}
                     onDragOver={handleDragOver}>
                  {/* Elementos classificados como formas angulares */}
                </div>
              </div>
            </div>
          </div>
        );
        
      case 'transformacao_padroes':
        return (
          <div className={styles.answerGrid}>
            <h4 className={styles.optionsTitle}>🔄 Opções de Transformação:</h4>
            <div className={styles.optionsContainer}>
              {['↻ 90° Esquerda', '↺ 90° Direita', '↕️ Espelhar V', '↔️ Espelhar H'].map((option, index) => (
                <button 
                  key={index}
                  className={`${styles.answerOption} ${selectedTransformation === option ? styles.selected : ''}`}
                  onClick={() => setSelectedTransformation(option)}
                >
                  <div className={styles.optionContent}>
                    <div className={styles.optionLabel}>{option}</div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        );
        
      case 'deteccao_anomalias':
        return (
          <div className={styles.answerGrid}>
            <h4 className={styles.optionsTitle}>🎯 Ações:</h4>
            <div className={styles.optionsContainer}>
              <button 
                className={styles.answerOption}
                onClick={() => {
                  // Resetar detecção
                  setDetectedAnomalies([]);
                }}
              >
                <div className={styles.optionContent}>
                  <div className={styles.optionEmoji}>🔄</div>
                  <div className={styles.optionLabel}>Resetar</div>
                </div>
              </button>
              <button 
                className={`${styles.answerOption} ${styles.primary}`}
                onClick={() => {
                  // Verificar resposta
                  console.log('Verificando anomalias detectadas:', detectedAnomalies);
                }}
              >
                <div className={styles.optionContent}>
                  <div className={styles.optionEmoji}>✅</div>
                  <div className={styles.optionLabel}>Verificar</div>
                </div>
              </button>
            </div>
            <div className={styles.gameHint}>
              💡 Clique nos elementos que parecem diferentes do padrão principal
            </div>
          </div>
        );
        
      default:
        return (
          <div className={styles.answerGrid}>
            <h4 className={styles.optionsTitle}>🎮 Aguardando...</h4>
            <p>Selecione uma atividade para ver as opções disponíveis</p>
          </div>
        );
    }
  };

  // =====================================================
  // 🎮 FUNÇÕES DE INTERAÇÃO DAS ATIVIDADES
  // =====================================================

  // Renderizar workspace de classificação
  const renderClassificationWorkspace = () => {
    return (
      <div className={styles.classificationWorkspace}>
        <div className={styles.elementsToClassify}>
                <h4>🎯 Elementos para classificar:</h4>
                <div className={styles.elementGrid}>
                  {['circle', 'square', 'circle', 'triangle', 'square', 'triangle'].map((shape, index) => (
                    <div
                      key={index}
                      className={`${styles.classificationElement} ${selectedForClassification === `${shape}-${index}` ? styles.selected : ''}`}
                      draggable
                      onDragStart={(e) => handleDragStart(e, `${shape}-${index}`)}
                      onDragEnd={handleDragEnd}
                      onClick={() => setSelectedForClassification(`${shape}-${index}`)}
                      title={`Elemento: ${shape}`}
                    >
                      <div className={styles.elementEmoji}>{getShapeEmoji(shape)}</div>
                      <div className={styles.elementIndex}>{index + 1}</div>
                    </div>
                  ))}
                </div>
                
                <div className={styles.progressIndicator}>
                  <span>Classificados: {Object.values(classificationResults || {}).filter(v => v).length} / 6</span>
                </div>
        </div>

        <div className={styles.classificationGroups}>
          <div className={styles.classGroup}>
                  <h5>🔴 Círculos</h5>
                  <div 
                    className={`${styles.dropZone} ${styles.circleZone}`}
                    onDrop={(e) => handleClassificationDrop(e, 'circle')}
                    onDragOver={handleDragOver}
                    data-group="circle"
                  >
                    <div className={styles.zoneContent}>
                      {Object.entries(classificationResults || {})
                        .filter(([key, value]) => value === 'circle')
                        .map(([key]) => {
                          const shape = key.split('-')[0];
                          return (
                            <div key={key} className={styles.classifiedElement}>
                              {getShapeEmoji(shape)}
                            </div>
                          );
                        })}
                      {Object.keys(classificationResults || {}).filter(key => 
                        classificationResults[key] === 'circle'
                      ).length === 0 && (
                        <div className={styles.zoneHint}>Arraste círculos aqui</div>
                      )}
                    </div>
                  </div>
          </div>

          <div className={styles.classGroup}>
                  <h5>🔵 Quadrados</h5>
                  <div 
                    className={`${styles.dropZone} ${styles.squareZone}`}
                    onDrop={(e) => handleClassificationDrop(e, 'square')}
                    onDragOver={handleDragOver}
                    data-group="square"
                  >
                    <div className={styles.zoneContent}>
                      {Object.entries(classificationResults || {})
                        .filter(([key, value]) => value === 'square')
                        .map(([key]) => {
                          const shape = key.split('-')[0];
                          return (
                            <div key={key} className={styles.classifiedElement}>
                              {getShapeEmoji(shape)}
                            </div>
                          );
                        })}
                      {Object.keys(classificationResults || {}).filter(key => 
                        classificationResults[key] === 'square'
                      ).length === 0 && (
                        <div className={styles.zoneHint}>Arraste quadrados aqui</div>
                      )}
                    </div>
                  </div>
          </div>

          <div className={styles.classGroup}>
                  <h5>🟢 Triângulos</h5>
                  <div 
                    className={`${styles.dropZone} ${styles.triangleZone}`}
                    onDrop={(e) => handleClassificationDrop(e, 'triangle')}
                    onDragOver={handleDragOver}
                    data-group="triangle"
                  >
                    <div className={styles.zoneContent}>
                      {Object.entries(classificationResults || {})
                        .filter(([key, value]) => value === 'triangle')
                        .map(([key]) => {
                          const shape = key.split('-')[0];
                          return (
                            <div key={key} className={styles.classifiedElement}>
                              {getShapeEmoji(shape)}
                            </div>
                          );
                        })}
                      {Object.keys(classificationResults || {}).filter(key => 
                        classificationResults[key] === 'triangle'
                      ).length === 0 && (
                        <div className={styles.zoneHint}>Arraste triângulos aqui</div>
                      )}
                    </div>
                  </div>
          </div>
        </div>

        <div className={styles.gameHints}>
          <p>💡 Dica: Arraste cada elemento para o grupo da forma correspondente</p>
        </div>
      </div>
    );
  };

  // Renderizar conteúdo da atividade - SEGUINDO PADRÃO MEMORY GAME
  const renderActivityInterface = () => {
    const activityKey = gameState.currentActivity;

    // Se não há atividade definida, mostrar atividade padrão
    if (!activityKey) {
      return renderCompletionPatterns();
    }

    switch (activityKey) {
      case 'sequence_patterns':
        return renderSequencePatterns();
      case 'completion_patterns':
        return renderCompletionPatterns();
      case 'geometric_patterns':
        return renderGeometricPatterns();
      case 'color_patterns':
        return renderColorPatterns();
      case 'complex_patterns':
        return renderComplexPatterns();
      default:
        return renderCompletionPatterns();
    }
  };

  // 🔄 PADRÕES SEQUENCIAIS - SEGUINDO PADRÃO MEMORY GAME
  const renderSequencePatterns = () => {
    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>🔄 Padrões Sequenciais</h2>
          <p className={styles.instructions}>
            {isShowingSequence
              ? "Observe a sequência de formas!"
              : isPlayerTurn
                ? "Recrie a sequência na ordem correta."
                : "Clique em 'Nova Sequência' para começar."}
          </p>
        </div>

        {/* Sequência para memorizar - seguindo padrão Memory Game */}
        {isShowingSequence && gameSequence.length > 0 && (
          <div className={styles.sequenceDisplay}>
            <h4 className={styles.displayTitle}>📝 Memorize esta sequência:</h4>
            <div className={styles.objectGrid}>
              {gameSequence.map((shape, index) => (
                <div
                  key={index}
                  className={`${styles.objectItem} ${styles.memorizing} ${playingShape === shape ? styles.active : ''}`}
                  style={{
                    animationDelay: `${index * 0.2}s`,
                    transform: playingShape === shape ? 'scale(1.2)' : 'scale(1)'
                  }}
                >
                  {PadroesVisuaisConfig.shapes.find(s => s.id === shape)?.emoji || '❓'}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Área de resposta do jogador - seguindo padrão Memory Game */}
        {isPlayerTurn && (
          <div className={styles.playerResponseArea}>
            <h4 className={styles.responseTitle}>🎯 Sua sequência:</h4>
            <div className={styles.responseGrid}>
              {Array.from({ length: gameSequence.length }, (_, index) => (
                <div
                  key={index}
                  className={`${styles.responseSlot} ${playerSequence[index] ? styles.filled : styles.empty}`}
                  onDrop={(e) => handleDrop(e, index)}
                  onDragOver={handleDragOver}
                >
                  {playerSequence[index] ?
                    PadroesVisuaisConfig.shapes.find(s => s.id === playerSequence[index])?.emoji || '❓'
                    : '?'
                  }
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Formas disponíveis - seguindo padrão Memory Game */}
        {isPlayerTurn && (
          <div className={styles.optionsArea}>
            <h4 className={styles.optionsTitle}>🎨 Formas Disponíveis:</h4>
            <div className={styles.optionsGrid}>
              {PadroesVisuaisConfig.shapes.map((shape) => (
                <button
                  key={shape.id}
                  className={`${styles.optionButton} ${draggedShape === shape.id ? styles.dragging : ''}`}
                  draggable
                  onDragStart={(e) => handleDragStart(e, shape.id)}
                  onDragEnd={handleDragEnd}
                  onClick={() => {
                    const emptySlot = playerSequence.findIndex(slot => !slot);
                    if (emptySlot !== -1) {
                      handleAddShapeToPattern(shape.id, emptySlot);
                    }
                  }}
                >
                  <div className={styles.optionEmoji}>{shape.emoji}</div>
                  <div className={styles.optionLabel}>{shape.name}</div>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  // 🧩 COMPLETAR PADRÕES - SEGUINDO PADRÃO MEMORY GAME
  const renderCompletionPatterns = () => {
    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>🧩 Completar Padrões</h2>
          <p className={styles.instructions}>
            Complete o padrão visual identificando o elemento faltante
          </p>
        </div>

        {/* Padrão a ser completado - seguindo padrão Memory Game */}
        <div className={styles.patternDisplay}>
          <div className={styles.patternGrid}>
            <div className={styles.patternItem}>🔴</div>
            <div className={styles.patternItem}>🔵</div>
            <div className={styles.patternItem}>🟢</div>
            <div className={styles.patternItem}>🔴</div>
            <div className={styles.patternItem}>🔵</div>
            <div className={`${styles.patternItem} ${styles.missing}`}>?</div>
          </div>
        </div>

        {/* Opções de resposta - seguindo padrão Memory Game */}
        <div className={styles.optionsArea}>
          <h4 className={styles.optionsTitle}>🎨 Escolha a opção correta:</h4>
          <div className={styles.optionsGrid}>
            <button
              className={styles.optionButton}
              onClick={() => selectPattern('🟢')}
            >
              🟢
            </button>
            <button
              className={styles.optionButton}
              onClick={() => selectPattern('🟡')}
            >
              🟡
            </button>
            <button
              className={styles.optionButton}
              onClick={() => selectPattern('🔺')}
            >
              🔺
            </button>
          </div>
        </div>
      </div>
    );
  };

  // 🔷 PADRÕES GEOMÉTRICOS - SEGUINDO PADRÃO MEMORY GAME
  const renderGeometricPatterns = () => {
    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>🔷 Padrões Geométricos</h2>
          <p className={styles.instructions}>
            Identifique e continue o padrão geométrico
          </p>
        </div>

        <div className={styles.patternDisplay}>
          <div className={styles.patternGrid}>
            <div className={styles.patternItem}>🔺</div>
            <div className={styles.patternItem}>🔴</div>
            <div className={styles.patternItem}>🔷</div>
            <div className={styles.patternItem}>🔺</div>
            <div className={styles.patternItem}>🔴</div>
            <div className={`${styles.patternItem} ${styles.missing}`}>?</div>
          </div>
        </div>

        <div className={styles.optionsArea}>
          <h4 className={styles.optionsTitle}>🎨 Escolha a forma correta:</h4>
          <div className={styles.optionsGrid}>
            <button className={styles.optionButton} onClick={() => selectPattern('🔷')}>🔷</button>
            <button className={styles.optionButton} onClick={() => selectPattern('🔶')}>🔶</button>
            <button className={styles.optionButton} onClick={() => selectPattern('🟦')}>🟦</button>
          </div>
        </div>
      </div>
    );
  };

  // 🌈 PADRÕES DE CORES - SEGUINDO PADRÃO MEMORY GAME
  const renderColorPatterns = () => {
    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>🌈 Padrões de Cores</h2>
          <p className={styles.instructions}>
            Complete a sequência de cores seguindo o padrão
          </p>
        </div>

        <div className={styles.patternDisplay}>
          <div className={styles.patternGrid}>
            <div className={styles.patternItem}>🔴</div>
            <div className={styles.patternItem}>🟡</div>
            <div className={styles.patternItem}>🔵</div>
            <div className={styles.patternItem}>🔴</div>
            <div className={styles.patternItem}>🟡</div>
            <div className={`${styles.patternItem} ${styles.missing}`}>?</div>
          </div>
        </div>

        <div className={styles.optionsArea}>
          <h4 className={styles.optionsTitle}>🎨 Escolha a cor correta:</h4>
          <div className={styles.optionsGrid}>
            <button className={styles.optionButton} onClick={() => selectPattern('🔵')}>🔵</button>
            <button className={styles.optionButton} onClick={() => selectPattern('🟢')}>🟢</button>
            <button className={styles.optionButton} onClick={() => selectPattern('🟣')}>🟣</button>
          </div>
        </div>
      </div>
    );
  };

  // 🧠 PADRÕES COMPLEXOS - SEGUINDO PADRÃO MEMORY GAME
  const renderComplexPatterns = () => {
    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>🧠 Padrões Complexos</h2>
          <p className={styles.instructions}>
            Analise o padrão complexo e complete a sequência
          </p>
        </div>

        <div className={styles.patternDisplay}>
          <div className={styles.complexPatternGrid}>
            <div className={styles.patternRow}>
              <div className={styles.patternItem}>🔴🔺</div>
              <div className={styles.patternItem}>🔵🔷</div>
              <div className={styles.patternItem}>🟢🟢</div>
            </div>
            <div className={styles.patternRow}>
              <div className={styles.patternItem}>🔺🔴</div>
              <div className={styles.patternItem}>🔷🔵</div>
              <div className={`${styles.patternItem} ${styles.missing}`}>?</div>
            </div>
          </div>
        </div>

        <div className={styles.optionsArea}>
          <h4 className={styles.optionsTitle}>🎨 Escolha o padrão correto:</h4>
          <div className={styles.optionsGrid}>
            <button className={styles.optionButton} onClick={() => selectPattern('🟢🟢')}>🟢🟢</button>
            <button className={styles.optionButton} onClick={() => selectPattern('🟡🟡')}>🟡🟡</button>
            <button className={styles.optionButton} onClick={() => selectPattern('🟣🟣')}>🟣🟣</button>
          </div>
        </div>
      </div>
    );
  };

  // =====================================================
  // 🎮 FUNÇÕES DE INTERAÇÃO DAS ATIVIDADES
  // =====================================================
  
  // Adicionar forma ao padrão
  const handleAddShapeToPattern = (shape, index) => {
    console.log('Forma adicionada:', shape, 'posição:', index);
    
    const newPlayerSequence = [...playerSequence];
    newPlayerSequence[index] = shape;
    setPlayerSequence(newPlayerSequence);
    
    // Verificar se completou a sequência
    checkSequenceCompletion(newPlayerSequence);
  };

  // Função para selecionar padrão na atividade de completar padrões
  const selectPattern = (pattern) => {
    console.log('Padrão selecionado:', pattern);
    
    // Verificar se é a resposta correta (🔴 no exemplo)
    const isCorrect = pattern === '🔴';
    
    if (isCorrect) {
      setFeedback({
        type: 'success',
        message: 'Padrão correto! 🎉',
        score: 20
      });
      playFeedback('Padrão completado corretamente!', 'success');
    } else {
      setFeedback({
        type: 'error',
        message: 'Padrão incorreto. Tente novamente!'
      });
      playFeedback('Padrão incorreto.', 'error');
    }
  };

  // Função para lidar com drops de categoria na classificação visual
  const handleCategoryDrop = (e, category) => {
    e.preventDefault();
    const item = e.dataTransfer.getData('text/plain');
    console.log('Item classificado:', item, 'categoria:', category);
    
    // Atualizar resultados de classificação
    const newResults = { ...classificationResults };
    newResults[item] = category;
    setClassificationResults(newResults);
    
    speak(`Item classificado como ${category}`);
  };

  // Toggle classificação
  const toggleClassification = (element, type) => {
    console.log('Classificação:', element, type);
    
    // Atualizar grupos de classificação
    const newGroups = [...classificationGroups];
    const elementIndex = newGroups.findIndex(g => g.element === element);
    
    if (elementIndex >= 0) {
      newGroups[elementIndex].type = type;
    } else {
      newGroups.push({ element, type });
    }
    
    setClassificationGroups(newGroups);
    
    // Feedback de classificação
    speak(`${element} classificado como ${type}`);
  };

  // Selecionar transformação
  const selectTransformation = (option) => {
    console.log('Transformação selecionada:', option);
    
    setSelectedTransformation(option);
    
    // Verificar se a transformação está correta
    const isCorrect = option === transformationTarget;
    
    if (isCorrect) {
      setFeedback({
        type: 'success',
        message: 'Transformação correta! 🔄',
        score: 15
      });
      playFeedback('Transformação correta!', 'success');
    } else {
      setFeedback({
        type: 'error',
        message: 'Transformação incorreta. Tente novamente!'
      });
      playFeedback('Transformação incorreta.', 'error');
    }
  };

  // Toggle anomalia
  const toggleAnomaly = (element, type) => {
    console.log('Verificando anomalia:', element, type);
    
    // Verificar se é realmente uma anomalia
    const isAnomaly = type === 'anomaly';
    
    if (isAnomaly) {
      setFeedback({
        type: 'success',
        message: 'Anomalia encontrada! 🔍',
        score: 20
      });
      
      setGameStats(prev => ({
        ...prev,
        score: prev.score + 20,
        correctSequences: prev.correctSequences + 1
      }));
      
      playFeedback('Anomalia encontrada corretamente!', 'success');
    } else {
      setFeedback({
        type: 'error',
        message: 'Este elemento não é uma anomalia.'
      });
      playFeedback('Este elemento não é uma anomalia.', 'error');
    }
  };

  // Funções para Classificação Visual
  const handleClassificationDrop = (e, groupType) => {
    e.preventDefault();
    const elementKey = e.dataTransfer.getData('text/plain');
    if (elementKey) {
      setClassificationResults(prev => ({
        ...prev,
        [elementKey]: groupType
      }));
      setSelectedForClassification(null);
    }
  };

  // Função para Detecção de Anomalias
  const handleAnomalyDetection = (index, shape) => {
    setDetectedAnomalies(prev => {
      if (prev.includes(index)) {
        return prev.filter(i => i !== index);
      } else {
        return [...prev, index];
      }
    });
  };

  // =====================================================
  // 🎮 RENDERIZAÇÃO PRINCIPAL
  // =====================================================

  // Se ainda não iniciou, mostra a tela de início
  if (showStartScreen) {
    return (
      <GameStartScreen
        gameTitle="Padrões Visuais"
        gameDescription="Desenvolva sua memória visual e sequencial"
        gameIcon="🎯"
        difficulties={[
          {
            id: 'easy',
            name: 'Fácil',
            description: 'Sequências de 3 formas\nIdeal para iniciantes',
            icon: '😊'
          },
          {
            id: 'medium',
            name: 'Médio',
            description: 'Sequências de 4 formas\nDesafio equilibrado',
            icon: '🎯'
          },
          {
            id: 'hard',
            name: 'Avançado',
            description: 'Sequências de 5 formas\nPara especialistas',
            icon: '🚀'
          }
        ]}
        onStart={(difficulty) => startGame(difficulty)}
        onBack={onBack}
      />
    );
  }

  // Layout PADRÃO LETTERRECOGNITION EXATO
  return (
    <div className={styles.padroesVisuaisGame}>
      <div className={styles.gameContent}>
        {/* Header do jogo - PADRÃO LETTERRECOGNITION */}
        <div className={styles.gameHeader}>
          <h1 className={styles.gameTitle}>
            🎯 Padrões Visuais V3
            <div style={{ fontSize: '0.7rem', opacity: 0.8, marginTop: '0.25rem' }}>
              {Object.values(ACTIVITY_TYPES).find(type => type.id === gameState.currentActivity)?.name || 'Atividade'}
            </div>
          </h1>
          <button
            className={`${styles.headerTtsButton} ${ttsActive ? styles.ttsActive : ''} ${isSpeaking ? styles.speaking : ''}`}
            onClick={toggleTTS}
            title={ttsActive ? (isSpeaking ? 'TTS Falando - Clique para desativar' : 'Desativar TTS') : 'Ativar TTS'}
            aria-label={ttsActive ? (isSpeaking ? 'TTS Falando - Clique para desativar' : 'Desativar TTS') : 'Ativar TTS'}
          >
            {isSpeaking ? '🎤' : (ttsActive ? '🔊' : '🔇')}
          </button>
        </div>

        {/* Header com estatísticas - PADRÃO LETTERRECOGNITION */}
        <div className={styles.gameStats}>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameStats.score}</div>
            <div className={styles.statLabel}>Pontos</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{currentLevel}</div>
            <div className={styles.statLabel}>Nível</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{getAccuracy()}%</div>
            <div className={styles.statLabel}>Precisão</div>
          </div>
        </div>

        {/* Menu de atividades - PADRÃO LETTERRECOGNITION */}
        <div className={styles.activityMenu}>
          {Object.values(ACTIVITY_TYPES).map((activity) => (
            <button
              key={activity.id}
              className={`${styles.activityButton} ${
                gameState.currentActivity === activity.id ? styles.active : ''
              }`}
              onClick={() => changeActivity(activity.id)}
              title={activity.description}
              aria-label={`Atividade: ${activity.name} - ${activity.description}`}
            >
              <span className={styles.activityIcon}>{activity.icon}</span>
              <span className={styles.activityName}>{activity.name}</span>
            </button>
          ))}
        </div>



        {/* Área do jogo - SEGUINDO PADRÃO MEMORY GAME */}
        {renderActivityInterface()}

        {/* Controles do jogo - PADRÃO LETTERRECOGNITION */}
        <div className={styles.gameControls}>
          <button className={styles.controlButton} onClick={explainGame}>
            🔊 Explicar
          </button>
          <button className={styles.controlButton} onClick={() => {
            if (gameSequence.length > 0) {
              showSequence();
            } else {
              generateNewLevel();
            }
          }}>
            🔄 {gameSequence.length > 0 ? 'Ver Sequência' : 'Nova Sequência'}
          </button>
          <button className={styles.controlButton} onClick={onBack}>
            ⬅️ Voltar
          </button>
        </div>

      </div>
    </div>
  );
}

export default PadroesVisuaisGame;
