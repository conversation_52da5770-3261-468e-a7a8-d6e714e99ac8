/**
 * @file MemoryGame.module.css
 * @description Estilos modulares para o Jogo da Memória - Layout padronizado com base em ContagemNumeros
 * @version 3.5.0
 */

/* Variáveis CSS para consistência e reutilização */
:root {
  --card-background: rgba(255, 255, 255, 0.1);
  --card-border: 1px solid rgba(255, 255, 255, 0.2);
  --card-blur: blur(10px);
  --success-bg: rgba(76, 175, 80, 0.3);
  --success-border: rgba(76, 175, 80, 0.5);
  --error-bg: rgba(244, 67, 54, 0.3);
  --error-border: rgba(244, 67, 54, 0.5);
  --primary-font: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Animações */
@keyframes buttonPressEffect {
  0% { transform: scale(1); }
  50% { transform: scale(1.15); box-shadow: 0 10px 30px rgba(255, 255, 255, 0.4); }
  100% { transform: scale(1); }
}

@keyframes correctFeedback {
  0% { box-shadow: 0 0 0 rgba(76, 175, 80, 0.4); }
  50% { box-shadow: 0 0 30px rgba(76, 175, 80, 0.8); }
  100% { box-shadow: 0 0 0 rgba(76, 175, 80, 0.4); }
}

@keyframes incorrectFeedback {
  0% { box-shadow: 0 0 0 rgba(244, 67, 54, 0.4); }
  25% { transform: translateX(-5px); }
  50% { transform: translateX(5px); box-shadow: 0 0 20px rgba(244, 67, 54, 0.8); }
  75% { transform: translateX(-5px); }
  100% { transform: translateX(0); box-shadow: 0 0 0 rgba(244, 67, 54, 0.4); }
}

/* Container principal */
.memoryGame {
  min-height: 100vh;
  background: var(--gradient-bg);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  color: white;
  font-family: var(--primary-font);
}

/* Conteúdo do jogo */
.gameContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Header do jogo */
.gameHeader {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem 3rem 1rem 1rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  position: relative;
  min-height: 70px;
}

.gameTitle {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: white;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.activitySubtitle {
  font-size: 0.7rem;
  opacity: 0.8;
  margin-top: 0.25rem;
  background: var(--card-background);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  border: var(--card-border);
}

/* Botão TTS no header */
.headerTtsButton {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  color: white;
  z-index: 10;
}

.headerTtsButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

.headerTtsButton:active {
  transform: scale(0.95);
}

.ttsActive {
  background: var(--success-bg) !important;
  border-color: var(--success-border) !important;
}

.ttsInactive {
  background: var(--error-bg) !important;
  border-color: var(--error-border) !important;
}

/* Menu de atividades - PADRÃO CONTAGEM NÚMEROS */
.activityMenu {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  justify-content: center;
  flex-wrap: wrap;
}

.activityButton {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.activityButton:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.activityButton.active {
  background: rgba(76, 175, 80, 0.3);
  border-color: rgba(76, 175, 80, 0.6);
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
}

.activitySelector {
  margin-bottom: 2rem;
}

.selectorTitle {
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-align: left;
}

.activityButtons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  justify-content: center;
}

/* Opções após completar atividade - NOVO SISTEMA */
.activityCompleteOptions {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: var(--card-blur);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.activityCompleteCard {
  background: var(--gradient-bg);
  border-radius: 20px;
  padding: 3rem;
  text-align: center;
  color: white;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.activityCompleteTitle {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #4CAF50;
}

.activityCompleteTitle::before {
  content: '🎉';
  font-size: 2.5rem;
  display: block;
  margin-bottom: 0.5rem;
}

.activityCompleteMessage {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.9);
}

.activityChoiceButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.choiceButton {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.choiceButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.choiceButton.continue {
  background: rgba(76, 175, 80, 0.2);
  border-color: rgba(76, 175, 80, 0.5);
}

.choiceButton.continue:hover {
  background: rgba(76, 175, 80, 0.3);
}

.choiceButton.newActivity {
  background: rgba(33, 150, 243, 0.2);
  border-color: rgba(33, 150, 243, 0.5);
}

.choiceButton.newActivity:hover {
  background: rgba(33, 150, 243, 0.3);
}

/* Elementos de atividade - SIMPLIFICADOS */
.activityIcon {
  font-size: 1rem;
  margin-right: 0.3rem;
}

.activityName {
  flex: 1;
  text-align: left;
  font-size: 0.8rem;
}

.activeIndicator {
  color: #00ff00;
  font-size: 0.7rem;
  margin-left: auto;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.7; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.05); }
}

/* Atividade de Som */
.soundActivity {
  text-align: center;
}

.soundIndicator {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: soundPulse 2s ease-in-out infinite;
}

@keyframes soundPulse {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.1); opacity: 1; }
}

.soundButton {
  background: rgba(74, 144, 226, 0.3);
  border: 2px solid rgba(74, 144, 226, 0.5);
  border-radius: 12px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  margin: 1rem;
}

.soundButton:hover {
  background: rgba(74, 144, 226, 0.5);
  transform: scale(1.05);
}

/* Atividade de Estimativa */
.estimationDisplay {
  position: relative;
}

.estimationObjects {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
  opacity: 0.9;
}

.estimationObject {
  font-size: 1.5rem;
  transform: rotate(var(--rotation));
  transition: opacity 0.3s ease;
}

.estimationTip {
  text-align: center;
  margin-top: 1rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  background: rgba(255, 193, 7, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border-left: 4px solid #FFC107;
}

/* Atividade de Sequência */
.sequenceDisplay {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  flex-wrap: wrap;
}

.sequenceNumber {
  background: var(--card-background);
  padding: 1rem;
  border-radius: 12px;
  min-width: 60px;
  text-align: center;
  color: white;
  font-weight: bold;
  transition: all 0.3s ease;
}

.sequenceNumber:hover {
  transform: scale(1.05);
}

.sequenceArrow {
  color: rgba(255, 255, 255, 0.7);
}

/* Estatísticas */
.gameStats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.statCard {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.statCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4CAF50, #2196F3, #FF9800, #E91E63);
  opacity: 0.7;
}

.statValue {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: white;
}

.statLabel {
  font-size: 0.8rem;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.8);
}

/* Área da pergunta - MELHORADA PARA COMBINAÇÃO DE PARES */
.questionArea {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.questionHeader {
  text-align: center;
  margin-bottom: 1.5rem;
  width: 100%;
}

.questionTitle {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: white;
}

.instructions {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 0.5rem;
}

/* Instruções específicas para Combinação de Pares - SEGUINDO PADRÃO CONTAGEM NÚMEROS */
.pairMatchingInstructions {
  background: rgba(33, 150, 243, 0.15);
  border: 2px solid rgba(33, 150, 243, 0.4);
  border-radius: 16px;
  padding: 1.5rem;
  color: white;
  font-size: 1.1rem;
  line-height: 1.6;
  text-align: center;
  box-shadow: 0 8px 32px rgba(33, 150, 243, 0.2);
  position: relative;
  overflow: hidden;
  margin-bottom: 2rem;
  max-width: 600px;
}

.pairMatchingInstructions::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: instructionShine 3s infinite;
}

.pairMatchingInstructions strong {
  color: #64b5f6;
  font-weight: bold;
}

@keyframes instructionShine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Indicador de progresso da atividade - SEGUINDO PADRÃO CONTAGEM NÚMEROS */
.pairMatchingProgress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin: 2rem 0;
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.15) 0%, rgba(139, 195, 74, 0.15) 100%);
  border: 2px solid rgba(76, 175, 80, 0.4);
  border-radius: 16px;
  color: white;
  box-shadow: 0 8px 32px rgba(76, 175, 80, 0.2);
}

.progressInfo {
  font-size: 1.2rem;
  font-weight: 600;
  text-align: center;
}

.progressBar {
  width: 100%;
  max-width: 300px;
  height: 12px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  overflow: hidden;
  margin: 0.5rem 0;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  border-radius: 10px;
  transition: width 0.5s ease-out;
  position: relative;
}

.progressFill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: progressGlow 2s ease-in-out infinite;
}

@keyframes progressGlow {
  0%, 100% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
}

.progressCounter {
  font-weight: bold;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 20px;
  padding: 0.3rem 1rem;
  font-size: 1rem;
}

.repeatButton {
  background: rgba(74, 144, 226, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.repeatButton:hover {
  background: rgba(74, 144, 226, 1);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.4);
}

.repeatButton:active {
  transform: scale(0.95);
}

.ttsIndicator {
  background: rgba(74, 144, 226, 0.8);
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
  margin-top: 0.5rem;
  transition: all 0.3s ease;
  border: 2px solid rgba(74, 144, 226, 1);
}

.answerButton:hover .ttsIndicator {
  background: rgba(74, 144, 226, 1);
  transform: scale(1.1);
}

/* ==================== ATIVIDADE: 🧩 COMBINAÇÃO DE PARES ==================== */

/* Área da atividade de Combinação de Pares - SEGUINDO PADRÃO CONTAGEM NÚMEROS */
.pairMatchingActivity {
  flex: 1;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 500px;
  color: white;
}

.pairMatchingActivity h3 {
  color: white;
  font-size: 2rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
  line-height: 1.3;
  text-align: center;
}

.pairMatchingTip {
  color: rgba(255, 255, 255, 0.95);
  font-size: 1.4rem;
  margin-bottom: 2rem;
  line-height: 1.5;
  font-weight: 500;
  max-width: 800px;
  text-align: center;
  background: rgba(255, 193, 7, 0.1);
  padding: 1rem 2rem;
  border-radius: 12px;
  border-left: 4px solid #FFC107;
}

/* Grid de memória - SISTEMA RESPONSIVO MELHORADO */
.memoryGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  justify-content: center;
  margin: 2rem auto;
  max-width: 800px;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  backdrop-filter: var(--card-blur);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Variações do grid baseadas no número de cartas - OTIMIZADAS */
.memoryGrid.grid2x2 {
  grid-template-columns: repeat(2, 1fr);
  max-width: 400px;
  gap: 2rem;
}

.memoryGrid.grid2x2 .memoryCard,
.memoryGrid.grid2x2 .card {
  min-height: 160px;
  min-width: 160px;
}

.memoryGrid.grid3x3 {
  grid-template-columns: repeat(3, 1fr);
  max-width: 550px;
  gap: 1.8rem;
}

.memoryGrid.grid3x3 .memoryCard,
.memoryGrid.grid3x3 .card {
  min-height: 140px;
  min-width: 140px;
}

.memoryGrid.grid4x4 {
  grid-template-columns: repeat(4, 1fr);
  max-width: 700px;
  gap: 1.5rem;
}

.memoryGrid.grid4x4 .memoryCard,
.memoryGrid.grid4x4 .card {
  min-height: 130px;
  min-width: 130px;
}

.memoryGrid.grid5x5 {
  grid-template-columns: repeat(5, 1fr);
  max-width: 850px;
  gap: 1.2rem;
}

.memoryGrid.grid5x5 .memoryCard,
.memoryGrid.grid5x5 .card {
  min-height: 110px;
  min-width: 110px;
}

.memoryCard, 
.card {
  aspect-ratio: 1;
  border-radius: 12px;
  cursor: pointer;
  position: relative;
  perspective: 1000px;
  transition: transform 0.3s ease;
  min-height: 120px;
  min-width: 120px;
  user-select: none;
}

.memoryCard:hover:not(.matched):not(.cardDisabled),
.card:hover:not(.matched):not(.cardDisabled) {
  transform: scale(1.05);
}

.memoryCard.cardDisabled,
.card.cardDisabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Container interno da carta - SIMPLES */
.cardInner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.6s;
  transform-style: preserve-3d;
}

/* Quando a carta está virada */
.memoryCard.flipped .cardInner,
.card.flipped .cardInner {
  transform: rotateY(180deg);
}

/* Quando carta está matched */
.memoryCard.matched .cardInner,
.card.matched .cardInner {
  transform: rotateY(180deg);
}

/* Efeito de pressão no click */
.memoryCard:active .cardInner,
.card:active .cardInner {
  transform: scale(0.95);
  transition: transform 0.1s ease;
}

/* Efeito de foco para acessibilidade */
.memoryCard:focus,
.card:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.5);
  border-radius: 16px;
}

/* Face traseira da carta (verso) - EMOJI VISÍVEL - CORRIGIDA */
.cardBack {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -ms-backface-visibility: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: 3px solid rgba(255, 255, 255, 0.5);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 4rem;
  color: white;
  backdrop-filter: blur(10px);
  box-shadow: 
    0 8px 25px rgba(0, 0, 0, 0.3),
    inset 0 2px 10px rgba(255, 255, 255, 0.2);
  transform: rotateY(180deg);
  -webkit-transform: rotateY(180deg);
  -moz-transform: rotateY(180deg);
  -ms-transform: rotateY(180deg);
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  z-index: 2;
}

/* Efeito brilho no conteúdo da carta - SIMPLIFICADO */
.cardBack::after {
  content: '';
  position: absolute;
  top: 10px;
  left: 10px;
  right: 10px;
  height: 40%;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.15),
    transparent
  );
  border-radius: 12px 12px 0 0;
  pointer-events: none;
}

/* Face frontal da carta (interrogação) - VISÍVEL INICIALMENTE - CORRIGIDA */
.cardFront {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -ms-backface-visibility: hidden;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(240, 248, 255, 0.9));
  border: 3px solid rgba(255, 255, 255, 0.6);
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transform: rotateY(0deg);
  -webkit-transform: rotateY(0deg);
  -moz-transform: rotateY(0deg);
  -ms-transform: rotateY(0deg);
  color: #2c3e50;
  box-shadow: 
    0 8px 20px rgba(0, 0, 0, 0.15),
    inset 0 2px 10px rgba(255, 255, 255, 0.3);
  font-size: 3.5rem;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  z-index: 1;
}

/* Efeito de interrogação - REMOVIDO SHIMMER PARA CORRIGIR FLIP */

/* Ícone da carta - MELHORADO */
.cardIcon {
  font-size: 3rem;
  margin-bottom: 0.5rem;
  display: block;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* Nome da carta - MELHORADO */
.cardName {
  font-size: 0.9rem;
  font-weight: 700;
  text-align: center;
  line-height: 1.2;
  color: #2c3e50;
  text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
  letter-spacing: 0.5px;
}

/* Ícone do verso da carta */
.cardBackIcon {
  font-size: 2.5rem;
  font-weight: bold;
}

/* Cartas combinadas - FEEDBACK MELHORADO */
.memoryCard.matched .cardFront,
.card.matched .cardFront {
  background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
  border-color: rgba(76, 175, 80, 0.8);
  color: white;
  animation: matchPulse 0.8s ease-in-out;
  box-shadow: 
    0 12px 30px rgba(76, 175, 80, 0.4),
    inset 0 2px 10px rgba(255, 255, 255, 0.3);
}

.memoryCard.matched .cardBack,
.card.matched .cardBack {
  background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
  border-color: rgba(76, 175, 80, 0.8);
  box-shadow: 
    0 12px 30px rgba(76, 175, 80, 0.4),
    inset 0 2px 10px rgba(255, 255, 255, 0.3);
}

/* Cartas combinadas - FEEDBACK MELHORADO SEGUINDO PADRÃO CONTAGEM NÚMEROS */
.memoryCard.matched .cardFront,
.card.matched .cardFront {
  background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
  border-color: rgba(76, 175, 80, 0.8);
  color: white;
  animation: matchSuccess 1.2s ease-in-out;
  box-shadow: 
    0 12px 30px rgba(76, 175, 80, 0.4),
    inset 0 2px 10px rgba(255, 255, 255, 0.3);
}

.memoryCard.matched .cardBack,
.card.matched .cardBack {
  background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
  border-color: rgba(76, 175, 80, 0.8);
  box-shadow: 
    0 12px 30px rgba(76, 175, 80, 0.4),
    inset 0 2px 10px rgba(255, 255, 255, 0.3);
  animation: matchCelebration 1.2s ease-in-out;
}

/* Animações de sucesso para pares encontrados */
@keyframes matchSuccess {
  0% { 
    transform: scale(1) rotateY(180deg); 
    box-shadow: 0 12px 30px rgba(76, 175, 80, 0.4);
  }
  25% {
    transform: scale(1.15) rotateY(180deg);
    box-shadow: 0 16px 40px rgba(76, 175, 80, 0.8);
  }
  50% {
    transform: scale(1.25) rotateY(180deg);
    box-shadow: 0 20px 50px rgba(76, 175, 80, 1);
  }
  75% {
    transform: scale(1.1) rotateY(180deg);
    box-shadow: 0 16px 40px rgba(76, 175, 80, 0.8);
  }
  100% { 
    transform: scale(1) rotateY(180deg); 
    box-shadow: 0 12px 30px rgba(76, 175, 80, 0.4);
  }
}

@keyframes matchCelebration {
  0% { 
    transform: rotateY(180deg) scale(1);
  }
  25% {
    transform: rotateY(180deg) scale(1.1) rotate(5deg);
  }
  50% {
    transform: rotateY(180deg) scale(1.2) rotate(-5deg);
  }
  75% {
    transform: rotateY(180deg) scale(1.1) rotate(3deg);
  }
  100% {
    transform: rotateY(180deg) scale(1) rotate(0deg);
  }
}

/* Mensagem de par encontrado - SEGUINDO PADRÃO CONTAGEM NÚMEROS */
.pairFoundMessage {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
  color: white;
  padding: 2rem 3rem;
  border-radius: 20px;
  font-size: 1.5rem;
  font-weight: 700;
  box-shadow: 0 20px 40px rgba(76, 175, 80, 0.4);
  z-index: 1000;
  text-align: center;
  animation: pairFoundSlide 2.5s ease-in-out;
  border: 3px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.pairFoundMessage::before {
  content: '🎉';
  font-size: 2rem;
  display: block;
  margin-bottom: 0.5rem;
  animation: celebrationBounce 0.6s ease-in-out infinite alternate;
}

@keyframes pairFoundSlide {
  0% { 
    opacity: 0; 
    transform: translate(-50%, -50%) scale(0.5) rotateY(-90deg); 
  }
  15% { 
    opacity: 1; 
    transform: translate(-50%, -50%) scale(1.1) rotateY(10deg); 
  }
  25% { 
    transform: translate(-50%, -50%) scale(1) rotateY(0deg); 
  }
  75% { 
    opacity: 1; 
    transform: translate(-50%, -50%) scale(1) rotateY(0deg); 
  }
  100% { 
    opacity: 0; 
    transform: translate(-50%, -50%) scale(0.8) rotateY(90deg); 
  }
}

@keyframes celebrationBounce {
  0% { 
    transform: scale(1) rotate(-5deg); 
  }
  100% { 
    transform: scale(1.2) rotate(5deg); 
  }
}

/* Feedback para tentativa incorreta - SEGUINDO PADRÃO CONTAGEM NÚMEROS */
.incorrectPairMessage {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: linear-gradient(135deg, #f44336 0%, #e57373 100%);
  color: white;
  padding: 1.5rem 2.5rem;
  border-radius: 16px;
  font-size: 1.2rem;
  font-weight: 600;
  box-shadow: 0 15px 30px rgba(244, 67, 54, 0.4);
  z-index: 1000;
  text-align: center;
  animation: incorrectShake 1.5s ease-in-out;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.incorrectPairMessage::before {
  content: '❌';
  font-size: 1.5rem;
  display: block;
  margin-bottom: 0.5rem;
}

@keyframes incorrectShake {
  0%, 100% { 
    transform: translate(-50%, -50%); 
    opacity: 0;
  }
  10% { 
    transform: translate(-48%, -50%); 
    opacity: 1;
  }
  20% { 
    transform: translate(-52%, -50%); 
  }
  30% { 
    transform: translate(-48%, -50%); 
  }
  40% { 
    transform: translate(-52%, -50%); 
  }
  50% { 
    transform: translate(-50%, -50%); 
  }
  90% { 
    opacity: 1;
  }
}

/* Controles do jogo - SEGUINDO PADRÃO CONTAGEM NÚMEROS */
.gameControls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.controlButton {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.controlButton:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

/* Padrão seguido - sem botões específicos */

/* Tela de finalização */
.completionScreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: var(--card-blur);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.completionCard {
  background: var(--gradient-bg);
  border-radius: 20px;
  padding: 3rem;
  text-align: center;
  color: white;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.completionTitle {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
}

.completionStats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin: 2rem 0;
}

.completionStat {
  background: var(--card-background);
  padding: 1rem;
  border-radius: 12px;
}

.completionActions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  flex-wrap: wrap;
}

/* Mensagem de encorajamento */
.encouragementMessage {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
  padding: 1.5rem 2rem;
  border-radius: 12px;
  font-size: 1.2rem;
  font-weight: 600;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  text-align: center;
  animation: messageSlide 3s ease-in-out;
}

@keyframes messageSlide {
  0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
  15% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  85% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
}

/* Análise cognitiva */
.cognitiveAnalysis {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  padding: 2rem;
  border-radius: 16px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  backdrop-filter: var(--card-blur);
}

.cognitiveAnalysis h3 {
  margin-top: 0;
  color: #667eea;
  text-align: center;
}

.cognitiveAnalysis div {
  margin: 0.5rem 0;
  line-height: 1.4;
}

/* Sistema de atividades */

/* Grid Container para o jogo da memória - SIMPLES */
.gridContainer {
  display: grid;
  gap: 1rem;
  margin: 1rem auto;
  max-width: 600px;
  justify-content: center;
  padding: 1rem;
}

/* Atividades específicas */
.sequenceContainer,
.spatialContainer,
.soundContainer,
.imageContainer,
.numberContainer {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  color: white;
  min-height: 350px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 1.1rem;
  margin: 1rem 0;
}

/* Display da sequência */
.roundInfo {
  display: flex;
  justify-content: center;
  margin-top: 0.5rem;
}

.roundBadge {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 0.5rem 1.2rem;
  font-size: 1rem;
  font-weight: bold;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.sequenceDisplay {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  justify-content: center;
  align-items: center;
  margin: 2rem 0;
  min-height: 140px;
}

.sequenceRow {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.sequenceItem {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  border: 6px solid rgba(255, 255, 255, 0.8);
  margin: 0 0.8rem;
  transition: all 0.3s ease;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.sequenceItem::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  box-shadow: inset 0 -8px 20px rgba(0, 0, 0, 0.2);
  pointer-events: none;
}

@keyframes buttonPressEffect {
  0% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.2); opacity: 1; }
  100% { transform: scale(1); opacity: 0.8; }
}

.pressEffect {
  animation: buttonPressEffect 0.3s ease-in-out;
}

.sequenceTitle {
  width: 100%;
  text-align: center;
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  color: rgba(255, 255, 255, 0.9);
}

/* Botões de cores para sequência */
.colorButtons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  align-items: center;
  margin: 2rem 0;
  flex-wrap: wrap;
}

.feedbackContainer {
  margin: 1.5rem 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.progressBar {
  width: 80%;
  height: 12px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  overflow: hidden;
  margin: 0.5rem 0;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  border-radius: 10px;
  transition: width 0.3s ease-out;
}

.progressCounter {
  font-weight: bold;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 20px;
  padding: 0.2rem 0.8rem;
  margin-left: 0.5rem;
}

.sequenceHistory {
  display: flex;
  gap: 8px;
  margin-top: 0.5rem;
}

.historyDot {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.5);
}

.colorButton {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  border: 4px solid rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  margin: 0.5rem;
  position: relative;
  overflow: hidden;
}

.colorButton::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  box-shadow: inset 0 -10px 20px rgba(0, 0, 0, 0.2);
  pointer-events: none;
}

.colorButton:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
  border-color: rgba(255, 255, 255, 0.8);
}

/* Estados ativos e feedback visual */
.activeButton {
  transform: scale(1.15);
  box-shadow: 0 0 30px rgba(255, 255, 255, 0.8);
  border-color: white;
}

/* Classes para feedback visual animado */
.buttonPress {
  animation: buttonPressEffect 0.5s ease;
}

.correctFeedback {
  animation: correctFeedback 0.8s ease;
}

.incorrectFeedback {
  animation: incorrectFeedback 0.5s ease;
}

/* Cores específicas */
.red {
  background-color: #f44336;
  box-shadow: 0 6px 20px rgba(244, 67, 54, 0.6);
}

.blue {
  background-color: #2196f3;
  box-shadow: 0 6px 20px rgba(33, 150, 243, 0.6);
}

.green {
  background-color: #4caf50;
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.6);
}

.yellow {
  background-color: #ffeb3b;
  box-shadow: 0 6px 20px rgba(255, 235, 59, 0.6);
}

.purple {
  background-color: #9c27b0;
  box-shadow: 0 6px 20px rgba(156, 39, 176, 0.6);
}

.orange {
  background-color: #ff9800;
  box-shadow: 0 6px 20px rgba(255, 152, 0, 0.6);
}

.blue {
  background-color: #2196f3;
}

.green {
  background-color: #4caf50;
}

.yellow {
  background-color: #ffeb3b;
}

/* Feedback da sequência */
.sequenceFeedback {
  margin-top: 2rem;
  color: white;
}

.sequenceProgress {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  margin-top: 1rem;
}

.sequenceStep {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.sequenceStep.completed {
  background: #4caf50;
  border-color: #4caf50;
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
}

/* Grid espacial */
.spatialGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.8rem;
  max-width: 450px;
  margin: 2rem auto;
  padding: 1.5rem;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 16px;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.spatialCell {
  width: 90px;
  height: 90px;
  background: rgba(255, 255, 255, 0.15);
  border: 3px solid rgba(255, 255, 255, 0.4);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.spatialCell:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.6);
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.spatialCell.highlighted {
  background: #ffeb3b !important;
  border-color: #ffc107 !important;
  box-shadow: 0 0 25px rgba(255, 235, 59, 0.8) !important;
  animation: spatialPulse 1.5s ease-in-out infinite;
  color: #333 !important;
}

.spatialCell.selected {
  background: rgba(33, 150, 243, 0.7);
  border-color: #2196f3;
  box-shadow: 0 0 20px rgba(33, 150, 243, 0.6);
}

.spatialCell.correct {
  background: rgba(76, 175, 80, 0.9) !important;
  border-color: #4caf50 !important;
  box-shadow: 0 0 25px rgba(76, 175, 80, 0.8) !important;
}

.spatialCell.incorrect {
  background: rgba(244, 67, 54, 0.9) !important;
  border-color: #f44336 !important;
  box-shadow: 0 0 25px rgba(244, 67, 54, 0.8) !important;
}

@keyframes spatialPulse {
  0%, 100% { 
    opacity: 0.9; 
    transform: scale(1);
    box-shadow: 0 0 25px rgba(255, 235, 59, 0.8);
  }
  50% { 
    opacity: 1; 
    transform: scale(1.15);
    box-shadow: 0 0 35px rgba(255, 235, 59, 1);
  }
}

/* Feedback espacial */
.feedback {
  margin-top: 2rem;
  text-align: center;
  color: white;
  font-size: 1.1rem;
}

/* Sequência Numérica - Mobile First */
.numberSequenceContainer {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
  margin: 1rem 0;
  text-align: center;
}

.numberRow {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  margin: 1.5rem 0;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
}

.numberCard {
  min-width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.4rem;
  font-weight: bold;
  color: #333;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.numberCard.missingNumber {
  background: linear-gradient(135deg, #ff5722 0%, #ff9800 100%);
  border: 3px solid #fff;
  color: white;
  font-size: 2rem;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  animation: numberPulse 2s ease-in-out infinite;
  position: relative;
  overflow: hidden;
}

.numberCard.missingNumber::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: shimmer 3s infinite;
}

@keyframes numberPulse {
  0%, 100% { 
    transform: scale(1);
    box-shadow: 0 4px 20px rgba(255, 87, 34, 0.4);
  }
  50% { 
    transform: scale(1.1);
    box-shadow: 0 8px 30px rgba(255, 87, 34, 0.6);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

.patternInfo {
  margin: 2rem 0;
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.15) 0%, rgba(63, 81, 181, 0.15) 100%);
  border: 2px solid rgba(33, 150, 243, 0.4);
  border-radius: 16px;
  color: white;
  font-size: 1rem;
  line-height: 1.6;
  text-align: center;
  box-shadow: 0 8px 32px rgba(33, 150, 243, 0.2);
  position: relative;
  overflow: hidden;
}

.patternInfo::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: patternShine 3s infinite;
}

.patternInfo strong {
  color: #64b5f6;
  font-weight: bold;
}

@keyframes patternShine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.answerContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
  margin-top: 2rem;
}

.numberInput {
  width: 100%;
  max-width: 200px;
  height: 60px;
  font-size: 1.5rem;
  font-weight: bold;
  text-align: center;
  border: 3px solid rgba(33, 150, 243, 0.5);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  outline: none;
  transition: all 0.3s ease;
  -webkit-appearance: none;
  -moz-appearance: textfield;
  appearance: none;
  touch-action: manipulation;
}

.numberInput::-webkit-outer-spin-button,
.numberInput::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.numberInput::placeholder {
  color: #999;
  font-weight: normal;
  opacity: 0.7;
}

.numberInput:focus {
  border-color: #2196f3;
  background: #fff;
  box-shadow: 0 0 20px rgba(33, 150, 243, 0.4);
  transform: scale(1.05);
}

.submitButton {
  width: 100%;
  max-width: 200px;
  height: 60px;
  background: linear-gradient(135deg, #4caf50, #45a049);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
  touch-action: manipulation;
  position: relative;
  overflow: hidden;
}

.submitButton::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.submitButton:hover:not(:disabled)::before {
  width: 300px;
  height: 300px;
}

.submitButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #45a049, #3d8b40);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(76, 175, 80, 0.5);
}

.submitButton:disabled {
  background: rgba(158, 158, 158, 0.5);
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* Responsividade para tablets */
@media (min-width: 768px) {
  .numberRow {
    gap: 1rem;
    padding: 1.5rem;
  }
  
  .numberCard {
    min-width: 80px;
    height: 80px;
    font-size: 1.6rem;
  }
  
  .numberCard.missingNumber {
    font-size: 2rem;
  }
  
  .answerContainer {
    flex-direction: row;
    justify-content: center;
  }
  
  .numberInput {
    max-width: 150px;
  }
  
  .submitButton {
    max-width: 150px;
  }
}

/* Responsividade para desktop */
@media (min-width: 1024px) {
  .numberSequenceContainer {
    padding: 2rem;
  }
  
  .numberRow {
    gap: 1.5rem;
    padding: 2rem;
  }
  
  .numberCard {
    min-width: 100px;
    height: 100px;
    font-size: 1.8rem;
  }
  
  .numberCard.missingNumber {
    font-size: 2.2rem;
  }
}

/* Reconstrução de Imagem - Mobile First */
.imageContainer {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1rem;
  text-align: center;
  color: white;
  margin: 1rem 0;
}

.imagePreview {
  padding: 1rem;
  margin-bottom: 2rem;
}

.emojiPreviewGrid {
  display: grid;
  gap: 0.5rem;
  max-width: 280px;
  margin: 1rem auto;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.previewItem {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.previewEmoji {
  font-size: 2rem;
}

/* Grid de reconstrução - Mobile First */
.reconstructionGrid {
  display: grid;
  gap: 0.5rem;
  max-width: 300px;
  margin: 1rem auto 2rem;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.dropZone {
  width: 65px;
  height: 65px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px dashed rgba(255, 255, 255, 0.4);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  margin: 0 auto;
}

.dropZone:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.6);
}

.dropZone:empty::after {
  content: '+';
  color: rgba(255, 255, 255, 0.5);
  font-size: 1.5rem;
  font-weight: bold;
}

.puzzlePiece {
  width: 100%;
  height: 100%;
  background: rgba(76, 175, 80, 0.2);
  border: 2px solid rgba(76, 175, 80, 0.6);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.puzzlePiece:hover {
  background: rgba(76, 175, 80, 0.3);
  transform: scale(1.05);
}

/* Peças disponíveis - Mobile First */
.availablePieces {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
  justify-content: center;
  margin-top: 2rem;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  max-width: 350px;
  margin-left: auto;
  margin-right: auto;
}

.pieceContainer {
  flex: 0 0 auto;
}

.availablePiece {
  width: 65px;
  height: 65px;
  background: rgba(33, 150, 243, 0.2);
  border: 2px solid rgba(33, 150, 243, 0.5);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.availablePiece:hover {
  background: rgba(33, 150, 243, 0.4);
  border-color: rgba(33, 150, 243, 0.7);
  transform: scale(1.05);
}

.availablePiece:active {
  transform: scale(0.95);
}

.emojiPiece {
  font-size: 1.8rem;
  user-select: none;
}

/* Responsividade para tablets */
@media (min-width: 768px) {
  .imageContainer {
    padding: 2rem;
  }
  
  .emojiPreviewGrid {
    max-width: 350px;
    gap: 0.8rem;
  }
  
  .previewItem {
    width: 75px;
    height: 75px;
  }
  
  .previewEmoji {
    font-size: 2.5rem;
  }
  
  .reconstructionGrid {
    max-width: 400px;
    gap: 0.8rem;
  }
  
  .dropZone {
    width: 85px;
    height: 85px;
  }
  
  .availablePieces {
    max-width: 450px;
    gap: 1rem;
  }
  
  .availablePiece {
    width: 85px;
    height: 85px;
  }
  
  .emojiPiece {
    font-size: 2.2rem;
  }
}

/* Responsividade para desktop */
@media (min-width: 1024px) {
  .emojiPreviewGrid {
    max-width: 450px;
    gap: 1rem;
  }
  
  .previewItem {
    width: 90px;
    height: 90px;
  }
  
  .previewEmoji {
    font-size: 3rem;
  }
  
  .reconstructionGrid {
    max-width: 500px;
    gap: 1rem;
  }
  
  .dropZone {
    width: 100px;
    height: 100px;
  }
  
  .availablePieces {
    max-width: 550px;
    gap: 1.2rem;
  }
  
  .availablePiece {
    width: 100px;
    height: 100px;
  }
  
  .emojiPiece {
    font-size: 2.5rem;
  }
}

/* Tela de conclusão */
.activitiesCompleted {
  margin: 1.5rem 0;
  text-align: left;
}

.activitiesCompleted h4 {
  color: #4CAF50;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  text-align: center;
}

.activityList {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
  margin-top: 1rem;
}

.activityItem {
  background: var(--success-bg);
  border: 1px solid var(--success-border);
  border-radius: 8px;
  padding: 0.75rem;
  text-align: center;
  font-size: 0.9rem;
  color: white;
}

/* Responsividade */
@media (max-width: 768px) {
  .memoryGame {
    padding: 0.5rem;
  }
  
  .gameTitle {
    font-size: 1.5rem;
  }
  
  .memoryGrid {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
    margin: 1rem auto;
    max-width: 400px;
    padding: 0.5rem;
  }
  
  .memoryCard {
    font-size: 1.5rem;
    min-height: 60px;
  }
  
  .gameStats {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }
  
  .questionArea {
    padding: 1rem;
  }
  
  .completionCard {
    padding: 2rem;
    margin: 1rem;
  }
  
  .completionTitle {
    font-size: 2rem;
  }
  
  .activityHeader {
    padding: 1rem;
    margin-bottom: 1rem;
  }
  
  .currentActivityName {
    font-size: 1rem;
  }
  
  .activityList {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .activityItem {
    font-size: 0.8rem;
    padding: 0.5rem;
  }
  
  .activityMenu {
    gap: 0.25rem;
  }
  
  .activityButton {
    padding: 0.4rem 0.8rem;
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .gameStats {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }

  .statCard {
    padding: 0.75rem;
  }

  .memoryGrid {
    gap: 0.3rem;
    margin: 1rem auto;
    padding: 0.3rem;
  }

  .memoryGrid.grid2x2 {
    max-width: 250px;
  }

  .memoryGrid.grid3x3 {
    max-width: 300px;
  }

  .memoryGrid.grid4x4 {
    grid-template-columns: repeat(3, 1fr);
    max-width: 300px;
  }

  .memoryGrid.grid5x5 {
    grid-template-columns: repeat(3, 1fr);
    max-width: 300px;
  }

  .memoryCard {
    font-size: 1.2rem;
    min-height: 50px;
    border-radius: 12px;
  }
  
  .controlButtons {
    flex-direction: column;
    align-items: center;
  }
  
  .completionActions {
    flex-direction: column;
  }
  
  .progressBar {
    max-width: 250px;
  }
  
  .sequenceContainer,
  .spatialContainer,
  .soundContainer,
  .imageContainer,
  .numberContainer {
    padding: 1.5rem;
    font-size: 1rem;
  }
  
  .activityButton {
    padding: 0.4rem 0.8rem;
    font-size: 0.7rem;
  }
}

/* Media query adicional para celulares em landscape e telas pequenas */
@media (max-width: 640px) and (orientation: landscape) {
  .memoryGrid {
    gap: 0.3rem;
    margin: 0.5rem auto;
    padding: 0.3rem;
  }

  .memoryGrid.grid2x2 {
    max-width: 200px;
  }

  .memoryGrid.grid3x3 {
    max-width: 250px;
  }

  .memoryGrid.grid4x4 {
    grid-template-columns: repeat(4, 1fr);
    max-width: 320px;
  }

  .memoryGrid.grid5x5 {
    grid-template-columns: repeat(5, 1fr);
    max-width: 400px;
  }

  .memoryCard {
    font-size: 1rem;
    min-height: 40px;
    border-radius: 8px;
  }

  .gameHeader {
    padding: 0.5rem 2rem 0.5rem 0.5rem;
    min-height: 50px;
  }

  .gameTitle {
    font-size: 1.3rem;
  }

  .activitySubtitle {
    font-size: 0.6rem;
  }
}

/* Suporte a alto contraste */
[data-theme="high-contrast"] {
  --card-background: #000;
  --card-border: 1px solid #fff;
  --success-bg: #28a745;
  --success-border: #1e7e34;
  --error-bg: #dc3545;
  --error-border: #bd2130;
}

/* Suporte a movimento reduzido */
.reduced-motion {
  .memoryCard, .controlButton, .encouragementMessage, .activityButton {
    animation: none !important;
    transition: none !important;
  }
}

/* Classes adicionais para atividades */
.questionHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.questionTitle {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.instructions {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 1rem;
  line-height: 1.4;
}

.questionArea {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  margin: 1rem 0;
  color: white;
}

/* ========================================
   RESPONSIVIDADE PARA DISPOSITIVOS MÓVEIS
   ======================================== */

@media (max-width: 768px) {
  .gridContainer {
    gap: 1rem;
    padding: 1.5rem;
    margin: 1rem auto;
  }

  .memoryGrid {
    gap: 1rem;
    padding: 1.5rem;
    margin: 1rem auto;
  }

  .memoryCard, 
  .card {
    min-height: 90px;
    min-width: 90px;
  }

  .memoryGrid.grid2x2 .memoryCard,
  .memoryGrid.grid2x2 .card {
    min-height: 120px;
    min-width: 120px;
  }

  .memoryGrid.grid3x3 .memoryCard,
  .memoryGrid.grid3x3 .card {
    min-height: 100px;
    min-width: 100px;
  }

  .memoryGrid.grid4x4 .memoryCard,
  .memoryGrid.grid4x4 .card {
    min-height: 85px;
    min-width: 85px;
  }

  .memoryGrid.grid5x5 .memoryCard,
  .memoryGrid.grid5x5 .card {
    min-height: 75px;
    min-width: 75px;
  }

  .cardFront, .cardBack {
    font-size: 2.5rem;
  }

  .memoryGrid.grid5x5 .cardFront,
  .memoryGrid.grid5x5 .cardBack {
    font-size: 1.8rem;
  }
}

@media (max-width: 480px) {
  .gridContainer {
    gap: 0.8rem;
    padding: 1rem;
    margin: 0.5rem auto;
  }

  .memoryGrid {
    gap: 0.8rem;
    padding: 1rem;
    margin: 0.5rem auto;
  }

  .memoryCard, 
  .card {
    min-height: 65px;
    min-width: 65px;
  }

  .memoryGrid.grid4x4 .memoryCard,
  .memoryGrid.grid4x4 .card {
    min-height: 60px;
    min-width: 60px;
  }

  .memoryGrid.grid5x5 .memoryCard,
  .memoryGrid.grid5x5 .card {
    min-height: 50px;
    min-width: 50px;
  }

  .cardFront, .cardBack {
    font-size: 1.8rem;
  }

  .memoryGrid.grid5x5 .cardFront,
  .memoryGrid.grid5x5 .cardBack {
    font-size: 1.2rem;
  }
}

/* ==================== DEBUG FIXES PARA FLIP DAS CARTAS ==================== */

/* Força a visibilidade correta do emoji quando carta está virada */
.card.flipped .cardBack,
.memoryCard.flipped .cardBack {
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 10 !important;
  display: flex !important;
}

/* Garante que a frente fique oculta quando virada */
.card.flipped .cardFront,
.memoryCard.flipped .cardFront {
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 1 !important;
}

/* Força rotação para cartas matched */
.card.matched .cardInner,
.memoryCard.matched .cardInner {
  transform: rotateY(180deg) !important;
}

/* DEBUG: Força emoji sempre visível no verso */
.cardBack {
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", "Segoe UI Symbol", sans-serif !important;
  line-height: 1 !important;
}