#!/usr/bin/env node

/**
 * @file test-real-game-metrics.js
 * @description Teste prático com simulação de jogo real
 * @version 1.0.0
 */

console.log('🎮 TESTE PRÁTICO: Jogo Real com Sistema de Métricas');
console.log('=' .repeat(70));

// Simular localStorage para Node.js
global.localStorage = {
  data: {},
  getItem(key) {
    return this.data[key] || null;
  },
  setItem(key, value) {
    this.data[key] = value;
  },
  removeItem(key) {
    delete this.data[key];
  },
  clear() {
    this.data = {};
  }
};

// Simular console com cores
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️ ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️ ${msg}${colors.reset}`),
  game: (msg) => console.log(`${colors.magenta}🎮 ${msg}${colors.reset}`)
};

// Simular jogo ColorMatch
class ColorMatchGameSimulator {
  constructor(dashboardAuth) {
    this.dashboardAuth = dashboardAuth;
    this.sessionId = `colormatch_${Date.now()}`;
    this.metrics = [];
    this.gameState = {
      level: 1,
      score: 0,
      correctAnswers: 0,
      wrongAnswers: 0,
      startTime: new Date()
    };
  }

  async startGame() {
    log.game('Iniciando jogo ColorMatch...');
    
    // Verificar se deve coletar métricas
    const shouldCollect = this.dashboardAuth.shouldSaveMetrics();
    const loginInfo = this.dashboardAuth.getLoginInfo();
    
    console.log(`🔍 Status de login:`, {
      shouldCollect,
      loginType: loginInfo.loginType,
      isLoggedIn: loginInfo.isLoggedIn
    });

    if (shouldCollect) {
      this.addMetric('GAME_START', {
        gameType: 'ColorMatch',
        difficulty: 'medium',
        timestamp: new Date().toISOString()
      });
      log.success('Métricas de início coletadas');
    } else {
      log.warning('Métricas não coletadas - sem login no dashboard');
    }

    return shouldCollect;
  }

  async playRound(isCorrect) {
    if (isCorrect) {
      this.gameState.correctAnswers++;
      this.gameState.score += 10;
      log.game(`Resposta correta! Score: ${this.gameState.score}`);
    } else {
      this.gameState.wrongAnswers++;
      log.game(`Resposta incorreta. Score: ${this.gameState.score}`);
    }

    // Tentar coletar métrica da jogada
    const shouldCollect = this.dashboardAuth.shouldSaveMetrics();
    if (shouldCollect) {
      this.addMetric('USER_ACTION', {
        action: 'color_selection',
        isCorrect,
        responseTime: Math.random() * 2000 + 500, // 500-2500ms
        level: this.gameState.level,
        score: this.gameState.score
      });
      log.info('Métrica da jogada coletada');
    }

    return shouldCollect;
  }

  async endGame() {
    const endTime = new Date();
    const duration = endTime.getTime() - this.gameState.startTime.getTime();
    
    log.game('Finalizando jogo...');
    
    const shouldCollect = this.dashboardAuth.shouldSaveMetrics();
    if (shouldCollect) {
      this.addMetric('GAME_END', {
        finalScore: this.gameState.score,
        correctAnswers: this.gameState.correctAnswers,
        wrongAnswers: this.gameState.wrongAnswers,
        duration,
        accuracy: (this.gameState.correctAnswers / (this.gameState.correctAnswers + this.gameState.wrongAnswers)) * 100
      });

      // Simular salvamento das métricas
      const saved = await this.saveMetrics();
      if (saved) {
        log.success(`Jogo finalizado - ${this.metrics.length} métricas salvas`);
      } else {
        log.error('Falha ao salvar métricas');
      }
    } else {
      log.warning('Jogo finalizado - métricas não salvas (sem login)');
    }

    return {
      shouldCollect,
      metricsCount: this.metrics.length,
      gameStats: this.gameState
    };
  }

  addMetric(type, data) {
    const metric = {
      type,
      timestamp: new Date().toISOString(),
      sessionId: this.sessionId,
      data
    };
    this.metrics.push(metric);
  }

  async saveMetrics() {
    if (!this.dashboardAuth.shouldSaveMetrics()) {
      return false;
    }

    const loginInfo = this.dashboardAuth.getLoginInfo();
    console.log('💾 Salvando métricas:', {
      sessionId: this.sessionId,
      metricsCount: this.metrics.length,
      loginType: loginInfo.loginType
    });

    // Simular salvamento baseado no tipo de login
    if (loginInfo.loginType === 'dashboard_user') {
      console.log('🔐 Salvando localmente (usuário logado)');
    } else if (loginInfo.loginType?.includes('admin')) {
      console.log('👨‍💼 Salvando com privilégios administrativos');
    }

    return true;
  }
}

// Simular hook useDashboardAuth
function simulateUseDashboardAuth() {
  const checkDashboardLogin = () => {
    const checks = {
      authToken: localStorage.getItem('authToken'),
      userData: localStorage.getItem('userData'),
      adminAuth: localStorage.getItem('adminAuth'),
      portalAuth: localStorage.getItem('portalBetina_adminAuth')
    };

    let hasValidLogin = false;
    let currentLoginType = null;

    if (checks.authToken && checks.userData) {
      try {
        const userData = JSON.parse(checks.userData);
        if (userData.email) {
          hasValidLogin = true;
          currentLoginType = 'dashboard_user';
        }
      } catch (error) {
        // Dados inválidos
      }
    }

    if (!hasValidLogin && checks.adminAuth === 'true') {
      hasValidLogin = true;
      currentLoginType = 'admin_dashboard';
    }

    if (!hasValidLogin && checks.portalAuth === 'true') {
      hasValidLogin = true;
      currentLoginType = 'portal_admin';
    }

    return {
      isLoggedIn: hasValidLogin,
      loginType: currentLoginType
    };
  };

  const shouldSaveMetrics = () => {
    const result = checkDashboardLogin();
    return result.isLoggedIn;
  };

  const getLoginInfo = () => {
    const result = checkDashboardLogin();
    return {
      isLoggedIn: result.isLoggedIn,
      loginType: result.loginType,
      userInfo: result.isLoggedIn ? { type: result.loginType } : null
    };
  };

  return { shouldSaveMetrics, getLoginInfo, checkDashboardLogin };
}

// Executar teste prático
async function runPracticalTest() {
  console.log('\n🚀 Iniciando teste prático...\n');

  const dashboardAuth = simulateUseDashboardAuth();
  
  // Cenário 1: Jogo sem login
  console.log('🎯 CENÁRIO 1: Criança jogando sem login no dashboard');
  console.log('-'.repeat(60));
  
  localStorage.clear();
  const game1 = new ColorMatchGameSimulator(dashboardAuth);
  
  const shouldCollect1 = await game1.startGame();
  await game1.playRound(true);  // Acerto
  await game1.playRound(false); // Erro
  await game1.playRound(true);  // Acerto
  const result1 = await game1.endGame();
  
  console.log('📊 Resultado:', {
    metricsCollected: shouldCollect1,
    metricsCount: result1.metricsCount,
    finalScore: result1.gameStats.score
  });

  // Cenário 2: Jogo com login de usuário
  console.log('\n🎯 CENÁRIO 2: Criança jogando com terapeuta logado no dashboard');
  console.log('-'.repeat(60));
  
  localStorage.setItem('authToken', 'therapist_token_456');
  localStorage.setItem('userData', JSON.stringify({
    email: '<EMAIL>',
    name: 'Dr. Silva',
    role: 'therapist'
  }));
  
  const game2 = new ColorMatchGameSimulator(dashboardAuth);
  
  const shouldCollect2 = await game2.startGame();
  await game2.playRound(true);  // Acerto
  await game2.playRound(true);  // Acerto
  await game2.playRound(false); // Erro
  await game2.playRound(true);  // Acerto
  const result2 = await game2.endGame();
  
  console.log('📊 Resultado:', {
    metricsCollected: shouldCollect2,
    metricsCount: result2.metricsCount,
    finalScore: result2.gameStats.score
  });

  // Cenário 3: Jogo com login administrativo
  console.log('\n🎯 CENÁRIO 3: Criança jogando com admin logado no dashboard');
  console.log('-'.repeat(60));
  
  localStorage.clear();
  localStorage.setItem('adminAuth', 'true');
  localStorage.setItem('adminLoginTime', new Date().toISOString());
  
  const game3 = new ColorMatchGameSimulator(dashboardAuth);
  
  const shouldCollect3 = await game3.startGame();
  await game3.playRound(true);  // Acerto
  await game3.playRound(true);  // Acerto
  await game3.playRound(true);  // Acerto
  const result3 = await game3.endGame();
  
  console.log('📊 Resultado:', {
    metricsCollected: shouldCollect3,
    metricsCount: result3.metricsCount,
    finalScore: result3.gameStats.score
  });

  // Resultados finais
  console.log('\n' + '='.repeat(70));
  console.log('📊 RESUMO DO TESTE PRÁTICO');
  console.log('='.repeat(70));
  
  console.log('🎮 Cenário 1 (Sem login):');
  console.log(`   • Métricas coletadas: ${result1.shouldCollect ? 'SIM' : 'NÃO'} ✅`);
  console.log(`   • Quantidade de métricas: ${result1.metricsCount}`);
  
  console.log('\n🔐 Cenário 2 (Com login de usuário):');
  console.log(`   • Métricas coletadas: ${result2.shouldCollect ? 'SIM' : 'NÃO'} ✅`);
  console.log(`   • Quantidade de métricas: ${result2.metricsCount}`);
  
  console.log('\n👨‍💼 Cenário 3 (Com login administrativo):');
  console.log(`   • Métricas coletadas: ${result3.shouldCollect ? 'SIM' : 'NÃO'} ✅`);
  console.log(`   • Quantidade de métricas: ${result3.metricsCount}`);

  // Verificar se o comportamento está correto
  const isCorrect = (
    !result1.shouldCollect && // Sem login = sem métricas
    result2.shouldCollect &&  // Com login = com métricas
    result3.shouldCollect     // Com admin = com métricas
  );

  if (isCorrect) {
    log.success('SISTEMA FUNCIONANDO CONFORME ESPECIFICADO!');
    console.log('🎉 Jogos gratuitos + métricas apenas com login ativo = ✅ IMPLEMENTADO');
  } else {
    log.error('SISTEMA NÃO ESTÁ FUNCIONANDO CONFORME ESPECIFICADO');
  }

  console.log('\n✅ TESTE PRÁTICO FINALIZADO!');
}

// Executar teste
runPracticalTest().catch(console.error);
