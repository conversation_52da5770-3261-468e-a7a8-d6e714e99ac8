/**
 * @file DashboardContainer.jsx
 * @description Container principal dos dashboards do Portal Betina V3
 * @version 3.0.0
 */

import React, { useState, useEffect } from 'react'
import PropTypes from 'prop-types'
import styles from './DashboardContainer.module.css'
import { usePremium } from '../../context/PremiumContext'
import { useAdmin } from '../../context/AdminContext'
import { useAccessibilityContext } from '../context/AccessibilityContext'
import {
  PerformanceDashboard,
  NeuropedagogicalDashboard,
  BackupEsporteDashboard,
  AdvancedAIReport,
  DASHBOARD_CONFIG,
  DASHBOARD_ORDER,
  getDashboardsByAccess,
  isPremiumDashboard,
  isAdminDashboard,
  canAccessDashboard,
} from './index.js'
import AdminGate from '../common/AdminGate/AdminGate'
import TextToSpeech from '../common/TextToSpeech/TextToSpeech'
import Button from '../common/Button/Button'
import RegistrationForm from '../auth/RegistrationForm/RegistrationForm'
import { PRICING_PLANS } from '../../config/pricingPlans.js'

/**
 * Container principal para mostrar diferentes tipos de dashboard
 */
const DashboardContainer = ({ initialTab = 'performance' }) => {
  const [activeTab, setActiveTab] = useState(initialTab)
  const [availableDashboards, setAvailableDashboards] = useState([])
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [loginData, setLoginData] = useState({ email: '', password: '' })
  const [loginError, setLoginError] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isLoggingOut, setIsLoggingOut] = useState(false)
  const [showRegistration, setShowRegistration] = useState(false)
  const { isPremium, canAccessDashboard: canAccessPremiumDashboard } = usePremium()
  const { isAdmin, canAccessIntegratedDashboard } = useAdmin()
  const { settings } = useAccessibilityContext()
  
  // Determinar quais dashboards o usuário pode acessar
  useEffect(() => {
    const accessLevel = isPremium ? 'premium' : 'public'
    const dashboards = getDashboardsByAccess(accessLevel, isAdmin)
    setAvailableDashboards(dashboards)
    
    // Se o initialTab não estiver disponível, voltar para performance
    if (initialTab !== 'performance' && !canAccessDashboard(initialTab, accessLevel, isAdmin)) {
      setActiveTab('performance')
    }
  }, [isPremium, isAdmin, initialTab])
  
  // Função de login usando a API do backend
  const handleLogin = async (e) => {
    e.preventDefault()
    setIsLoading(true)
    setLoginError('')

    console.log('🔐 Tentando login:', { 
      email: loginData.email, 
      password: loginData.password ? '***' : 'vazio' 
    })

    try {
      console.log('📡 Fazendo requisição para /api/auth/dashboard/login')
      
      const response = await fetch('/api/auth/dashboard/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: loginData.email,
          password: loginData.password,
          rememberMe: true
        })
      })

      console.log('📨 Resposta recebida:', { 
        status: response.status, 
        statusText: response.statusText,
        ok: response.ok 
      })

      const data = await response.json()
      console.log('📄 Dados da resposta:', data)

      if (data.success && data.token) {
        console.log('✅ Login bem-sucedido!')
        // Salvar token no localStorage (estrutura correta do backend)
        localStorage.setItem('authToken', data.token)
        localStorage.setItem('userData', JSON.stringify(data.user || { email: loginData.email }))
        
        setIsAuthenticated(true)
        setLoginError('')
        setIsLoading(false)
        
        // Verificar se é usuário premium (sem await para não travar)
        checkPremiumStatus()
      } else {
        console.log('❌ Login falhou:', data.message)
        setLoginError(data.message || 'Erro ao fazer login')
        setIsLoading(false)
      }
    } catch (error) {
      console.error('💥 Erro no login:', error)
      setLoginError('Erro de conexão. Tente novamente.')
      setIsLoading(false)
    }
  }

  // Verificar status premium do usuário
  const checkPremiumStatus = async () => {
    try {
      const token = localStorage.getItem('authToken')
      if (!token) return

      const response = await fetch('/api/premium/auth/status', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      const data = await response.json()
      if (response.ok && data.success) {
        // Atualizar contexto premium se necessário
        console.log('Status premium:', data.data)
      }
    } catch (error) {
      console.error('Erro ao verificar status premium:', error)
    }
  }

  // Verificar se usuário já está logado ao carregar o componente
  useEffect(() => {
    const token = localStorage.getItem('authToken')
    const userData = localStorage.getItem('userData')

    console.log('🔍 Verificando autenticação:', { token: !!token, userData: !!userData })

    if (token && userData) {
      try {
        const user = JSON.parse(userData)
        console.log('✅ Usuário encontrado no localStorage:', user.email)
        setIsAuthenticated(true)
        // Verificar se token ainda é válido
        checkPremiumStatus()
      } catch (error) {
        console.error('❌ Erro ao verificar dados salvos:', error)
        // Limpar dados inválidos
        localStorage.removeItem('authToken')
        localStorage.removeItem('refreshToken')
        localStorage.removeItem('userData')
        setIsAuthenticated(false)
      }
    } else {
      console.log('🚫 Usuário não autenticado - redirecionando para login')
      setIsAuthenticated(false)
    }
  }, [])

  // Auto-logout por inatividade (2 horas)
  useEffect(() => {
    if (!isAuthenticated) return

    let inactivityTimer
    let warningTimer
    const INACTIVITY_TIME = 2 * 60 * 60 * 1000 // 2 horas
    const WARNING_TIME = INACTIVITY_TIME - (5 * 60 * 1000) // 5 minutos antes

    const resetTimer = () => {
      clearTimeout(inactivityTimer)
      clearTimeout(warningTimer)

      // Aviso 5 minutos antes do logout
      warningTimer = setTimeout(() => {
        const shouldStay = window.confirm(
          'Sua sessão expirará em 5 minutos por inatividade.\n\nDeseja continuar conectado?'
        )
        if (!shouldStay) {
          handleLogout()
        }
      }, WARNING_TIME)

      // Logout automático
      inactivityTimer = setTimeout(() => {
        alert('Sessão expirada por inatividade. Você será desconectado.')
        handleLogout()
      }, INACTIVITY_TIME)
    }

    // Eventos que resetam o timer
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']

    const resetTimerHandler = () => resetTimer()

    // Adicionar listeners
    events.forEach(event => {
      document.addEventListener(event, resetTimerHandler, true)
    })

    // Iniciar timer
    resetTimer()

    // Cleanup
    return () => {
      clearTimeout(inactivityTimer)
      clearTimeout(warningTimer)
      events.forEach(event => {
        document.removeEventListener(event, resetTimerHandler, true)
      })
    }
  }, [isAuthenticated])

  const handleInputChange = (field, value) => {
    setLoginData(prev => ({
      ...prev,
      [field]: value
    }))
    setLoginError('')
  }

  // Função de logout
  const handleLogout = async () => {
    setIsLoggingOut(true)

    try {
      const token = localStorage.getItem('authToken')

      if (token) {
        // Tentar chamar API de logout
        try {
          await fetch('/api/auth/logout', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            }
          })
          console.log('✅ Logout via API bem-sucedido')
        } catch (apiError) {
          console.warn('⚠️ Erro na API de logout, continuando com logout local:', apiError.message)
        }
      }

      // Limpar dados locais
      localStorage.removeItem('authToken')
      localStorage.removeItem('refreshToken')
      localStorage.removeItem('userData')

      // Resetar estado
      setIsAuthenticated(false)
      setLoginData({ email: '', password: '' })
      setLoginError('')
      setActiveTab('performance')

      console.log('✅ Logout local concluído')

    } catch (error) {
      console.error('❌ Erro durante logout:', error)
      // Mesmo com erro, fazer logout local
      localStorage.removeItem('authToken')
      localStorage.removeItem('refreshToken')
      localStorage.removeItem('userData')
      setIsAuthenticated(false)
    } finally {
      setIsLoggingOut(false)
    }
  }

  // Verificar se precisa mostrar tela de login para dashboards premium/admin
  const needsAuthentication = () => {
    console.log('🔐 Verificando autenticação:', { 
      activeTab, 
      isAuthenticated, 
      isAdmin, 
      isPremium,
      isAdminTab: isAdminDashboard(activeTab),
      isPremiumTab: isPremiumDashboard(activeTab)
    })
    
    // TODOS OS DASHBOARDS REQUEREM LOGIN AGORA
    return !isAuthenticated
  }

  // Renderizar tela de login
  const renderLoginScreen = () => {
    return (
      <div className={styles.loginContainer}>
        <div className={styles.loginBox}>          <div style={{ textAlign: 'center', marginBottom: '1.5rem' }}>
            <h1 style={{ fontSize: '1.8rem', marginBottom: '0.5rem' }}>
              {isAdminDashboard(activeTab) ? '🔐 Admin' : '🔐 Premium'}
            </h1>
            <p style={{ opacity: 0.9, lineHeight: 1.4, fontSize: '0.9rem' }}>
              {isAdminDashboard(activeTab) 
                ? 'Credenciais administrativas' 
                : 'Entre para acessar os dashboards'
              }
            </p>
          </div>

          <form onSubmit={handleLogin} style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            <div>
              <label style={{ display: 'block', marginBottom: '0.3rem', fontWeight: '500', fontSize: '0.9rem' }}>
                Email:
              </label>
              <input
                type="email"
                value={loginData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={styles.loginInput}
                placeholder="Digite seu email"
                required
              />
            </div>

            <div>
              <label style={{ display: 'block', marginBottom: '0.3rem', fontWeight: '500', fontSize: '0.9rem' }}>
                Senha:
              </label>
              <input
                type="password"
                value={loginData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                className={styles.loginInput}
                placeholder="Digite sua senha"
                required
              />
            </div>

            {loginError && (
              <div style={{
                background: 'rgba(239, 68, 68, 0.2)',
                color: '#fecaca',
                padding: '0.8rem',
                borderRadius: '10px',
                border: '2px solid #ef4444',
                textAlign: 'center',
                fontSize: '0.85rem'
              }}>
                {loginError}
              </div>
            )}

            <button
              type="submit"
              disabled={isLoading}
              className={styles.loginButton}
            >
              {isLoading ? '🔄 Entrando...' : '🔓 Entrar'}
            </button>
          </form>



          {/* Seção de Preços e Cadastro */}
          <div style={{
            marginTop: '2rem',
            padding: '1.5rem',
            background: 'rgba(99, 102, 241, 0.1)',
            borderRadius: '15px',
            border: '2px solid #6366f1',
            textAlign: 'center'
          }}>
            <h3 style={{
              color: '#ffffff',
              margin: '0 0 1rem 0',
              fontSize: '1.2rem',
              fontWeight: '700',
              textShadow: '0 2px 4px rgba(0,0,0,0.3)'
            }}>
              🚀 Não tem conta? Cadastre-se!
            </h3>

            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
              gap: '1rem',
              marginBottom: '1.5rem'
            }}>
              {Object.entries(PRICING_PLANS).map(([planId, plan]) => (
                <div
                  key={planId}
                  style={{
                    background: plan.popular
                      ? 'linear-gradient(135deg, rgba(245, 158, 11, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%)'
                      : 'rgba(255, 255, 255, 0.05)',
                    border: plan.popular ? '2px solid #f59e0b' : '2px solid rgba(255, 255, 255, 0.2)',
                    borderRadius: '16px',
                    padding: '1.5rem',
                    position: 'relative',
                    transition: 'all 0.3s ease',
                    cursor: 'pointer'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.transform = 'translateY(-4px)'
                    e.target.style.boxShadow = plan.popular
                      ? '0 12px 40px rgba(245, 158, 11, 0.3)'
                      : '0 12px 40px rgba(99, 102, 241, 0.2)'
                    e.target.style.borderColor = plan.popular ? '#f59e0b' : '#6366f1'
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.transform = 'translateY(0)'
                    e.target.style.boxShadow = 'none'
                    e.target.style.borderColor = plan.popular ? '#f59e0b' : 'rgba(255, 255, 255, 0.2)'
                  }}
                >
                  {plan.popular && (
                    <div style={{
                      position: 'absolute',
                      top: '-8px',
                      left: '50%',
                      transform: 'translateX(-50%)',
                      background: '#f59e0b',
                      color: '#ffffff',
                      padding: '2px 8px',
                      borderRadius: '4px',
                      fontSize: '0.7rem',
                      fontWeight: '600'
                    }}>
                      POPULAR
                    </div>
                  )}

                  <h4 style={{
                    color: '#ffffff',
                    margin: '0 0 0.5rem 0',
                    fontSize: '0.9rem'
                  }}>
                    {plan.name}
                  </h4>

                  <div style={{
                    background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
                    borderRadius: '12px',
                    padding: '12px',
                    marginBottom: '1rem',
                    textAlign: 'center',
                    boxShadow: '0 4px 15px rgba(99, 102, 241, 0.3)'
                  }}>
                    <div style={{
                      color: '#ffffff',
                      fontSize: '2.2rem',
                      fontWeight: '800',
                      lineHeight: '1',
                      textShadow: '0 2px 4px rgba(0,0,0,0.3)'
                    }}>
                      R$ {plan.price.toFixed(2)}
                    </div>
                    <div style={{
                      fontSize: '0.9rem',
                      color: '#e0e7ff',
                      fontWeight: '500',
                      marginTop: '4px',
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px'
                    }}>
                      por {plan.period}
                    </div>
                  </div>

                  <p style={{
                    color: '#d1d5db',
                    fontSize: '0.75rem',
                    margin: '0 0 0.5rem 0',
                    lineHeight: '1.3'
                  }}>
                    {plan.description}
                  </p>

                  <ul style={{
                    listStyle: 'none',
                    padding: 0,
                    margin: 0,
                    fontSize: '0.7rem'
                  }}>
                    {plan.features.slice(0, 3).map((feature, index) => (
                      <li key={index} style={{
                        color: '#e5e7eb',
                        marginBottom: '2px',
                        paddingLeft: '12px',
                        position: 'relative'
                      }}>
                        <span style={{
                          position: 'absolute',
                          left: 0,
                          color: '#10b981'
                        }}>✓</span>
                        {feature}
                      </li>
                    ))}
                    {plan.features.length > 3 && (
                      <li style={{
                        color: '#9ca3af',
                        fontSize: '0.65rem',
                        fontStyle: 'italic',
                        marginTop: '4px'
                      }}>
                        +{plan.features.length - 3} recursos
                      </li>
                    )}
                  </ul>
                </div>
              ))}
            </div>

            <button
              onClick={() => setShowRegistration(true)}
              style={{
                background: 'linear-gradient(135deg, #6366f1 0%, #4f46e5 100%)',
                color: '#ffffff',
                border: 'none',
                padding: '14px 28px',
                borderRadius: '12px',
                fontSize: '1.1rem',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                boxShadow: '0 6px 20px rgba(99, 102, 241, 0.4)',
                textTransform: 'uppercase',
                letterSpacing: '0.5px'
              }}
              onMouseOver={(e) => {
                e.target.style.transform = 'translateY(-3px)'
                e.target.style.boxShadow = '0 10px 30px rgba(99, 102, 241, 0.5)'
                e.target.style.background = 'linear-gradient(135deg, #7c3aed 0%, #6366f1 100%)'
              }}
              onMouseOut={(e) => {
                e.target.style.transform = 'translateY(0)'
                e.target.style.boxShadow = '0 6px 20px rgba(99, 102, 241, 0.4)'
                e.target.style.background = 'linear-gradient(135deg, #6366f1 0%, #4f46e5 100%)'
              }}
            >
              🚀 Criar Conta Premium
            </button>

            <div style={{
              marginTop: '1rem',
              fontSize: '0.75rem',
              color: '#9ca3af',
              lineHeight: '1.4'
            }}>
              <strong>Como funciona:</strong><br />
              1. Preencha o formulário de cadastro<br />
              2. Escolha seu plano e pague via PIX<br />
              3. Aguarde aprovação (até 24h)<br />
              4. Receba acesso completo aos dashboards
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Renderizar o dashboard ativo
  const renderDashboard = () => {
    console.log('🎨 Renderizando dashboard:', { 
      activeTab, 
      needsAuth: needsAuthentication(),
      isAuthenticated 
    })
    
    // Verificar se precisa de autenticação para dashboards premium
    if (needsAuthentication()) {
      console.log('🚫 Exibindo tela de login')
      return renderLoginScreen()
    }
    
    console.log('✅ Usuário autenticado - exibindo dashboard')
    
    // Verificar acesso específico para dashboard administrativo
    if (isAdminDashboard(activeTab) && (!isAdmin || !canAccessIntegratedDashboard())) {
      return (        <AdminGate 
          title="Sistema Integrado - Acesso Restrito"
          message="Este dashboard contém informações administrativas sensíveis e está disponível apenas para administradores autorizados do sistema."
        />
      )
    }
    
    // Renderizar o dashboard apropriado
    switch(activeTab) {
      case 'performance':
        return <PerformanceDashboard />
      case 'neuropedagogical':
        return <NeuropedagogicalDashboard />
      case 'backupEsporte':
        return <BackupEsporteDashboard />
      case 'integrated':
        return (
          <div className={styles.placeholderDashboard}>
            <h3>🔄 Sistema Integrado</h3>
            <p>Dashboard em desenvolvimento. Use o Relatório A para análises avançadas.</p>
          </div>
        )
      case 'relatorioA':
        return <AdvancedAIReport />
      default:
        return <PerformanceDashboard />
    }
  }

  // Ordenar dashboards conforme a ordem definida no DASHBOARD_ORDER
  const sortedDashboards = [...availableDashboards].sort((a, b) => {
    const aIndex = DASHBOARD_ORDER.indexOf(a[0])
    const bIndex = DASHBOARD_ORDER.indexOf(b[0])
    return aIndex - bIndex
  })
  
  console.log('🔄 Renderizando componente principal')
  
  return (
    <div className={styles.dashboardContainer}>
      {/* Header com botão de logout - sempre mostra quando não está na tela de login */}
      {!needsAuthentication() && (
        <div className={styles.dashboardHeader}>
          <div className={styles.headerLeft}>
            <h1 className={styles.dashboardTitle}>
              📊 Dashboard de Métricas
            </h1>
            <p className={styles.dashboardSubtitle}>
              Análise de desempenho e progresso terapêutico
            </p>
          </div>
          <div className={styles.headerRight}>
            <button
              onClick={handleLogout}
              disabled={isLoggingOut}
              className={styles.logoutButton}
              title="Sair do dashboard"
            >
              {isLoggingOut ? '🔄 Saindo...' : '🚪 Sair'}
            </button>
          </div>
        </div>
      )}

      {/* Navegação entre dashboards */}
      <div className={styles.dashboardTabs}>
        {sortedDashboards.map(([key, config]) => (
          <button
            key={key}
            className={`${styles.dashboardTab} ${activeTab === key ? styles.active : ''}`}
            onClick={() => setActiveTab(key)}
            aria-pressed={activeTab === key}
          >
            <span aria-hidden="true">{config.icon}</span> {config.title}
          </button>        ))}

        {/* Botão de logout nas tabs como alternativa */}
        {!needsAuthentication() && (
          <button
            onClick={handleLogout}
            disabled={isLoggingOut}
            className={styles.logoutTab}
            title="Sair do dashboard"
          >
            {isLoggingOut ? '🔄' : '🚪'} Sair
          </button>
        )}
      </div>
      
      {/* Conteúdo do dashboard */}
      <div className={styles.dashboardContent}>
        <div className={styles.dashboardWrapper}>
          {renderDashboard()}
        </div>
      </div>

      {/* Modal de Cadastro */}
      {showRegistration && (
        <RegistrationForm
          onClose={() => setShowRegistration(false)}
          onSuccess={(data) => {
            console.log('Cadastro realizado:', data)
            setShowRegistration(false)
            // Aqui você pode adicionar lógica adicional após o cadastro
          }}
        />
      )}
    </div>
  )
}

DashboardContainer.propTypes = {
  initialTab: PropTypes.string
}

export default DashboardContainer
