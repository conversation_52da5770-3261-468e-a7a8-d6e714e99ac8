#!/usr/bin/env node

/**
 * @file test-metrics-dashboard-final.js
 * @description Teste final do sistema: Dashboard de Métricas + Jogos
 * @version 1.0.0
 */

console.log('📊 TESTE FINAL: Dashboard de Métricas + Jogos Gratuitos');
console.log('=' .repeat(70));

// Simular localStorage para Node.js
global.localStorage = {
  data: {},
  getItem(key) {
    return this.data[key] || null;
  },
  setItem(key, value) {
    this.data[key] = value;
  },
  removeItem(key) {
    delete this.data[key];
  },
  clear() {
    this.data = {};
  }
};

// Simular console com cores
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️ ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️ ${msg}${colors.reset}`),
  dashboard: (msg) => console.log(`${colors.cyan}📊 ${msg}${colors.reset}`)
};

// Simular hook useDashboardAuth (APENAS DASHBOARD DE MÉTRICAS)
function simulateMetricsDashboardAuth() {
  const checkDashboardLogin = () => {
    // 📊 VERIFICAR APENAS O DASHBOARD DE MÉTRICAS
    const authToken = localStorage.getItem('authToken');
    const userData = localStorage.getItem('userData');

    let hasValidLogin = false;
    let currentLoginType = null;
    let userInfo = null;

    // Verificar se há login ativo no dashboard de métricas
    if (authToken && userData) {
      try {
        const parsedUserData = JSON.parse(userData);
        if (parsedUserData.email) {
          hasValidLogin = true;
          currentLoginType = 'metrics_dashboard';
          userInfo = {
            email: parsedUserData.email,
            name: parsedUserData.name || 'Usuário',
            role: parsedUserData.role || 'user',
            type: 'metrics_user'
          };
        }
      } catch (error) {
        console.warn('📊 Dados de usuário inválidos no dashboard de métricas');
      }
    }

    return {
      isLoggedIn: hasValidLogin,
      loginType: currentLoginType,
      userInfo
    };
  };

  const shouldSaveMetrics = () => {
    const result = checkDashboardLogin();
    return result.isLoggedIn;
  };

  const getLoginInfo = () => {
    return checkDashboardLogin();
  };

  return { shouldSaveMetrics, getLoginInfo, checkDashboardLogin };
}

// Simular jogo com métricas reais
class GameWithMetrics {
  constructor(gameName, dashboardAuth) {
    this.gameName = gameName;
    this.dashboardAuth = dashboardAuth;
    this.sessionId = `${gameName.toLowerCase()}_${Date.now()}`;
    this.metrics = [];
    this.startTime = new Date();
  }

  async startGame() {
    log.dashboard(`Iniciando ${this.gameName}...`);
    
    const loginInfo = this.dashboardAuth.getLoginInfo();
    console.log(`🔍 Status do dashboard de métricas:`, {
      isLoggedIn: loginInfo.isLoggedIn,
      loginType: loginInfo.loginType,
      userEmail: loginInfo.userInfo?.email || 'N/A'
    });

    if (this.dashboardAuth.shouldSaveMetrics()) {
      this.addMetric('GAME_START', {
        gameType: this.gameName,
        timestamp: new Date().toISOString(),
        userInfo: loginInfo.userInfo
      });
      log.success('Métrica de início coletada');
      return true;
    } else {
      log.warning('Métricas não coletadas - sem login no dashboard');
      return false;
    }
  }

  async playAction(actionType, data = {}) {
    if (this.dashboardAuth.shouldSaveMetrics()) {
      this.addMetric('USER_ACTION', {
        action: actionType,
        timestamp: new Date().toISOString(),
        ...data
      });
      log.info(`Métrica de ação coletada: ${actionType}`);
      return true;
    }
    return false;
  }

  async endGame(finalScore = 0) {
    const endTime = new Date();
    const duration = endTime.getTime() - this.startTime.getTime();
    
    if (this.dashboardAuth.shouldSaveMetrics()) {
      this.addMetric('GAME_END', {
        finalScore,
        duration,
        timestamp: endTime.toISOString(),
        metricsCollected: this.metrics.length
      });

      const loginInfo = this.dashboardAuth.getLoginInfo();
      console.log('💾 Salvando métricas no dashboard:', {
        sessionId: this.sessionId,
        metricsCount: this.metrics.length,
        userEmail: loginInfo.userInfo.email,
        gameName: this.gameName
      });

      log.success(`${this.gameName} finalizado - ${this.metrics.length} métricas salvas`);
      return { saved: true, count: this.metrics.length };
    } else {
      log.warning(`${this.gameName} finalizado - métricas não salvas (sem login)`);
      return { saved: false, count: 0 };
    }
  }

  addMetric(type, data) {
    const metric = {
      type,
      timestamp: new Date().toISOString(),
      sessionId: this.sessionId,
      gameName: this.gameName,
      data
    };
    this.metrics.push(metric);
  }
}

// Executar teste completo
async function runFinalTest() {
  console.log('\n🚀 Iniciando teste final do sistema...\n');

  const dashboardAuth = simulateMetricsDashboardAuth();
  const testResults = [];

  // CENÁRIO 1: Criança joga sem login no dashboard de métricas
  console.log('🎯 CENÁRIO 1: Criança jogando sem login no dashboard de métricas');
  console.log('-'.repeat(65));
  
  localStorage.clear();
  
  const game1 = new GameWithMetrics('ColorMatch', dashboardAuth);
  const started1 = await game1.startGame();
  await game1.playAction('color_selection', { color: 'red', correct: true });
  await game1.playAction('color_selection', { color: 'blue', correct: false });
  await game1.playAction('color_selection', { color: 'green', correct: true });
  const result1 = await game1.endGame(20);
  
  testResults.push({
    scenario: 'Sem login no dashboard',
    metricsCollected: started1,
    metricsSaved: result1.saved,
    metricsCount: result1.count
  });

  // CENÁRIO 2: Terapeuta faz login no dashboard de métricas
  console.log('\n🎯 CENÁRIO 2: Terapeuta logado no dashboard de métricas');
  console.log('-'.repeat(65));
  
  // Simular login no dashboard de métricas
  localStorage.setItem('authToken', 'metrics_dashboard_token_789');
  localStorage.setItem('userData', JSON.stringify({
    email: '<EMAIL>',
    name: 'Dr. Silva',
    role: 'therapist',
    id: 'therapist_123'
  }));
  
  const game2 = new GameWithMetrics('MemoryGame', dashboardAuth);
  const started2 = await game2.startGame();
  await game2.playAction('card_flip', { cardId: 1, matched: false });
  await game2.playAction('card_flip', { cardId: 5, matched: true });
  await game2.playAction('level_complete', { level: 1, time: 45000 });
  const result2 = await game2.endGame(85);
  
  testResults.push({
    scenario: 'Com login no dashboard',
    metricsCollected: started2,
    metricsSaved: result2.saved,
    metricsCount: result2.count
  });

  // CENÁRIO 3: Múltiplos jogos com login ativo
  console.log('\n🎯 CENÁRIO 3: Múltiplos jogos com login ativo no dashboard');
  console.log('-'.repeat(65));
  
  const games = ['LetterRecognition', 'CreativePainting', 'MusicalSequence'];
  const multiGameResults = [];
  
  for (const gameName of games) {
    const game = new GameWithMetrics(gameName, dashboardAuth);
    const started = await game.startGame();
    
    // Simular algumas ações baseadas no jogo
    if (gameName === 'LetterRecognition') {
      await game.playAction('letter_recognition', { letter: 'A', correct: true });
      await game.playAction('letter_recognition', { letter: 'B', correct: true });
    } else if (gameName === 'CreativePainting') {
      await game.playAction('brush_stroke', { color: '#FF0000', pressure: 0.8 });
      await game.playAction('color_change', { newColor: '#00FF00' });
      await game.playAction('save_artwork', { artworkId: 'art_123' });
    } else if (gameName === 'MusicalSequence') {
      await game.playAction('note_played', { note: 'C4', timing: 1000 });
      await game.playAction('sequence_complete', { accuracy: 95 });
    }
    
    const result = await game.endGame(Math.floor(Math.random() * 100));
    multiGameResults.push({
      game: gameName,
      metricsCount: result.count
    });
  }

  // CENÁRIO 4: Logout do dashboard durante o jogo
  console.log('\n🎯 CENÁRIO 4: Logout do dashboard durante o jogo');
  console.log('-'.repeat(65));
  
  const game4 = new GameWithMetrics('PadroesVisuais', dashboardAuth);
  const started4 = await game4.startGame();
  await game4.playAction('pattern_recognition', { pattern: 'circle', correct: true });
  
  // Simular logout
  console.log('🚪 Usuário fez logout do dashboard de métricas...');
  localStorage.removeItem('authToken');
  localStorage.removeItem('userData');
  
  await game4.playAction('pattern_recognition', { pattern: 'square', correct: false });
  const result4 = await game4.endGame(15);
  
  testResults.push({
    scenario: 'Logout durante jogo',
    metricsCollected: started4,
    metricsSaved: result4.saved,
    metricsCount: result4.count
  });

  // RESULTADOS FINAIS
  console.log('\n' + '='.repeat(70));
  console.log('📊 RESULTADOS FINAIS DO TESTE');
  console.log('='.repeat(70));
  
  console.log('📈 Resumo por cenário:');
  testResults.forEach((result, index) => {
    const status = result.metricsSaved ? '✅ SALVAS' : '❌ NÃO SALVAS';
    console.log(`   ${index + 1}. ${result.scenario}: ${result.metricsCount} métricas ${status}`);
  });

  console.log('\n🎮 Múltiplos jogos com login:');
  multiGameResults.forEach(result => {
    console.log(`   • ${result.game}: ${result.metricsCount} métricas salvas`);
  });

  const totalMetricsWithLogin = multiGameResults.reduce((sum, r) => sum + r.metricsCount, 0) + 
                                testResults.filter(r => r.metricsSaved).reduce((sum, r) => sum + r.metricsCount, 0);
  
  console.log(`\n📊 Total de métricas salvas com login ativo: ${totalMetricsWithLogin}`);
  console.log(`📊 Total de métricas perdidas sem login: ${testResults.filter(r => !r.metricsSaved).length > 0 ? 'Algumas' : 'Nenhuma'}`);

  // Verificar se o comportamento está correto
  const correctBehavior = (
    !testResults[0].metricsSaved && // Sem login = sem métricas
    testResults[1].metricsSaved &&  // Com login = com métricas
    testResults[2].metricsSaved     // Logout = sem métricas nas ações posteriores
  );

  if (correctBehavior) {
    log.success('SISTEMA FUNCIONANDO CONFORME ESPECIFICADO!');
    console.log('🎉 ✅ Jogos gratuitos');
    console.log('🎉 ✅ Métricas apenas com login no dashboard de métricas');
    console.log('🎉 ✅ Sistema diferencia corretamente os dois dashboards');
  } else {
    log.error('SISTEMA NÃO ESTÁ FUNCIONANDO CONFORME ESPECIFICADO');
  }

  console.log('\n📋 ESPECIFICAÇÃO IMPLEMENTADA:');
  console.log('   🎮 Jogos são 100% gratuitos');
  console.log('   📊 Métricas só são salvas quando há login no DASHBOARD DE MÉTRICAS');
  console.log('   🔐 Dashboard de métricas usa authToken + userData');
  console.log('   👨‍💼 Dashboard administrativo é separado e não afeta métricas');

  console.log('\n✅ TESTE FINAL CONCLUÍDO!');
}

// Executar teste
runFinalTest().catch(console.error);
