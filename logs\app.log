{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-06-18T23:44:36.106Z","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-06-18T23:44:54.936Z","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-06-18T23:45:23.706Z","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-06-18T23:53:21.347Z","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-06-19T02:11:26.824Z","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-06-19T02:11:27.050Z","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"8ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":501,"timestamp":"2025-06-19T03:16:53.285Z","url":"/api/dashboard/therapeutic","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; pt-BR) WindowsPowerShell/5.1.19041.5965","userId":"anonymous"}
{"duration":"8ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-06-21T02:43:31.562Z","url":"/api/placeholder/40/40","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"7ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":404,"timestamp":"2025-06-30T19:33:59.930Z","url":"/api/games/PadroesVisuais/session","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; pt-BR) WindowsPowerShell/5.1.19041.5965","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":400,"timestamp":"2025-06-30T19:34:19.124Z","url":"/api/dashboard/overview","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; pt-BR) WindowsPowerShell/5.1.19041.5965","userId":"anonymous"}
{"duration":"8ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-06-30T20:47:40.249Z","url":"/debug-dashboard.html?id=ed10c3b3-9f4a-4e15-82eb-99a3975955c1&vscodeBrowserReqId=1751316460220","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-06-30T20:47:48.242Z","url":"/debug-dashboard.html","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-06-30T20:47:48.620Z","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"13ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:40:12.396Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:40:13.694Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:40:15.736Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:40:19.558Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:40:19.783Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:40:20.817Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:40:22.888Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:40:27.407Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:40:27.813Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:40:36.407Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:40:43.861Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:40:53.414Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:41:15.931Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:41:26.415Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"6ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:42:20.000Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:42:31.433Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"7ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:44:28.039Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:44:39.562Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"7ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:48:26.946Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:48:28.118Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:48:30.148Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:48:34.187Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:48:42.228Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"16ms","ip":"::ffff:127.0.0.1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:48:57.786Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:48:58.345Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"6ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:49:30.395Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"7ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:50:34.521Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"7ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T22:52:42.653Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T23:03:26.881Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T23:05:34.918Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T23:06:15.175Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T23:06:16.234Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T23:06:18.285Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T23:06:22.328Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T23:06:30.403Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T23:06:46.452Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T23:07:18.556Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-01T23:08:22.613Z","url":"/ws","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"22ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":500,"timestamp":"2025-07-02T01:31:03.896Z","url":"/api/metrics/progress","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; pt-BR) WindowsPowerShell/5.1.19041.5965","userId":"anonymous"}
{"duration":"6ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":500,"timestamp":"2025-07-02T01:32:37.769Z","url":"/api/metrics/progress","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; pt-BR) WindowsPowerShell/5.1.19041.5965","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-02T01:34:31.404Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; pt-BR) WindowsPowerShell/5.1.19041.5965","userId":"anonymous"}
{"duration":"4ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-02T01:40:17.003Z","url":"/analysis?id=60798d01-7ba2-444e-b0d8-9f91af97b550&vscodeBrowserReqId=1751420416984","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":500,"timestamp":"2025-07-02T01:50:06.443Z","url":"/api/dashboard/therapeutic","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; pt-BR) WindowsPowerShell/5.1.19041.5965","userId":"anonymous"}
{"duration":"7ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-02T16:07:54.423Z","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-02T16:07:54.725Z","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-02T22:54:44.279Z","url":"/api/metrics/performance?userId=test123&timeframe=30d","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; pt-BR) WindowsPowerShell/5.1.19041.5965","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-02T22:54:51.880Z","url":"/api/metrics/multisensory?userId=test123&timeframe=30d","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; pt-BR) WindowsPowerShell/5.1.19041.5965","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-02T22:59:03.375Z","url":"/api/metrics/interactions","userAgent":"node","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":400,"timestamp":"2025-07-02T22:59:03.499Z","url":"/api/metrics/interactions","userAgent":"node","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-02T22:59:03.608Z","url":"/api/metrics/multisensory","userAgent":"node","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-02T22:59:03.721Z","url":"/api/metrics/multisensory","userAgent":"node","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":404,"timestamp":"2025-07-02T22:59:03.967Z","url":"/api/metrics/progress","userAgent":"node","userId":"anonymous"}
{"duration":"4ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":500,"timestamp":"2025-07-02T22:59:04.077Z","url":"/api/dashboard/therapeutic","userAgent":"node","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-02T22:59:08.768Z","url":"/api/metrics/interactions","userAgent":"node","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":400,"timestamp":"2025-07-02T22:59:08.880Z","url":"/api/metrics/interactions","userAgent":"node","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-02T22:59:08.989Z","url":"/api/metrics/multisensory","userAgent":"node","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-02T22:59:09.101Z","url":"/api/metrics/multisensory","userAgent":"node","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":404,"timestamp":"2025-07-02T22:59:09.336Z","url":"/api/metrics/progress","userAgent":"node","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":500,"timestamp":"2025-07-02T22:59:09.448Z","url":"/api/dashboard/therapeutic","userAgent":"node","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":400,"timestamp":"2025-07-02T22:59:09.679Z","url":"/api/dashboard/behavior","userAgent":"node","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":400,"timestamp":"2025-07-02T22:59:09.790Z","url":"/api/dashboard/overview","userAgent":"node","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-02T22:59:09.901Z","url":"/api/dashboard/cache","userAgent":"node","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":400,"timestamp":"2025-07-02T22:59:10.014Z","url":"/api/reports/therapeutic","userAgent":"node","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":400,"timestamp":"2025-07-02T22:59:10.124Z","url":"/api/reports/progress","userAgent":"node","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-02T22:59:10.234Z","url":"/api/reports/export","userAgent":"node","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":401,"timestamp":"2025-07-02T22:59:10.345Z","url":"/api/profiles/users","userAgent":"node","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":404,"timestamp":"2025-07-02T22:59:10.457Z","url":"/api/profiles/users","userAgent":"node","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-02T22:59:10.565Z","url":"/api/profiles/users/active","userAgent":"node","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":401,"timestamp":"2025-07-02T22:59:10.676Z","url":"/api/profiles/children","userAgent":"node","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":400,"timestamp":"2025-07-02T22:59:10.789Z","url":"/api/profiles/children","userAgent":"node","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":400,"timestamp":"2025-07-02T23:17:27.396Z","url":"/api/metrics/interactions","userAgent":"node","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":400,"timestamp":"2025-07-02T23:17:27.631Z","url":"/api/metrics/multisensory","userAgent":"node","userId":"anonymous"}
{"duration":"8ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-02T23:30:38.463Z","url":"/","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-02T23:30:38.612Z","url":"/.well-known/appspecific/com.chrome.devtools.json","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":500,"timestamp":"2025-07-02T23:34:23.677Z","url":"/api/metrics/interactions","userAgent":"node","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":500,"timestamp":"2025-07-02T23:34:23.910Z","url":"/api/metrics/multisensory","userAgent":"node","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-06T03:05:06.811Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; pt-BR) WindowsPowerShell/5.1.19041.5965","userId":"anonymous"}
{"duration":"7ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":500,"timestamp":"2025-07-06T03:05:33.944Z","url":"/api/metrics/sessions","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; pt-BR) WindowsPowerShell/5.1.19041.5965","userId":"anonymous"}
{"duration":"7ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-06T03:13:44.137Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; pt-BR) WindowsPowerShell/5.1.19041.5965","userId":"anonymous"}
{"duration":"7ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-06T12:41:17.742Z","url":"/?id=27d06b3b-a887-4b33-a51e-a559447d7f34&vscodeBrowserReqId=1751805677708","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"8ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-08T03:11:42.838Z","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-08T03:11:42.944Z","url":"/.well-known/appspecific/com.chrome.devtools.json","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-08T03:11:43.050Z","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","userId":"anonymous"}
{"duration":"8ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":404,"timestamp":"2025-07-08T19:40:28.871Z","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"11ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-10T13:15:48.363Z","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-10T13:15:48.587Z","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"7ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-10T13:21:29.734Z","url":"/?id=7e052f8f-7935-40a9-94c1-5e575fa34c88&vscodeBrowserReqId=1752153689699","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.101.2 Chrome/134.0.6998.205 Electron/35.5.1 Safari/537.36","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-10T13:21:54.657Z","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-10T13:21:54.982Z","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-10T13:21:58.271Z","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"4ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-12T00:22:08.573Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"4ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-13T21:23:09.003Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-13T21:23:09.028Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"9ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-14T15:14:08.444Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-14T15:14:08.480Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"4ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-14T16:57:07.988Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-14T16:57:08.013Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-14T17:16:20.096Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-14T17:16:20.125Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-14T18:05:55.851Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-14T18:05:55.868Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-14T18:41:26.435Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-14T18:41:26.461Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"6ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-14T19:17:57.420Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-14T19:17:57.458Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-15T20:47:50.636Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-15T20:47:50.660Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-15T21:04:46.651Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-15T21:04:46.675Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"6ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-15T23:57:37.713Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-15T23:57:37.737Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T10:54:59.342Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T10:54:59.372Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"8ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T12:08:04.317Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T12:08:04.346Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T12:08:44.486Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T12:08:44.502Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T12:57:43.306Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T12:57:43.318Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:02:57.101Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:10:41.708Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:10:41.736Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"8ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:19:12.135Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:19:12.159Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:20:00.218Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:20:11.507Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:21:42.828Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:21:42.838Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:22:31.518Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:22:43.515Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:23:36.490Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:23:36.506Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:34:19.413Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:34:19.425Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:36:19.927Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:36:19.939Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:39:43.617Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:39:43.632Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:43:55.664Z","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:43:55.937Z","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:45:03.735Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:45:03.747Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"7ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:45:18.901Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:45:18.929Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"4ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:47:05.919Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T13:47:16.515Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:02:52.451Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:02:52.470Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"4ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:04:12.815Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:04:12.835Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"8ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:04:23.105Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:04:23.132Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:05:06.606Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:05:06.626Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:05:43.451Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:05:43.475Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:06:14.549Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:06:31.525Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":401,"timestamp":"2025-07-16T14:06:46.222Z","url":"/api/backup/status/user_demo","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:06:46.225Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":401,"timestamp":"2025-07-16T14:06:46.239Z","url":"/api/backup/status/user_demo","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":401,"timestamp":"2025-07-16T14:06:46.249Z","url":"/api/backup/status/user_demo","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":401,"timestamp":"2025-07-16T14:06:58.073Z","url":"/api/backup/status/user_demo","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:06:58.076Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":401,"timestamp":"2025-07-16T14:06:58.083Z","url":"/api/backup/status/user_demo","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":401,"timestamp":"2025-07-16T14:07:12.491Z","url":"/api/backup/status/user_demo","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:07:12.496Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":401,"timestamp":"2025-07-16T14:07:12.502Z","url":"/api/backup/status/user_demo","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:07:36.134Z","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:08:21.064Z","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:08:54.237Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"4ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":401,"timestamp":"2025-07-16T14:08:54.256Z","url":"/api/backup/status/user_demo","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:08:54.269Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":401,"timestamp":"2025-07-16T14:08:54.274Z","url":"/api/backup/status/user_demo","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:11:54.273Z","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:13:42.294Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:13:42.308Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":400,"timestamp":"2025-07-16T14:18:00.698Z","url":"/api/backup/user-data","userAgent":"node","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":400,"timestamp":"2025-07-16T14:18:00.716Z","url":"/api/backup/import","userAgent":"node","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":400,"timestamp":"2025-07-16T14:18:00.723Z","url":"/api/backup/import","userAgent":"node","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:19:47.965Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:19:47.988Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":400,"timestamp":"2025-07-16T14:20:07.015Z","url":"/api/backup/user-data","userAgent":"node","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":400,"timestamp":"2025-07-16T14:20:07.038Z","url":"/api/backup/import","userAgent":"node","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":400,"timestamp":"2025-07-16T14:20:07.048Z","url":"/api/backup/import","userAgent":"node","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:20:07.091Z","url":"/api/backup/status/","userAgent":"node","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:21:37.546Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:21:37.566Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:22:13.987Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:22:14.001Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:22:28.039Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:22:48.514Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:23:05.496Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:28:12.732Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T14:28:12.744Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T15:08:50.099Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T15:08:50.116Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"14ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T17:15:40.843Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T17:15:40.887Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T18:26:06.963Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T18:26:06.994Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T18:26:27.520Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T18:26:29.512Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"9ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T18:57:43.258Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T18:57:43.289Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T18:58:41.859Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-16T18:58:41.881Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T10:57:13.854Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T10:57:13.870Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-17T10:57:14.016Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-17T10:57:14.020Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T10:57:14.025Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T10:57:14.029Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T12:11:48.292Z","url":"/ocr?id=381e5bfc-a307-4757-aefe-0e538b104fa0&vscodeBrowserReqId=1752754308261","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.102.1 Chrome/134.0.6998.205 Electron/35.6.0 Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T12:11:53.752Z","url":"/ocr","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T12:11:53.864Z","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"6ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T15:32:01.396Z","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"8ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T15:43:36.744Z","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T16:02:37.650Z","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T16:04:21.359Z","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T16:04:59.354Z","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T16:04:59.635Z","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T16:13:30.236Z","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"7ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T16:19:20.687Z","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T16:32:00.642Z","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":401,"timestamp":"2025-07-17T16:45:34.352Z","url":"/api/metrics","userAgent":"node","userId":"anonymous"}
{"duration":"4ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T16:45:34.377Z","url":"/api/sessions","userAgent":"node","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T16:45:34.385Z","url":"/api/games/start","userAgent":"node","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T16:45:34.391Z","url":"/api/games/padroes_visuais","userAgent":"node","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T16:45:34.395Z","url":"/api/padroes_visuais","userAgent":"node","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T16:45:34.401Z","url":"/sessions","userAgent":"node","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T16:45:34.406Z","url":"/metrics","userAgent":"node","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T16:45:34.410Z","url":"/","userAgent":"node","userId":"anonymous"}
{"duration":"7ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T16:47:39.280Z","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":401,"timestamp":"2025-07-17T16:56:54.451Z","url":"/api/metrics/sessions","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; pt-BR) WindowsPowerShell/5.1.19041.6093","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:00:16.785Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:00:47.669Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:01:17.660Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:01:47.646Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:02:17.656Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:02:47.657Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:03:27.647Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:04:27.646Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:05:27.647Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:06:27.659Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:07:23.382Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:07:47.652Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:08:17.659Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"8ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:08:47.677Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"7ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:09:27.673Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"6ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:10:27.669Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":401,"timestamp":"2025-07-17T17:10:41.550Z","url":"/api/metrics/sessions","userAgent":"node-fetch","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":401,"timestamp":"2025-07-17T17:10:41.557Z","url":"/api/metrics/dashboard","userAgent":"node-fetch","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":401,"timestamp":"2025-07-17T17:10:41.563Z","url":"/api/dashboard/overview","userAgent":"node-fetch","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":401,"timestamp":"2025-07-17T17:10:41.585Z","url":"/api/metrics/sessions","userAgent":"node-fetch","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:11:27.654Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:11:47.649Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:12:17.667Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:13:19.659Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:14:27.648Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:15:27.659Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:16:27.658Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:17:27.657Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:17:40.861Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:18:10.608Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:18:11.664Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:18:17.651Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:18:41.644Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:18:47.657Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:19:11.652Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:19:27.655Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:19:41.659Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:20:11.653Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:20:27.655Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:21:13.658Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:21:27.658Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:22:05.041Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:22:06.847Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:34:41.662Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:34:59.662Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:35:11.649Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:35:29.649Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:35:41.656Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:35:59.668Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:36:29.653Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:36:59.658Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:37:29.657Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:38:27.646Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:39:27.645Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:40:27.649Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:41:27.647Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:42:11.236Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:42:29.656Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:42:59.648Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T17:44:01.651Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T18:35:16.166Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T18:35:46.647Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T18:36:16.649Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T18:36:46.647Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T18:37:16.656Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T18:37:46.648Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T20:06:46.942Z","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T22:22:00.430Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T22:22:30.425Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T22:23:00.428Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T22:23:30.431Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T22:24:00.417Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T22:24:30.418Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T22:25:00.428Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-17T22:25:30.457Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"6ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-18T15:37:42.554Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-18T15:37:42.574Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-18T15:37:42.736Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-18T15:37:42.741Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-18T15:37:42.747Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-18T15:37:42.750Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-18T15:40:18.662Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-18T15:40:18.689Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-18T15:40:18.699Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-18T15:40:18.714Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-18T15:40:18.722Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-18T15:40:18.737Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-18T16:32:26.237Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-18T16:32:26.248Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-18T16:32:27.150Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-18T16:32:27.153Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":401,"timestamp":"2025-07-18T16:32:44.113Z","url":"/api/metrics/game-sessions?userId=1750545073090","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":401,"timestamp":"2025-07-18T16:32:44.128Z","url":"/api/metrics/game-sessions?userId=1750545073090","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":401,"timestamp":"2025-07-18T16:32:44.190Z","url":"/api/metrics/game-sessions?userId=1750545073090","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"10ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-19T23:16:14.371Z","url":"/residence","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"4ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-19T23:16:14.692Z","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-19T23:45:30.998Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-19T23:46:00.989Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"17ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-20T19:29:56.951Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; pt-BR) WindowsPowerShell/5.1.19041.6093","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-20T19:30:37.264Z","url":"/api/public/digital-signature/status","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; pt-BR) WindowsPowerShell/5.1.19041.6093","userId":"anonymous"}
{"duration":"31ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-21T01:36:47.002Z","url":"/api/status","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; pt-BR) WindowsPowerShell/5.1.19041.6093","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-21T01:37:18.270Z","url":"/api/public/signature/status","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; pt-BR) WindowsPowerShell/5.1.19041.6093","userId":"anonymous"}
{"duration":"7ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-21T20:56:14.845Z","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-21T20:56:44.480Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-21T20:57:14.478Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-21T20:57:44.895Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-21T20:58:14.898Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-21T20:58:44.901Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-21T20:59:14.899Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-21T21:00:16.907Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-21T21:01:21.896Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-21T21:02:21.905Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-21T21:03:21.906Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-21T21:04:21.899Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-21T21:05:21.908Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-21T21:06:21.905Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"43ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-22T21:03:44.112Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-22T21:03:44.186Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-22T21:03:44.960Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-22T21:03:44.968Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-22T21:03:44.982Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-22T21:03:44.990Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"6ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T12:04:29.173Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T12:04:29.204Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T12:04:29.255Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T12:04:29.405Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T12:04:29.409Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T12:04:29.416Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T12:37:15.567Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T12:37:15.584Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T12:37:15.613Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T12:37:15.617Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T12:37:15.622Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T12:37:15.625Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"7ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T12:41:09.760Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T12:41:09.779Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T12:41:09.810Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T12:41:09.816Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T12:41:09.822Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T12:41:09.826Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"6ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T12:44:18.959Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T12:44:18.988Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T12:44:18.992Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T12:44:19.023Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T12:44:19.028Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T12:44:19.041Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"7ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T12:53:16.554Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T12:53:16.574Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T12:53:16.602Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T12:53:16.606Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T12:53:16.612Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T12:53:16.615Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T12:53:25.760Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T12:53:25.763Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T12:53:29.201Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T12:53:29.206Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T12:53:37.513Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T12:53:37.521Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T12:53:37.525Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T12:53:37.529Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T12:55:48.010Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T12:55:48.013Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":429,"timestamp":"2025-07-23T12:56:02.521Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":429,"timestamp":"2025-07-23T12:56:02.524Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"8ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T13:29:07.114Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T13:29:07.134Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T13:29:07.174Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T13:29:07.179Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T13:29:07.186Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T13:29:07.189Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T13:29:14.953Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T13:29:14.957Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T13:38:44.735Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T13:38:44.749Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T13:38:44.755Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T13:38:44.761Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T13:38:44.766Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T13:38:44.771Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T13:40:54.124Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T13:40:54.127Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T13:50:32.158Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T13:50:32.178Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T13:50:32.211Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T13:50:32.215Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T13:50:32.222Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T13:50:32.225Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T13:50:35.245Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T13:50:35.248Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T13:51:49.516Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T13:51:49.521Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T13:51:57.485Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T13:51:57.501Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T13:51:57.505Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T13:51:57.509Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T15:21:58.043Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T15:21:58.063Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T15:21:58.174Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T15:21:58.178Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T15:21:58.183Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T15:21:58.186Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"6ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T17:48:10.895Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T17:48:10.922Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T17:50:02.993Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15","userId":"anonymous"}
{"duration":"9ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T17:55:16.427Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T17:55:16.446Z","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T17:55:16.486Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T17:55:16.490Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T17:55:16.496Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T17:55:16.499Z","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T17:55:22.585Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T17:55:22.591Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":404,"timestamp":"2025-07-23T18:06:05.262Z","url":"/api/auth/admin-login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"6ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T19:00:52.943Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T19:00:52.964Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T19:00:52.995Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T19:00:52.999Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T19:00:53.005Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T19:00:53.010Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T19:00:59.536Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T19:00:59.543Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T19:01:00.436Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T19:01:00.439Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":401,"timestamp":"2025-07-23T19:01:06.560Z","url":"/api/metrics/game-sessions?userId=1750545073090","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":401,"timestamp":"2025-07-23T19:01:06.574Z","url":"/api/metrics/game-sessions?userId=1750545073090","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":401,"timestamp":"2025-07-23T19:01:06.623Z","url":"/api/metrics/game-sessions?userId=1750545073090","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"6ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T20:46:00.422Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T20:46:00.440Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T20:46:00.480Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T20:46:00.484Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T20:46:00.489Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T20:46:00.492Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T20:46:04.336Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T20:46:04.339Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T20:46:13.122Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T20:46:13.126Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T22:05:21.742Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T22:05:21.762Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T22:05:21.794Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T22:05:21.834Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T22:05:21.838Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T22:05:21.843Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"7ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T22:12:42.444Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T22:12:42.462Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T22:12:42.492Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T22:12:42.496Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T22:12:42.502Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T22:12:42.506Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":404,"timestamp":"2025-07-23T22:12:46.925Z","url":"/api/auth/logout","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T22:13:00.579Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T22:13:00.586Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T22:13:00.594Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T22:13:00.599Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T22:13:00.602Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"7ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T22:14:17.191Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T22:14:17.215Z","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T22:14:17.243Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T22:14:17.247Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T22:14:17.252Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T22:14:17.256Z","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T22:17:40.172Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T22:17:40.177Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T22:18:18.312Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T22:18:18.319Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T22:18:18.323Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T22:18:18.328Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T22:18:44.130Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T22:18:44.141Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T22:18:44.148Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T22:18:44.158Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T22:18:44.162Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T22:18:44.166Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T22:20:34.517Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T22:20:34.533Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T22:20:34.555Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T22:20:34.562Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T22:20:34.568Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T22:20:34.572Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"6ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":404,"timestamp":"2025-07-23T22:21:15.335Z","url":"/api/auth/logout","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"4ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T23:59:13.297Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T23:59:13.317Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T23:59:13.330Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T23:59:13.336Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-23T23:59:13.345Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"0ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T23:59:28.340Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-23T23:59:28.344Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"5ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-24T13:07:00.873Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-24T13:07:00.903Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-24T13:07:00.925Z","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-24T13:07:01.057Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-24T13:07:01.061Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-24T13:07:01.065Z","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":404,"timestamp":"2025-07-24T13:08:43.956Z","url":"/api/auth/logout","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-24T14:05:48.708Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-24T14:05:48.728Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-24T14:05:48.740Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-24T14:05:48.746Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-24T14:05:48.752Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-24T14:12:08.345Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-24T14:12:08.371Z","url":"/api/premium/auth/status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-24T14:12:08.381Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-24T14:12:08.396Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-24T14:12:08.404Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-24T14:12:08.411Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-24T14:12:33.265Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"1ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"POST","statusCode":401,"timestamp":"2025-07-24T14:12:33.274Z","url":"/api/metrics/performance","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"6ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-24T21:22:25.317Z","url":"/sw.js","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-24T21:22:54.773Z","url":"/sw.js","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"9ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-25T02:19:17.325Z","url":"/dashboards","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"4ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-25T02:19:19.428Z","url":"/sw.js","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Error Response","method":"GET","statusCode":404,"timestamp":"2025-07-25T02:19:48.684Z","url":"/sw.js","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
