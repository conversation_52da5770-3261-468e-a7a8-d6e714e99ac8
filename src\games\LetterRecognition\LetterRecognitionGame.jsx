/**
 * 📚 LETTER RECOGNITION GAME - Portal Betina V3
 * Layout baseado no padrão ColorMatch
 */

import React, { useState, useEffect, useCallback, useContext, useRef } from 'react';
import { SystemContext } from '../../components/context/SystemContext.jsx';
import { useAccessibilityContext } from '../../components/context/AccessibilityContext';
import { v4 as uuidv4 } from 'uuid';
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic.js';
import { LetterRecognitionConfig } from './LetterRecognitionConfig.js';
import { LetterRecognitionMetrics } from './LetterRecognitionMetrics.js';

// Importa o componente padrão de tela de dificuldade
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';

// 📚 Importar coletores avançados de reconhecimento de letras
import { LetterRecognitionCollectorsHub } from './collectors/index.js';
// 🔄 Importar hook multissensorial
import { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js';
// 🎯 Importar hook orquestrador terapêutico
import { useTherapeuticOrchestrator } from '../../hooks/useTherapeuticOrchestrator.js';

// Importa estilos modulares
import styles from './LetterRecognition.module.css';

// Configurações do jogo
const LETTERS = [
  { id: 'a', letter: 'A', sound: 'A', example: '🐝 Abelha', color: '#FF6B6B' },
  { id: 'b', letter: 'B', sound: 'Bê', example: '⚽ Bola', color: '#4ECDC4' },
  { id: 'c', letter: 'C', sound: 'Cê', example: '🏠 Casa', color: '#45B7D1' },
  { id: 'd', letter: 'D', sound: 'Dê', example: '🎲 Dado', color: '#FFA07A' },
  { id: 'e', letter: 'E', sound: 'É', example: '⭐ Estrela', color: '#98D8C8' },
  { id: 'f', letter: 'F', sound: 'Efe', example: '🌸 Flor', color: '#F7DC6F' },
  { id: 'g', letter: 'G', sound: 'Gê', example: '� Gato', color: '#BB8FCE' },
  { id: 'h', letter: 'H', sound: 'Agá', example: '� Hotel', color: '#85C1E9' }
];

const DIFFICULTIES = {
  EASY: { letters: LETTERS.slice(0, 4), name: 'Fácil' },
  MEDIUM: { letters: LETTERS.slice(0, 6), name: 'Médio' },
  HARD: { letters: LETTERS, name: 'Avançado' }
};

// 🎯 SISTEMA DE ATIVIDADES EXPANDIDO V3
const ACTIVITY_TYPES = {
  LETTER_SELECTION: {
    id: 'letter_selection',
    name: 'Seleção de Letras',
    icon: '🔤',
    description: 'Encontre a letra correta baseada no som e exemplo',
    component: 'LetterSelectionActivity'
  },
  WORD_FORMATION: {
    id: 'word_formation',
    name: 'Formação de Palavras',
    icon: '🔗',
    description: 'Monte palavras usando as letras aprendidas',
    component: 'WordFormationActivity'
  },
  SEQUENCE_RECOGNITION: {
    id: 'sequence_recognition',
    name: 'Reconhecimento de Sequência',
    icon: '📝',
    description: 'Complete sequências alfabéticas',
    component: 'SequenceRecognitionActivity'
  },
  VISUAL_DISCRIMINATION: {
    id: 'visual_discrimination',
    name: 'Discriminação Visual',
    icon: '👁️',
    description: 'Identifique letras com formas similares',
    component: 'VisualDiscriminationActivity'
  }
};

// 🎯 Configuração de palavras para formação - PORTUGUÊS BRASILEIRO
const WORD_BANK = {
  EASY: [
    { word: 'MAMA', emoji: '👩', meaning: 'Mamãe' },
    { word: 'PAPA', emoji: '👨', meaning: 'Papai' },
    { word: 'CASA', emoji: '🏠', meaning: 'Casa' },
    { word: 'BOLA', emoji: '⚽', meaning: 'Bola' },
    { word: 'GATO', emoji: '🐱', meaning: 'Gato' },
    { word: 'PATO', emoji: '🦆', meaning: 'Pato' }
  ],
  MEDIUM: [
    { word: 'FADA', emoji: '🧚', meaning: 'Fada' },
    { word: 'CAFÉ', emoji: '☕', meaning: 'Café' },
    { word: 'FLOR', emoji: '🌸', meaning: 'Flor' },
    { word: 'SAPO', emoji: '🐸', meaning: 'Sapo' },
    { word: 'LUA', emoji: '🌙', meaning: 'Lua' },
    { word: 'SOL', emoji: '☀️', meaning: 'Sol' },
    { word: 'MAR', emoji: '🌊', meaning: 'Mar' }
  ],
  HARD: [
    { word: 'JARDIM', emoji: '🌻', meaning: 'Jardim' },
    { word: 'LIVRO', emoji: '📚', meaning: 'Livro' },
    { word: 'ESCOLA', emoji: '🏫', meaning: 'Escola' },
    { word: 'AMIGO', emoji: '👫', meaning: 'Amigo' },
    { word: 'ALEGRIA', emoji: '😊', meaning: 'Alegria' },
    { word: 'FAMÍLIA', emoji: '👨‍👩‍👧‍👦', meaning: 'Família' },
    { word: 'BORBOLETA', emoji: '🦋', meaning: 'Borboleta' }
  ]
};

// 🎯 BANCO DE SEQUÊNCIAS ALFABÉTICAS
const SEQUENCE_BANK = {
  EASY: [
    { sequence: ['A', 'B'], missing: 'C', options: ['C', 'D', 'E'], description: 'Continue: A, B, ?' },
    { sequence: ['B', 'C'], missing: 'D', options: ['D', 'A', 'F'], description: 'Continue: B, C, ?' },
    { sequence: ['C', 'D'], missing: 'E', options: ['E', 'B', 'G'], description: 'Continue: C, D, ?' },
    { sequence: ['D', 'E'], missing: 'F', options: ['F', 'C', 'H'], description: 'Continue: D, E, ?' },
    { sequence: ['E', 'F'], missing: 'G', options: ['G', 'D', 'I'], description: 'Continue: E, F, ?' },
    { sequence: ['F', 'G'], missing: 'H', options: ['H', 'E', 'J'], description: 'Continue: F, G, ?' }
  ],
  MEDIUM: [
    { sequence: ['A', 'B', 'C'], missing: 'D', options: ['D', 'F', 'E'], description: 'Continue: A, B, C, ?' },
    { sequence: ['B', 'C', 'D'], missing: 'E', options: ['E', 'G', 'F'], description: 'Continue: B, C, D, ?' },
    { sequence: ['G', 'H', 'I'], missing: 'J', options: ['J', 'L', 'K'], description: 'Continue: G, H, I, ?' },
    { sequence: ['M', 'N', 'O'], missing: 'P', options: ['P', 'R', 'Q'], description: 'Continue: M, N, O, ?' },
    { sequence: ['P', 'Q', 'R'], missing: 'S', options: ['S', 'U', 'T'], description: 'Continue: P, Q, R, ?' },
    { sequence: ['S', 'T', 'U'], missing: 'V', options: ['V', 'X', 'W'], description: 'Continue: S, T, U, ?' }
  ],
  HARD: [
    { sequence: ['A', 'C'], missing: 'E', options: ['E', 'D', 'F'], description: 'Continue saltando: A, C, ?' },
    { sequence: ['B', 'D'], missing: 'F', options: ['F', 'E', 'G'], description: 'Continue saltando: B, D, ?' },
    { sequence: ['Z', 'Y'], missing: 'X', options: ['X', 'W', 'V'], description: 'Continue invertido: Z, Y, ?' },
    { sequence: ['Y', 'X'], missing: 'W', options: ['W', 'V', 'U'], description: 'Continue invertido: Y, X, ?' },
    { sequence: ['A', 'C', 'E'], missing: 'G', options: ['G', 'F', 'H'], description: 'Continue saltando: A, C, E, ?' },
    { sequence: ['B', 'D', 'F'], missing: 'H', options: ['H', 'G', 'I'], description: 'Continue saltando: B, D, F, ?' }
  ]
};

// 🎯 Pares para discriminação fonética
// Removido: PHONETIC_PAIRS agora vem do config

// 🎯 Grupos para discriminação visual
// Removido: VISUAL_GROUPS agora vem do config

function LetterRecognitionGame({ onBack }) {
  const { user, ttsEnabled = true } = useContext(SystemContext);
  const metricsRef = useRef(new LetterRecognitionMetrics());
  const sessionIdRef = useRef(uuidv4());

  // 📚 Inicializar coletores avançados de reconhecimento de letras
  const [collectorsHub] = useState(() => new LetterRecognitionCollectorsHub())

  // 🔄 Hook multissensorial integrado
  const {
    initializeSession: initMultisensory,
    recordInteraction: recordMultisensoryInteraction,
    finalizeSession: finalizeMultisensory,
    updateData: updateMultisensoryData,
    multisensoryData,
    isInitialized: multisensoryInitialized
  } = useMultisensoryIntegration(sessionIdRef.current, {
    gameType: 'letter_recognition',
    collectorsHub,
    sensorTypes: {
      visual: true,
      haptic: true,
      tts: ttsEnabled,
      gestural: true,
      biometric: true
    },
    adaptiveMode: true,
    autoUpdate: true,
    enablePatternAnalysis: true,
    logLevel: 'info',
    enableAdvancedMetrics: true,
    enableRealTimeAnalysis: true,
    enableNeurodivergenceSupport: true,
    learningStyle: user?.profile?.learningStyle || 'visual'
  });

  // 🎯 Hook orquestrador terapêutico integrado
  const therapeuticOrchestrator = useTherapeuticOrchestrator({ 
    userId: user?.id || 'anonymous'
  });

  // ♿ Contexto de acessibilidade
  const { settings } = useAccessibilityContext();

  // =====================================================
  // 🔊 SISTEMA DE TEXT-TO-SPEECH (TTS) PADRONIZADO
  // =====================================================
  
  // Estado do TTS com persistência
  const [ttsActive, setTtsActive] = useState(() => {
    const saved = localStorage.getItem('letterRecognition_ttsActive');
    return saved !== null ? JSON.parse(saved) : true;
  });
  
  // Função para alternar TTS
  const toggleTTS = useCallback(() => {
    setTtsActive(prev => {
      const newState = !prev;
      localStorage.setItem('letterRecognition_ttsActive', JSON.stringify(newState));
      
      // Cancelar qualquer fala em andamento se desabilitando
      if (!newState && 'speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
      
      return newState;
    });
  }, []);
  
  // Função TTS padronizada
  const speak = useCallback((text, options = {}) => {
    // Verificar se TTS está ativo
    if (!ttsActive || !('speechSynthesis' in window)) {
      return;
    }
    
    // Cancelar qualquer fala anterior
    window.speechSynthesis.cancel();
    
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'pt-BR';
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;
    
    window.speechSynthesis.speak(utterance);
  }, [ttsActive]);

  // Integração com o sistema unificado Portal Betina V3
  const {
    startUnifiedSession,
    recordInteraction,
    endUnifiedSession,
    updateMetrics,
    portalReady,
    sessionId,
    isSessionActive,
    gameState: unifiedGameState,
    sessionMetrics
  } = useUnifiedGameLogic('letter_recognition');

  // Conectar métricas ao backend após inicialização
  useEffect(() => {
    if (metricsRef.current && recordInteraction && updateMetrics) {
      metricsRef.current.connectToBackend({
        recordInteraction,
        updateMetrics
      });
    }
  }, [recordInteraction, updateMetrics]);

  const [cognitiveAnalysisVisible, setCognitiveAnalysisVisible] = useState(false)
  const [analysisResults, setAnalysisResults] = useState(null)
  const [attemptCount, setAttemptCount] = useState(0)

  // 🎯 ESTADO EXPANDIDO COM SISTEMA DE ATIVIDADES V3
  const [gameState, setGameState] = useState({
    status: 'menu',
    score: 0,
    round: 1,
    targetLetter: null,
    selectedLetter: null,
    showFeedback: false,
    accuracy: 100,
    totalRounds: 10,
    difficulty: 'EASY',
    availableLetters: DIFFICULTIES.EASY.letters,
    allLetters: DIFFICULTIES.EASY.letters,
    roundStartTime: null,
    
    // 🎯 Sistema de atividades
    currentActivity: ACTIVITY_TYPES.LETTER_SELECTION.id,
    activityCycle: [
      ACTIVITY_TYPES.LETTER_SELECTION.id,
      ACTIVITY_TYPES.WORD_FORMATION.id,
      ACTIVITY_TYPES.SEQUENCE_RECOGNITION.id,
      ACTIVITY_TYPES.VISUAL_DISCRIMINATION.id
    ],
    activityIndex: 0,
    roundsPerActivity: 4, // Será calculado dinamicamente (4-7 rodadas)
    activityRoundCount: 0, // Iniciar com 0 rodadas
    activitiesCompleted: [], // Lista de atividades já completadas
    
    // 🎯 Dados específicos de atividades
    activityData: {
      wordFormation: {
        currentWord: null,
        placedLetters: [],
        availableLetters: []
      },
      sequenceRecognition: {
        currentSequence: null,
        userProgress: []
      },
      visualDiscrimination: {
        currentGroup: null,
        foundTargets: 0,
        totalTargets: 0
      }
    },
    
    // 🎯 Sistema de prevenção de repetição
    letterHistory: [], // Histórico das últimas 3 letras usadas
    wordHistory: [], // Histórico das últimas 3 palavras usadas
    sequenceHistory: [], // Histórico das últimas 3 sequências usadas
    
    // 🎯 Métricas comportamentais avançadas
    behavioralMetrics: {
      activityPreferences: {},
      responsePatterns: [],
      adaptiveAdjustments: 0,
      engagementLevel: 1.0
    }
  });
  
  const [showStartScreen, setShowStartScreen] = useState(true);

  // 🎯 SISTEMA DE RODADAS ADAPTATIVO (4-7 rodadas) - MELHORADO
  const calculateOptimalRounds = useCallback((activityType, difficulty, userPerformance = 0.7) => {
    // Base de rodadas por atividade e dificuldade
    const roundsByDifficulty = {
      'EASY': {
        'letter_selection': 4,
        'word_formation': 4,
        'sequence_recognition': 4,
        'visual_discrimination': 5
      },
      'MEDIUM': {
        'letter_selection': 5,
        'word_formation': 6,
        'sequence_recognition': 5,
        'visual_discrimination': 6
      },
      'HARD': {
        'letter_selection': 6,
        'word_formation': 7,
        'sequence_recognition': 6,
        'visual_discrimination': 7
      }
    };
    
    // Obter rodadas base para dificuldade e atividade
    const base = roundsByDifficulty[difficulty]?.[activityType] || 4;
    
    // Ajuste baseado na performance do usuário
    const performanceAdjustment = userPerformance < 0.5 ? 1 : userPerformance > 0.8 ? -1 : 0;
    
    // Garantir mínimo de 4 e máximo de 7
    return Math.max(4, Math.min(7, base + performanceAdjustment));
  }, []);

  // 🎯 SISTEMA DE ROTAÇÃO DE ATIVIDADES
  const rotateActivity = useCallback(() => {
    setGameState(prev => {
      const nextActivityIndex = (prev.activityIndex + 1) % prev.activityCycle.length;
      const nextActivity = prev.activityCycle[nextActivityIndex];
      
      // 🔊 Anunciar nova atividade
      const activityName = ACTIVITY_TYPES[nextActivity.toUpperCase()]?.name || 'Nova Atividade';
      speak(`Nova atividade: ${activityName}`, { pitch: 1.2, rate: 0.8 });
      
      return {
        ...prev,
        currentActivity: nextActivity,
        activityIndex: nextActivityIndex,
        activityRoundCount: 0,
        selectedLetter: null,
        showFeedback: false
      };
    });
  }, [speak]);

  // =====================================================
  // 🔊 SISTEMA DE TTS ESPECÍFICO POR ATIVIDADE - ACESSIBILIDADE
  // =====================================================

  // 🔊 TTS específico para cada atividade com instruções claras e direcionadas
  const provideActivityInstructions = useCallback((activity, activityData) => {
    console.log('🔊 Providing TTS instructions for activity:', activity);

    switch (activity) {
      case 'letter_selection':
        if (activityData?.targetLetter) {
          const letter = activityData.targetLetter;
          const instruction = `Encontre a letra ${letter.letter}. ${letter.sound} de ${letter.example.split(' ')[1]}. Clique na letra ${letter.letter}.`;
          speak(instruction, { rate: 0.7, pitch: 1.1 });
        }
        break;

      case 'word_formation':
        if (activityData?.currentWord) {
          const word = activityData.currentWord;
          const instruction = `Monte a palavra ${word.meaning}. ${word.emoji} ${word.meaning}. Use as letras para formar ${word.word}.`;
          speak(instruction, { rate: 0.7, pitch: 1.1 });
        }
        break;

      case 'sequence_recognition':
        if (activityData?.currentSequence) {
          const sequence = activityData.currentSequence;
          const instruction = `Complete a sequência alfabética. ${sequence.sequence.join(', ')}. Qual letra vem depois?`;
          speak(instruction, { rate: 0.7, pitch: 1.1 });
        }
        break;

      case 'visual_discrimination':
        if (activityData?.currentGroup) {
          const group = activityData.currentGroup;
          const instruction = `${group.description}. Clique em todas as letras ${group.target} que você encontrar.`;
          speak(instruction, { rate: 0.7, pitch: 1.1 });
        }
        break;

      default:
        speak("Escolha uma atividade para começar.", { rate: 0.8 });
    }
  }, [speak]);

  // 🎯 Gerador de conteúdo específico por atividade - MELHORADO COM TTS
  const generateActivityContent = useCallback((activity, difficulty) => {
    const difficultyConfig = DIFFICULTIES[difficulty] || DIFFICULTIES.EASY;

    switch (activity) {
      case 'word_formation':
        // Sistema de prevenção de repetição usando histórico
        console.log('📝 Gerando word_formation com histórico:', gameState.wordHistory);
        
        const wordBank = WORD_BANK[difficulty] || WORD_BANK.EASY;
        let selectedWord;
        
        // Filtrar palavras que não estão no histórico
        const filteredWords = wordBank.filter(word => 
          !gameState.wordHistory.includes(word.word)
        );
        
        // Se não há palavras disponíveis (todas foram usadas recentemente), use qualquer uma exceto a última
        if (filteredWords.length === 0) {
          const notLastWord = wordBank.filter(word => 
            word.word !== gameState.wordHistory[gameState.wordHistory.length - 1]
          );
          selectedWord = notLastWord[Math.floor(Math.random() * notLastWord.length)];
        } else {
          selectedWord = filteredWords[Math.floor(Math.random() * filteredWords.length)];
        }
        
        console.log('🎯 Palavra selecionada:', selectedWord.word, 'Histórico:', gameState.wordHistory);

        const wordLetters = selectedWord.word.split('');
        
        // 🎯 Sistema de cartas baseado na dificuldade para manter estrutura coesa
        let targetCardCount, distractorCount;
        switch (difficulty) {
          case 'EASY':
            targetCardCount = 4;
            // Para palavras de 4 letras = 4 cartas exatas
            // Para palavras de 3 letras = 1 distrator
            distractorCount = Math.max(0, targetCardCount - wordLetters.length);
            break;
          case 'MEDIUM':
            targetCardCount = 6;
            // Para palavras de 3-4 letras = 2-3 distratores
            distractorCount = Math.max(0, targetCardCount - wordLetters.length);
            break;
          case 'HARD':
            targetCardCount = 8;
            // Para palavras de 5-9 letras = 3-1 distratores ou nenhum
            distractorCount = Math.max(0, targetCardCount - wordLetters.length);
            break;
          default:
            targetCardCount = 4;
            distractorCount = Math.max(0, targetCardCount - wordLetters.length);
        }
        
        // Gerar distratores apenas se necessário
        const distractors = distractorCount > 0 ? generateDistracters(wordLetters, distractorCount) : [];
        
        // Garantir que temos exatamente o número de cartas desejado
        const allLetters = [...wordLetters, ...distractors];
        const availableLetters = shuffleArray(allLetters.slice(0, targetCardCount));

        console.log('📝 Generated word formation content:', {
          selectedWord: selectedWord.word,
          wordLength: wordLetters.length,
          difficulty,
          targetCardCount,
          distractorCount,
          totalCards: availableLetters.length,
          wordLetters,
          distractors
        });

        const wordContent = {
          currentWord: selectedWord,
          availableLetters,
          placedLetters: new Array(selectedWord.word.length).fill(null)
        };

        // Fornecer instruções TTS específicas
        setTimeout(() => provideActivityInstructions(activity, wordContent), 500);

        return wordContent;
        
      case 'sequence_recognition':
        // Sistema de prevenção de repetição usando histórico
        console.log('📝 Gerando sequence_recognition com histórico:', gameState.sequenceHistory);
        
        const sequenceBank = SEQUENCE_BANK[difficulty] || SEQUENCE_BANK.EASY;
        let selectedSequence;
        
        // Filtrar sequências que não estão no histórico
        const filteredSequences = sequenceBank.filter(seq => 
          !gameState.sequenceHistory.includes(seq.description)
        );
        
        // Se não há sequências disponíveis (todas foram usadas recentemente), use qualquer uma exceto a última
        if (filteredSequences.length === 0) {
          const notLastSequence = sequenceBank.filter(seq => 
            seq.description !== gameState.sequenceHistory[gameState.sequenceHistory.length - 1]
          );
          selectedSequence = notLastSequence[Math.floor(Math.random() * notLastSequence.length)];
        } else {
          selectedSequence = filteredSequences[Math.floor(Math.random() * filteredSequences.length)];
        }
        
        console.log('🎯 Sequência selecionada:', selectedSequence.description, 'Histórico:', gameState.sequenceHistory);

        // Embaralhar as opções para variabilidade
        const shuffledOptions = shuffleArray([...selectedSequence.options]);

        console.log('📝 Generated sequence recognition content:', {
          selectedSequence: selectedSequence.description,
          sequence: selectedSequence.sequence,
          missing: selectedSequence.missing,
          difficulty,
          shuffledOptions
        });

        const sequenceContent = {
          currentSequence: {
            ...selectedSequence,
            options: shuffledOptions
          }
        };

        // Fornecer instruções TTS específicas
        setTimeout(() => provideActivityInstructions(activity, sequenceContent), 500);

        return sequenceContent;
        
      case 'visual_discrimination':
        // Sistema de prevenção de repetição usando histórico
        console.log('📝 Gerando visual_discrimination com histórico:', gameState.visualHistory || []);
        
        // Grupos visuais melhorados por dificuldade
        const visualGroupsByDifficulty = {
          EASY: [
            { target: 'A', items: ['A', 'H', 'V', 'A', 'N', 'A'], description: 'Encontre todas as letras A' },
            { target: 'O', items: ['O', 'C', 'U', 'O', 'Q', 'O'], description: 'Encontre todas as letras O' },
            { target: 'I', items: ['I', 'L', 'T', 'I', 'J', 'I'], description: 'Encontre todas as letras I' },
            { target: 'E', items: ['E', 'F', 'L', 'E', 'T', 'E'], description: 'Encontre todas as letras E' }
          ],
          MEDIUM: [
            { target: 'B', items: ['B', 'P', 'R', 'B', 'D', 'B', 'Q', 'R'], description: 'Encontre todas as letras B' },
            { target: 'P', items: ['P', 'B', 'R', 'P', 'F', 'P', 'D', 'B'], description: 'Encontre todas as letras P' },
            { target: 'D', items: ['D', 'B', 'P', 'D', 'Q', 'D', 'O', 'C'], description: 'Encontre todas as letras D' },
            { target: 'G', items: ['G', 'C', 'O', 'G', 'Q', 'G', 'D', 'B'], description: 'Encontre todas as letras G' },
            { target: 'M', items: ['M', 'N', 'W', 'M', 'H', 'M', 'A', 'V'], description: 'Encontre todas as letras M' }
          ],
          HARD: [
            { target: 'Q', items: ['Q', 'O', 'G', 'Q', 'C', 'Q', 'D', 'P', 'B', 'G'], description: 'Encontre todas as letras Q' },
            { target: 'R', items: ['R', 'P', 'B', 'R', 'F', 'R', 'K', 'D', 'H', 'B'], description: 'Encontre todas as letras R' },
            { target: 'K', items: ['K', 'H', 'X', 'K', 'R', 'K', 'F', 'Y', 'A', 'V'], description: 'Encontre todas as letras K' },
            { target: 'W', items: ['W', 'M', 'V', 'W', 'N', 'W', 'A', 'H', 'Y', 'X'], description: 'Encontre todas as letras W' },
            { target: 'Y', items: ['Y', 'V', 'T', 'Y', 'X', 'Y', 'K', 'A', 'F', 'I'], description: 'Encontre todas as letras Y' }
          ]
        };

        const availableGroups = visualGroupsByDifficulty[difficulty] || visualGroupsByDifficulty.EASY;
        let selectedGroup;

        // Filtrar grupos que não estão no histórico (se existir)
        const visualHistory = gameState.visualHistory || [];
        const filteredGroups = availableGroups.filter(group => 
          !visualHistory.includes(group.target)
        );

        // Se não há grupos disponíveis, use qualquer um exceto o último
        if (filteredGroups.length === 0) {
          const notLastGroup = availableGroups.filter(group => 
            group.target !== visualHistory[visualHistory.length - 1]
          );
          selectedGroup = notLastGroup[Math.floor(Math.random() * notLastGroup.length)];
        } else {
          selectedGroup = filteredGroups[Math.floor(Math.random() * filteredGroups.length)];
        }

        console.log('🎯 Grupo visual selecionado:', selectedGroup.target, 'Histórico:', visualHistory);

        // Embaralhar os itens para variabilidade
        const shuffledItems = shuffleArray([...selectedGroup.items]);

        // Contar quantas letras alvo existem no array
        const actualTargetCount = shuffledItems.filter(item => item === selectedGroup.target).length;

        console.log('📝 Generated visual discrimination content:', {
          selectedGroup: selectedGroup.description,
          target: selectedGroup.target,
          actualTargetCount,
          difficulty,
          shuffledItems
        });

        const visualContent = {
          currentGroup: {
            ...selectedGroup,
            items: shuffledItems
          },
          foundTargets: 0,
          totalTargets: actualTargetCount, // Usar contagem real
          selectedItems: [] // Array para rastrear itens selecionados
        };

        // Fornecer instruções TTS específicas
        setTimeout(() => provideActivityInstructions(activity, visualContent), 500);

        return visualContent;
        
      default: // letter_selection
        // Sistema de prevenção de repetição usando histórico
        console.log('📝 Gerando letter_selection com histórico:', gameState.letterHistory);
        
        let newTargetLetter;
        const filteredLetters = difficultyConfig.letters.filter(letter => 
          !gameState.letterHistory.includes(letter)
        );
        
        // Se não há letras disponíveis (todas foram usadas recentemente), use qualquer uma exceto a última
        if (filteredLetters.length === 0) {
          const notLastLetter = difficultyConfig.letters.filter(letter => 
            letter !== gameState.letterHistory[gameState.letterHistory.length - 1]
          );
          newTargetLetter = notLastLetter[Math.floor(Math.random() * notLastLetter.length)];
        } else {
          newTargetLetter = filteredLetters[Math.floor(Math.random() * filteredLetters.length)];
        }
        
        console.log('🎯 Letra selecionada:', newTargetLetter, 'Histórico:', gameState.letterHistory);

        // Criar opções com a letra alvo + 2 distrativas
        const letterOptions = [newTargetLetter];
        const otherAvailable = difficultyConfig.letters.filter(l => l !== newTargetLetter);
        letterOptions.push(...shuffleArray(otherAvailable).slice(0, 2));

        const letterContent = {
          targetLetter: newTargetLetter,
          options: shuffleArray(letterOptions)
        };

        // Fornecer instruções TTS específicas
        setTimeout(() => provideActivityInstructions(activity, letterContent), 500);

        return letterContent;
    }
  }, []);

  // 🎯 Função auxiliar para embaralhar arrays
  function shuffleArray(array) {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  }

  // 🎯 Função auxiliar para gerar distradores
  function generateDistracters(targetLetters, count) {
    const allLetters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');
    const available = allLetters.filter(letter => !targetLetters.includes(letter));
    return shuffleArray(available).slice(0, count);
  }

  // 🎮 FUNÇÃO SIMPLIFICADA PARA MUDAR ATIVIDADE
  const changeActivity = useCallback((activityId) => {
    console.log('🎯 Changing activity to:', activityId);

    setGameState(prev => {
      console.log('🔄 Current state before change:', {
        currentActivity: prev.currentActivity,
        targetActivity: activityId,
        currentActivityData: prev.activityData
      });

      // Usar a função generateActivityContent para gerar dados corretos
      const activityContent = generateActivityContent(activityId, prev.difficulty);
      console.log('📝 Generated activity content:', activityContent);

      let newActivityData = { ...prev.activityData };
      
      // Atualizar dados específicos da atividade usando generateActivityContent
      switch (activityId) {
        case 'word_formation':
          newActivityData.wordFormation = activityContent;
          break;
        case 'sequence_recognition':
          newActivityData.sequenceRecognition = activityContent;
          break;
        case 'visual_discrimination':
          newActivityData.visualDiscrimination = activityContent;
          break;
        default: // letter_selection
          return {
            ...prev,
            currentActivity: activityId,
            activityRoundCount: 0,
            selectedLetter: null,
            showFeedback: false,
            roundStartTime: Date.now(),
            targetLetter: activityContent.targetLetter,
            availableLetters: activityContent.options,
            activityData: newActivityData
          };
      }
      
      const newState = {
        ...prev,
        currentActivity: activityId,
        activityRoundCount: 0,
        selectedLetter: null,
        showFeedback: false,
        roundStartTime: Date.now(),
        activityData: newActivityData
      };
      
      console.log('✅ New state after change:', { 
        currentActivity: newState.currentActivity,
        activityData: newState.activityData
      });
      
      return newState;
    });
  }, [generateActivityContent]);

  // 🎯 Função auxiliar para embaralhar arrays
  function shuffleArray(array) {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  }

  // 🎯 Função auxiliar para gerar distradores
  function generateDistracters(targetLetters, count) {
    const allLetters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');
    const available = allLetters.filter(letter => !targetLetters.includes(letter));
    return shuffleArray(available).slice(0, count);
  }

  // 🎯 NOVA FUNÇÃO DE GERAÇÃO DE RODADAS COM SISTEMA DE ATIVIDADES
  const generateNewRound = useCallback(() => {
    console.log('🎯 Gerando nova rodada...', { 
      currentActivity: gameState.currentActivity,
      activityRoundCount: gameState.activityRoundCount,
      round: gameState.round 
    });
    
    setGameState(prev => {
      // 🚫 REMOVIDO: Rotação automática de atividades - agora é controlada pelo usuário
      // O usuário precisa completar pelo menos 4 rodadas antes de poder trocar atividade
      
      console.log('🔄 Incrementando rodada da atividade:', { 
        currentActivity: prev.currentActivity,
        activityRoundCount: prev.activityRoundCount + 1,
        roundsPerActivity: prev.roundsPerActivity 
      });
      
      let newState = { 
        ...prev, 
        activityRoundCount: prev.activityRoundCount + 1 // Incrementar contador de rodadas
      };
      
      // 🎯 Recalcular número ótimo de rodadas baseado na performance
      if (prev.activityRoundCount === 0) {
        const userPerformance = prev.accuracy / 100;
        newState.roundsPerActivity = calculateOptimalRounds(prev.currentActivity, prev.difficulty, userPerformance);
        console.log('🎯 Rodadas calculadas para', prev.currentActivity, ':', newState.roundsPerActivity);
      }
      
      // Gerar conteúdo específico da atividade (sem rotação automática)
      const activityContent = generateActivityContent(newState.currentActivity, newState.difficulty);
      
      console.log('📝 Conteúdo gerado para atividade:', { 
        activity: newState.currentActivity, 
        content: activityContent 
      });
      
      // Atualizar estado baseado na atividade atual
      switch (newState.currentActivity) {
        case 'word_formation':
          newState.activityData.wordFormation = {
            currentWord: activityContent.currentWord,
            placedLetters: activityContent.placedLetters,
            availableLetters: activityContent.availableLetters
          };
          break;
          
        case 'sequence_recognition':
          newState.activityData.sequenceRecognition = {
            currentSequence: activityContent.currentSequence,
            userProgress: []
          };
          break;
          
        case 'visual_discrimination':
          newState.activityData.visualDiscrimination = {
            currentGroup: activityContent.currentGroup,
            foundTargets: activityContent.foundTargets,
            totalTargets: activityContent.totalTargets
          };
          break;
          
        default: // letter_selection
          newState.targetLetter = activityContent.targetLetter;
          newState.availableLetters = activityContent.options ||
            shuffleArray([...DIFFICULTIES[newState.difficulty].letters]).slice(0, 3);
      }
      
      return {
        ...newState,
        selectedLetter: null,
        showFeedback: false,
        roundStartTime: Date.now()
      };
    });
  }, [generateActivityContent, speak]); // Removidas dependências do gameState que causavam loop

  // Inicializar jogo com letra aleatória - CORRIGIDO
  useEffect(() => {
    if (!showStartScreen) {
      // Hub de coletores será usado apenas para coletar dados
      generateNewRound();
    }
    
    // Cleanup TTS ao desmontar
    return () => {
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, [showStartScreen]); // Removido generateNewRound para evitar loop infinito
  // 🎯 HANDLER PRINCIPAL ATUALIZADO PARA SISTEMA DE ATIVIDADES
  const handleLetterSelect = async (letterId) => {
    if (gameState.selectedLetter) return;

    setGameState(prev => ({ ...prev, selectedLetter: letterId }));
    
    const isCorrect = letterId === gameState.targetLetter.id;
    const reactionTime = gameState.roundStartTime ? (Date.now() - gameState.roundStartTime) : 0;

    // 🔊 Pronunciar a letra selecionada primeiro
    speakLetter(letterId);

    // 📚 Coletar dados com sistema V3 expandido
    try {
      if (!collectorsHub.sessionActive && sessionIdRef.current) {
        await collectorsHub.initializeSession(sessionIdRef.current, {
          gameMode: 'letter_recognition_v3',
          activityType: gameState.currentActivity,
          difficulty: gameState.difficulty
        });
      }

      // 📚 Registrar com coletores V3 - dados completos
      await collectorsHub.collectComprehensiveData({
        // Dados básicos
        targetLetter: gameState.targetLetter,
        selectedLetter: LETTERS.find(l => l.id === letterId),
        isCorrect,
        responseTime: reactionTime,
        sessionId: sessionIdRef.current,
        userId: user?.id || 'anonymous',
        
        // Dados V3 específicos
        activityType: gameState.currentActivity,
        activityRound: gameState.activityRoundCount || 0,
        activitiesCompleted: gameState.activitiesCompleted || [],
        
        // Dados da atividade atual
        activityData: gameState.activityData,
        
        // Métricas comportamentais V3
        behavioralMetrics: {
          ...gameState.behavioralMetrics,
          reactionTime: [...(gameState.behavioralMetrics.reactionTime || []), reactionTime],
          accuracy: [...(gameState.behavioralMetrics.accuracy || []), isCorrect ? 1 : 0],
          attentionSpan: Date.now() - gameState.roundStartTime,
          currentLevel: gameState.currentLevel,
          totalAttempts: gameState.round,
          activitySpecific: {
            ...gameState.behavioralMetrics.activitySpecific,
            // Adicionar métricas específicas baseadas na atividade atual
            ...(gameState.currentActivity === 'word_formation' && {
              wordBuildingTime: reactionTime,
              letterSequenceAccuracy: gameState.activityData?.wordAccuracy || 0
            }),
            ...(gameState.currentActivity === 'visual_discrimination' && {
              visualScanTime: reactionTime,
              targetDetectionRate: gameState.activityData?.detectionRate || 0
            }),
            ...(gameState.currentActivity === 'sequence_recognition' && {
              sequenceCompletionTime: reactionTime,
              patternRecognition: isCorrect ? 1 : 0
            })
          }
        },
        
        // Comportamento do jogador
        playerBehavior: {
          hesitationTime: reactionTime > 3000 ? reactionTime - 3000 : 0,
          confidence: reactionTime < 1000 ? 'high' : reactionTime < 3000 ? 'medium' : 'low',
          attemptPattern: gameState.round % 5 === 0 ? 'milestone' : 'regular'
        }
      });
    } catch (error) {
      console.warn('📚 LetterRecognition V3: Erro ao coletar dados:', error);
    }
    
    // 🔄 Registrar interação multissensorial V3
    if (multisensoryInitialized) {
      await recordMultisensoryInteraction('game_interaction', {
        interactionType: 'user_action',
        gameSpecificData: {
          targetLetter: gameState.targetLetter.id,
          selectedLetter: letterId,
          isCorrect,
          round: gameState.round,
          score: gameState.score,
          responseTime: reactionTime,
          activityType: gameState.currentActivity,
          activityRound: gameState.activityRoundCount || 0
        },
        multisensoryProcessing: {
          linguisticProcessing: { 
            letterRecognition: 0.8, 
            phonologicalAwareness: 0.7, 
            linguisticMemory: 0.7,
            activitySpecificSkill: calculateActivitySpecificSkill(gameState.currentActivity, isCorrect)
          },
          cognitiveProcessing: { 
            accuracy: isCorrect ? 1.0 : 0.0, 
            processingSpeed: reactionTime < 3000 ? 0.8 : 0.5, 
            adaptability: 0.7,
            activityComplexity: getActivityComplexity(gameState.currentActivity)
          },
          behavioralProcessing: { 
            interactionCount: gameState.round, 
            averageResponseTime: reactionTime, 
            consistency: 0.8,
            activityProgression: (gameState.activitiesCompleted?.length || 0) / 6
          }
        }
      });
    }

    // 🎯 Usar handlers universais V3
    if (isCorrect) {
      handleCorrectAnswer();
    } else {
      handleIncorrectAnswer();
    }
  };

  const startGame = useCallback(async (selectedDifficulty) => {
    console.log('🚀 STARTING GAME WITH DIFFICULTY:', selectedDifficulty);

    try {
      // PRIMEIRO: Esconder a tela inicial IMEDIATAMENTE
      setShowStartScreen(false);
      
      const difficultyKey = selectedDifficulty.toUpperCase();
      const difficultyConfig = DIFFICULTIES[difficultyKey];

      console.log('🎯 Difficulty config:', difficultyConfig);

      // Inicializar dados da atividade atual
      const initialActivityContent = generateActivityContent('letter_selection', difficultyKey);

      console.log('📝 Initial activity content:', initialActivityContent);

      setGameState(prev => ({
        ...prev,
        status: 'playing',
        difficulty: difficultyKey,
        availableLetters: difficultyConfig.letters,
        allLetters: difficultyConfig.letters,
        score: 0,
        round: 1,
        accuracy: 100,
        targetLetter: initialActivityContent.targetLetter,
        roundStartTime: Date.now(),
        currentActivity: 'letter_selection',
        // 🎯 Configurar rodadas baseadas na dificuldade
        roundsPerActivity: calculateOptimalRounds('letter_selection', difficultyKey, 1.0), // Performance inicial ótima
        activityRoundCount: 0, // Resetar contador de rodadas
        // Inicializar dados de todas as atividades
        activityData: {
          letterSelection: initialActivityContent,
          wordFormation: generateActivityContent('word_formation', difficultyKey),
          sequenceRecognition: generateActivityContent('sequence_recognition', difficultyKey),
          visualDiscrimination: generateActivityContent('visual_discrimination', difficultyKey)
        }
      }));

      console.log('✅ Game started successfully!');

      // 📚 Inicializar coletores de reconhecimento de letras
      try {
        if (collectorsHub && typeof collectorsHub.initializeSession === 'function') {
          await collectorsHub.initializeSession(sessionIdRef.current, {
            difficulty: difficultyKey,
            availableLetters: difficultyConfig.letters.map(l => l.id),
            gameMode: 'letter_recognition'
          });
          console.log('📚 LetterRecognition: Coletores inicializados com sucesso');
        } else {
          console.log('📚 LetterRecognition: Coletores já ativos');
        }
      } catch (error) {
        console.warn('⚠️ LetterRecognition: Erro ao inicializar coletores:', error);
      }

      // 🔄 Inicializar integração multissensorial
      try {
        await initMultisensory(sessionIdRef.current, {
          difficulty: difficultyKey,
          gameMode: 'letter_recognition',
          availableLetters: difficultyConfig.letters.map(l => l.id),
          userId: user?.id || 'anonymous'
        });
      } catch (error) {
        console.warn('⚠️ Erro ao inicializar sessão multissensorial:', error);
      }
      
      // Iniciar sessão unificada Portal Betina V3
      if (portalReady) {
        startUnifiedSession({
          gameType: 'LetterRecognition',
          difficulty: difficultyKey,
          userId: user?.id || 'anonymous'
        });
      }
      
      // TTS automático ao iniciar
      setTimeout(() => {
        speak(`Bem-vindo ao Reconhecimento de Letras! Dificuldade: ${difficultyConfig.name}. Ouça o som da letra e encontre a letra correta. Vamos começar!`, {
          rate: 0.8
        });
      }, 1000);

    } catch (error) {
      console.error('❌ Error starting game:', error);
    }
  }, [initMultisensory, startUnifiedSession, portalReady, user, speak, generateActivityContent, collectorsHub]);
  const restartGame = () => {
    setGameState({
      status: 'menu',
      score: 0,
      round: 1,
      targetLetter: null,
      selectedLetter: null,
      showFeedback: false,
      accuracy: 100,
      totalRounds: 10,
      difficulty: 'EASY',
      availableLetters: DIFFICULTIES.EASY.letters,
      allLetters: DIFFICULTIES.EASY.letters,
      roundStartTime: null,
      
      // 🎯 Sistema de atividades
      currentActivity: ACTIVITY_TYPES.LETTER_SELECTION.id,
      activityCycle: [
        ACTIVITY_TYPES.LETTER_SELECTION.id,
        ACTIVITY_TYPES.WORD_FORMATION.id,
        ACTIVITY_TYPES.SEQUENCE_RECOGNITION.id,
        ACTIVITY_TYPES.VISUAL_DISCRIMINATION.id
      ],
      activityIndex: 0,
      roundsPerActivity: 4, // Será recalculado dinamicamente (4-7 rodadas)
      activityRoundCount: 0, // Iniciar com 0 rodadas
      activitiesCompleted: [],
      
      // 🎯 Dados específicos de atividades
      activityData: {
        wordFormation: {
          currentWord: null,
          placedLetters: [],
          availableLetters: []
        },
        sequenceRecognition: {
          currentSequence: null,
          userProgress: []
        },
        visualDiscrimination: {
          currentGroup: null,
          foundTargets: 0,
          totalTargets: 0
        }
      },
      
      // 🎯 Métricas comportamentais avançadas
      behavioralMetrics: {
        activityPreferences: {},
        responsePatterns: [],
        adaptiveAdjustments: 0,
        engagementLevel: 1.0
      },
      
      // 🎯 Sistema de prevenção de repetição
      letterHistory: [],
      wordHistory: [],
      sequenceHistory: []
    });
    setShowStartScreen(true);
  };

  // 🔄 Detectar quando o jogo é completado e finalizar sessão multissensorial
  useEffect(() => {
    if (gameState.round > 10 && !showStartScreen) { // Assumindo que 10 é o número máximo de rounds
      const finalizeMultisensorySession = async () => {
        try {
          const multisensoryReport = await finalizeMultisensory({
            finalScore: gameState.score,
            finalAccuracy: gameState.accuracy,
            totalInteractions: gameState.round - 1,
            sessionDuration: Date.now() - (gameState.startTime || Date.now()),
            difficulty: gameState.difficulty
          });
          console.log('🔄 LetterRecognition: Relatório multissensorial final:', multisensoryReport);
        } catch (error) {
          console.warn('⚠️ LetterRecognition: Erro ao finalizar sessão multissensorial:', error);
        }
      };

      finalizeMultisensorySession();
    }
  }, [gameState.round, showStartScreen, multisensoryInitialized, gameState.score, gameState.accuracy, gameState.difficulty, gameState.startTime]);

  // 🔊 Explicar o jogo atual - ACESSIBILIDADE MELHORADA
  const explainGame = useCallback(() => {
    let explanation = "";

    switch (gameState.currentActivity) {
      case 'letter_selection':
        const letter = gameState.targetLetter;
        if (letter) {
          explanation = `Atividade: Encontrar Letra. Você deve encontrar a letra ${letter.letter}. ${letter.sound} de ${letter.example.split(' ')[1]}. Clique na letra ${letter.letter} quando encontrá-la.`;
        } else {
          explanation = "Atividade: Encontrar Letra. Procure a letra indicada e clique nela quando encontrar.";
        }
        break;

      case 'word_formation':
        const wordData = gameState.activityData.wordFormation;
        if (wordData?.currentWord) {
          explanation = `Atividade: Formação de Palavras. Monte a palavra ${wordData.currentWord.meaning}. ${wordData.currentWord.emoji} ${wordData.currentWord.meaning}. Clique nas letras para formar ${wordData.currentWord.word}.`;
        } else {
          explanation = "Atividade: Formação de Palavras. Monte palavras clicando nas letras na ordem correta.";
        }
        break;

      case 'sequence_recognition':
        const sequenceData = gameState.activityData.sequenceRecognition;
        if (sequenceData?.currentSequence) {
          explanation = `Atividade: Sequência Alfabética. Complete a sequência ${sequenceData.currentSequence.sequence.join(', ')}. Qual letra vem depois?`;
        } else {
          explanation = "Atividade: Sequência Alfabética. Complete sequências de letras do alfabeto.";
        }
        break;

      case 'visual_discrimination':
        const visualData = gameState.activityData.visualDiscrimination;
        if (visualData?.currentGroup) {
          explanation = `Atividade: Discriminação Visual. ${visualData.currentGroup.description}. Clique em todas as letras ${visualData.currentGroup.target} que encontrar.`;
        } else {
          explanation = "Atividade: Discriminação Visual. Encontre todas as letras iguais à letra alvo.";
        }
        break;

      default:
        explanation = "Bem-vindo ao jogo de reconhecimento de letras! Aqui você vai aprender o alfabeto de forma divertida. Use o menu para escolher diferentes atividades.";
    }

    speak(explanation);

    // Registrar uso de acessibilidade
    if (portalReady) {
      recordInteraction({
        type: 'tts_usage',
        data: {
          text: explanation,
          activity: gameState.currentActivity,
          round: gameState.round
        }
      });
    }
  }, [gameState.currentActivity, gameState.targetLetter, gameState.activityData, gameState.round, speak, portalReady, recordInteraction]);

  // 🔄 Repetir a instrução da atividade atual
  const repeatInstruction = useCallback(() => {
    // Fornecer instruções específicas baseadas na atividade atual
    provideActivityInstructions(gameState.currentActivity, gameState.activityData[gameState.currentActivity]);
  }, [gameState.currentActivity, gameState.activityData, provideActivityInstructions]);

  // 🧪 Teste de acessibilidade - simula criança neurodivergente usando apenas TTS
  const testAccessibility = useCallback(() => {
    const testMessage = `
      Teste de Acessibilidade Ativado!

      Simulando criança neurodivergente usando apenas instruções de áudio.

      Atividade atual: ${ACTIVITY_TYPES[gameState.currentActivity]?.name || 'Desconhecida'}

      Instruções sendo fornecidas automaticamente...
    `;

    speak(testMessage, { rate: 0.8 });

    // Fornecer instruções específicas após 2 segundos
    setTimeout(() => {
      provideActivityInstructions(gameState.currentActivity, gameState.activityData[gameState.currentActivity]);
    }, 3000);

  }, [gameState.currentActivity, gameState.activityData, speak, provideActivityInstructions]);

  // 🎉 Feedback sonoro para acertos/erros
  const playFeedback = useCallback((isCorrect, selectedLetter) => {
    if (isCorrect) {
      speak("Muito bem! Você acertou!", { pitch: 1.3, rate: 0.9 });
    } else {
      const targetLetter = gameState.targetLetter;
      const selectedLetterData = LETTERS.find(l => l.id === selectedLetter);
      speak(`Não foi dessa vez. Você escolheu ${selectedLetterData?.sound}, mas a resposta correta é ${targetLetter?.sound}`, { 
        pitch: 0.9, 
        rate: 0.7 
      });
    }
  }, [gameState.targetLetter, speak]);

  // 🎵 Pronunciar letra ao clicar na opção
  const speakLetter = useCallback((letterId) => {
    const letter = LETTERS.find(l => l.id === letterId);
    if (letter) {
      speak(letter.sound, { rate: 0.6, pitch: 1.2 });
    }
  }, [speak]);

  // ==================== MÉTODOS AUXILIARES V3 ====================

  /**
   * Calcula habilidade específica da atividade
   */
  const calculateActivitySpecificSkill = (activityType, isCorrect) => {
    const baseScore = isCorrect ? 0.8 : 0.3;
    
    const skillMultipliers = {
      'letter_selection': 1.0,
      'word_formation': 1.5,
      'sequence_recognition': 1.3,
      'visual_discrimination': 1.4
    };
    
    return baseScore * (skillMultipliers[activityType] || 1.0);
  };

  /**
   * Obtém complexidade da atividade
   */
  const getActivityComplexity = (activityType) => {
    const complexityMap = {
      'letter_selection': 0.3,
      'word_formation': 0.8,
      'sequence_recognition': 0.6,
      'visual_discrimination': 0.7
    };
    
    return complexityMap[activityType] || 0.5;
  };

  /**
   * Atualiza métricas comportamentais específicas da atividade
   */
  const updateActivitySpecificMetrics = (activityType, isCorrect, responseTime) => {
    const updates = {};
    
    switch (activityType) {
      case 'word_formation':
        updates.wordBuildingTime = [...(gameState.behavioralMetrics.activitySpecific?.wordBuildingTime || []), responseTime];
        updates.letterSequenceAccuracy = [...(gameState.behavioralMetrics.activitySpecific?.letterSequenceAccuracy || []), isCorrect ? 1 : 0];
        break;
      
      case 'visual_discrimination':
        updates.visualScanTime = [...(gameState.behavioralMetrics.activitySpecific?.visualScanTime || []), responseTime];
        updates.targetDetectionRate = [...(gameState.behavioralMetrics.activitySpecific?.targetDetectionRate || []), isCorrect ? 1 : 0];
        break;
      
      case 'sequence_recognition':
        updates.sequenceCompletionTime = [...(gameState.behavioralMetrics.activitySpecific?.sequenceCompletionTime || []), responseTime];
        updates.patternRecognition = [...(gameState.behavioralMetrics.activitySpecific?.patternRecognition || []), isCorrect ? 1 : 0];
        break;
      
      default:
        // letter_selection ou outras atividades
        updates.basicProcessingTime = [...(gameState.behavioralMetrics.activitySpecific?.basicProcessingTime || []), responseTime];
        break;
    }
    
    return updates;
  };

  // ==================== HANDLERS ESPECÍFICOS DAS ATIVIDADES V3 ====================

  /**
   * 🔗 Handler para seleção de letras na formação de palavras - CORRIGIDO
   */
  const handleWordLetterSelect = async (letter, letterIndex) => {
    console.log('🎯 Word Letter Select:', { letter, letterIndex, selectedLetter: gameState.selectedLetter });
    
    if (gameState.selectedLetter) {
      console.log('⏳ Already processing a selection, skipping...');
      return;
    }

    const reactionTime = gameState.roundStartTime ? (Date.now() - gameState.roundStartTime) : 0;
    const wordData = gameState.activityData.wordFormation;
    const currentWord = wordData.currentWord;
    
    // Encontrar próxima posição vazia
    const placedLetters = [...(wordData.placedLetters || [])];
    let nextEmptyIndex = placedLetters.findIndex(slot => slot === null || slot === undefined);
    
    if (nextEmptyIndex === -1) {
      console.log('⚠️ Word already complete');
      return; // Palavra já completa
    }
    
    const expectedLetter = currentWord.word[nextEmptyIndex];
    const isCorrect = letter.toUpperCase() === expectedLetter.toUpperCase();
    
    console.log('🔍 Word formation check:', {
      letter: letter.toUpperCase(),
      expected: expectedLetter.toUpperCase(),
      position: nextEmptyIndex,
      isCorrect
    });
    
    if (isCorrect) {
      // Colocar a letra na posição correta
      placedLetters[nextEmptyIndex] = letter.toUpperCase();
      
      setGameState(prev => ({
        ...prev,
        selectedLetter: letter,
        activityData: {
          ...prev.activityData,
          wordFormation: {
            ...prev.activityData.wordFormation,
            placedLetters
          }
        }
      }));

      // Verificar se a palavra está completa
      const isWordComplete = placedLetters.every(slot => slot !== null && slot !== undefined);
      if (isWordComplete) {
        console.log('✅ Word completed!');
        setTimeout(() => {
          handleCorrectAnswer();
        }, 1000); // Delay para mostrar a palavra completa
      } else {
        // Reset selection after successful placement
        setTimeout(() => {
          setGameState(prev => ({ ...prev, selectedLetter: null }));
        }, 500);
      }
    } else {
      console.log('❌ Wrong letter for word formation');
      setGameState(prev => ({ ...prev, selectedLetter: letter }));
      handleIncorrectAnswer();
    }
  };

  /**
   * 📝 Handler para reconhecimento de sequência
   */
  const handleSequenceSelect = async (letter) => {
    console.log('🎯 Sequence Select:', { letter, selectedLetter: gameState.selectedLetter });
    
    if (gameState.selectedLetter) {
      console.log('⏳ Already processing a selection, skipping...');
      return;
    }

    const reactionTime = gameState.roundStartTime ? (Date.now() - gameState.roundStartTime) : 0;
    const sequenceData = gameState.activityData.sequenceRecognition;
    const currentSequence = sequenceData.currentSequence;
    const expectedLetter = currentSequence?.missing;
    const isCorrect = letter.toUpperCase() === expectedLetter?.toUpperCase();

    console.log('🔍 Sequence check:', {
      letter: letter.toUpperCase(),
      expected: expectedLetter?.toUpperCase(),
      isCorrect
    });

    setGameState(prev => ({
      ...prev,
      selectedLetter: letter
    }));

    if (isCorrect) {
      console.log('✅ Correct sequence letter!');
      setTimeout(() => {
        handleCorrectAnswer();
      }, 1000);
    } else {
      console.log('❌ Wrong sequence letter');
      setTimeout(() => {
        handleIncorrectAnswer();
      }, 1000);
    }
  };

  /**
   * 👁️ Handler para discriminação visual
   */
  const handleVisualSelect = async (letter, itemIndex) => {
    console.log('🎯 Visual Select:', { letter, itemIndex, selectedLetter: gameState.selectedLetter });
    
    // Para discriminação visual, permitimos seleções múltiplas das letras alvo
    const selectionKey = `${letter}-${itemIndex}`;
    
    // Verificar se este item já foi selecionado
    const visualData = gameState.activityData.visualDiscrimination;
    if (visualData.selectedItems?.includes(selectionKey)) {
      console.log('⚠️ Item already selected, skipping...');
      return;
    }

    const reactionTime = gameState.roundStartTime ? (Date.now() - gameState.roundStartTime) : 0;
    const currentGroup = visualData.currentGroup;
    const targetLetter = currentGroup?.target;
    const isCorrect = letter.toUpperCase() === targetLetter?.toUpperCase();

    console.log('🔍 Visual discrimination check:', {
      letter: letter.toUpperCase(),
      target: targetLetter?.toUpperCase(),
      isCorrect,
      currentFound: visualData.foundTargets
    });

    setGameState(prev => ({
      ...prev,
      selectedLetter: selectionKey,
      activityData: {
        ...prev.activityData,
        visualDiscrimination: {
          ...prev.activityData.visualDiscrimination,
          foundTargets: isCorrect ? prev.activityData.visualDiscrimination.foundTargets + 1 : prev.activityData.visualDiscrimination.foundTargets,
          selectedItems: [...(prev.activityData.visualDiscrimination.selectedItems || []), selectionKey]
        }
      }
    }));

    if (isCorrect) {
      // Verificar se encontrou todas as letras alvo
      const newFoundCount = visualData.foundTargets + 1;
      console.log(`✅ Correct target found! (${newFoundCount}/${visualData.totalTargets})`);

      if (newFoundCount >= visualData.totalTargets) {
        console.log('🎉 All targets found!');
        setTimeout(() => {
          handleCorrectAnswer();
        }, 1500);
      } else {
        // Reset selection para permitir próxima seleção
        setTimeout(() => {
          setGameState(prev => ({ ...prev, selectedLetter: null }));
        }, 500);
      }
    } else {
      console.log('❌ Wrong letter selected in visual discrimination');
      // Para seleções incorretas, apenas marcar como incorreta mas permitir continuar
      setTimeout(() => {
        setGameState(prev => ({ ...prev, selectedLetter: null }));
      }, 1000);
    }
  };

  /**
   * Função auxiliar para coletar dados específicos da atividade
   */
  const collectActivityData = async (data) => {
    try {
      if (!collectorsHub.sessionActive && sessionIdRef.current) {
        await collectorsHub.initializeSession(sessionIdRef.current, {
          gameMode: 'letter_recognition_v3',
          activityType: data.activityType,
          difficulty: gameState.difficulty
        });
      }

      // Preparar métricas comportamentais específicas
      const activitySpecificMetrics = updateActivitySpecificMetrics(
        data.activityType, 
        data.isCorrect, 
        data.responseTime
      );

      await collectorsHub.collectComprehensiveData({
        // Dados básicos
        targetLetter: { id: data.targetLetter, letter: data.targetLetter },
        selectedLetter: { id: data.selectedLetter, letter: data.selectedLetter },
        isCorrect: data.isCorrect,
        responseTime: data.responseTime,
        sessionId: sessionIdRef.current,
        userId: user?.id || 'anonymous',
        
        // Dados V3 específicos
        activityType: data.activityType,
        activityRound: gameState.activityRoundCount || 0,
        activitiesCompleted: gameState.activitiesCompleted || [],
        
        // Dados da atividade atual
        activityData: {
          ...gameState.activityData,
          ...data.activitySpecificData
        },
        
        // Métricas comportamentais V3
        behavioralMetrics: {
          ...gameState.behavioralMetrics,
          reactionTime: [...(gameState.behavioralMetrics.reactionTime || []), data.responseTime],
          accuracy: [...(gameState.behavioralMetrics.accuracy || []), data.isCorrect ? 1 : 0],
          attentionSpan: Date.now() - gameState.roundStartTime,
          activitySpecific: {
            ...gameState.behavioralMetrics.activitySpecific,
            ...activitySpecificMetrics
          }
        },
        
        // Comportamento do jogador
        playerBehavior: {
          hesitationTime: data.responseTime > 3000 ? data.responseTime - 3000 : 0,
          confidence: data.responseTime < 1000 ? 'high' : data.responseTime < 3000 ? 'medium' : 'low',
          activityEngagement: calculateActivityEngagement(data.activityType, data.responseTime)
        }
      });

      // Registrar interação multissensorial
      if (multisensoryInitialized) {
        await recordMultisensoryInteraction('activity_interaction', {
          interactionType: 'activity_specific',
          gameSpecificData: {
            activityType: data.activityType,
            targetLetter: data.targetLetter,
            selectedLetter: data.selectedLetter,
            isCorrect: data.isCorrect,
            responseTime: data.responseTime,
            ...data.activitySpecificData
          },
          multisensoryProcessing: {
            linguisticProcessing: {
              activitySpecificSkill: calculateActivitySpecificSkill(data.activityType, data.isCorrect),
              complexityHandling: getActivityComplexity(data.activityType)
            },
            cognitiveProcessing: {
              accuracy: data.isCorrect ? 1.0 : 0.0,
              processingSpeed: data.responseTime < 3000 ? 0.8 : 0.5,
              activityAdaptation: calculateActivityAdaptation(data.activityType, gameState.activityRoundCount || 0)
            },
            behavioralProcessing: {
              activityEngagement: calculateActivityEngagement(data.activityType, data.responseTime),
              progressionRate: (gameState.activitiesCompleted?.length || 0) / 6
            }
          }
        });
      }
    } catch (error) {
      console.warn(`📚 LetterRecognition V3: Erro ao coletar dados da atividade ${data.activityType}:`, error);
    }
  };

  // ==================== FUNÇÕES AUXILIARES ESPECÍFICAS ====================

  /**
   * Obtém letra esperada na sequência
   */
  const getExpectedSequenceLetter = (sequence, missingIndex) => {
    if (!sequence || sequence.length === 0) return null;
    
    if (missingIndex === 0 && sequence.length > 1) {
      return String.fromCharCode(sequence[1].charCodeAt(0) - 1);
    } else if (missingIndex === sequence.length) {
      return String.fromCharCode(sequence[sequence.length - 1].charCodeAt(0) + 1);
    } else if (missingIndex > 0 && missingIndex < sequence.length) {
      // Interpolação para posição no meio
      const prevChar = sequence[missingIndex - 1];
      const nextChar = sequence[missingIndex + 1];
      return String.fromCharCode(Math.round((prevChar.charCodeAt(0) + nextChar.charCodeAt(0)) / 2));
    }
    
    return null;
  };

  /**
   * Identifica padrão da sequência
   */
  const identifySequencePattern = (sequence) => {
    if (sequence.length < 2) return 'simple';
    
    const differences = [];
    for (let i = 1; i < sequence.length; i++) {
      differences.push(sequence[i].charCodeAt(0) - sequence[i-1].charCodeAt(0));
    }
    
    if (differences.every(diff => diff === 1)) return 'consecutive';
    if (differences.every(diff => diff === differences[0] && diff > 1)) return 'skip';
    if (differences.every(diff => diff < 0)) return 'reverse';
    return 'complex';
  };

  /**
   * Calcula similaridade fonética
   */
  const calculatePhoneticSimilarity = (sounds) => {
    if (!sounds || sounds.length < 2) return 0.5;
    
    const sound1 = sounds[0]?.toLowerCase();
    const sound2 = sounds[1]?.toLowerCase();
    
    // Análise simplificada de similaridade fonética
    if (sound1 === sound2) return 1.0;
    
    const similarPairs = [
      ['b', 'p'], ['d', 't'], ['g', 'k'], ['f', 'v'], ['s', 'z'], ['m', 'n']
    ];
    
    const areSimilar = similarPairs.some(pair => 
      (pair[0] === sound1 && pair[1] === sound2) || 
      (pair[1] === sound1 && pair[0] === sound2)
    );
    
    return areSimilar ? 0.8 : 0.3;
  };

  /**
   * Obtém complexidade auditiva
   */
  const getAuditoryComplexity = (phoneticPair) => {
    const sounds = phoneticPair.sounds || [];
    const similarity = calculatePhoneticSimilarity(sounds);
    
    // Maior complexidade = maior similaridade (mais difícil de discriminar)
    return similarity;
  };

  /**
   * Calcula engajamento na atividade
   */
  const calculateActivityEngagement = (activityType, responseTime) => {
    const optimalTimes = {
      'letter_selection': 2000,
      'word_formation': 5000,
      'sequence_recognition': 4000,
      'visual_discrimination': 6000
    };
    
    const optimalTime = optimalTimes[activityType] || 3000;
    const timeRatio = Math.abs(responseTime - optimalTime) / optimalTime;
    
    return Math.max(0.2, 1 - timeRatio);
  };

  /**
   * Calcula adaptação à atividade
   */
  const calculateActivityAdaptation = (activityType, activityRound) => {
    // Adaptação melhora com rounds na mesma atividade
    const adaptationBonus = Math.min(activityRound * 0.1, 0.3);
    const baseAdaptation = 0.5;
    
    return Math.min(baseAdaptation + adaptationBonus, 1.0);
  };

  // ==================== HANDLERS DE RESPOSTA ====================

  /**
   * ✅ Handler para resposta correta
   */
  const handleCorrectAnswer = useCallback(() => {
    setGameState(prev => {
      const newScore = prev.score + 10;
      const newRound = prev.round + 1;
      const totalAttempts = prev.round;
      const correctAnswers = Math.floor(prev.score / 10) + 1;
      const newAccuracy = Math.round((correctAnswers / totalAttempts) * 100);

      // 🎯 Atualizar histórico de letras para evitar repetição
      const updatedHistory = [...prev.letterHistory];
      if (prev.targetLetter && prev.currentActivity === 'letter_selection') {
        updatedHistory.push(prev.targetLetter);
        // Manter apenas as últimas 3 letras no histórico
        if (updatedHistory.length > 3) {
          updatedHistory.shift();
        }
      }

      // 🎯 Atualizar histórico de palavras para evitar repetição
      const updatedWordHistory = [...prev.wordHistory];
      if (prev.currentActivity === 'word_formation' && prev.activityData?.wordFormation?.currentWord) {
        updatedWordHistory.push(prev.activityData.wordFormation.currentWord.word);
        // Manter apenas as últimas 3 palavras no histórico
        if (updatedWordHistory.length > 3) {
          updatedWordHistory.shift();
        }
      }

      // 🎯 Atualizar histórico de sequências para evitar repetição
      const updatedSequenceHistory = [...prev.sequenceHistory];
      if (prev.currentActivity === 'sequence_recognition' && prev.activityData?.sequenceRecognition?.currentSequence) {
        updatedSequenceHistory.push(prev.activityData.sequenceRecognition.currentSequence.description);
        // Manter apenas as últimas 3 sequências no histórico
        if (updatedSequenceHistory.length > 3) {
          updatedSequenceHistory.shift();
        }
      }

      return {
        ...prev,
        selectedLetter: prev.targetLetter?.id || prev.selectedLetter,
        showFeedback: true,
        score: newScore,
        round: newRound,
        accuracy: newAccuracy,
        activityRoundCount: prev.activityRoundCount + 1,
        letterHistory: updatedHistory,
        wordHistory: updatedWordHistory,
        sequenceHistory: updatedSequenceHistory
      };
    });

    speak("Muito bem! Resposta correta!", { pitch: 1.3, rate: 0.9 });
    
    // ✅ Avanço automático para próxima pergunta (não próximo jogo)
    setTimeout(() => {
      console.log('🎯 Avançando para próxima pergunta automaticamente...');
      setGameState(prev => ({
        ...prev,
        showFeedback: false,
        selectedLetter: null // Reset selection
      }));
      generateNewRound();
    }, 1500); // Tempo reduzido para progressão mais rápida
  }, [speak, generateNewRound]);

  /**
   * ❌ Handler para resposta incorreta
   */
  const handleIncorrectAnswer = useCallback(() => {
    setGameState(prev => {
      const totalAttempts = prev.round;
      const correctAnswers = Math.floor(prev.score / 10);
      const newAccuracy = totalAttempts > 0 ? Math.round((correctAnswers / totalAttempts) * 100) : 100;

      return {
        ...prev,
        showFeedback: true,
        accuracy: newAccuracy,
        round: prev.round + 1,
        activityRoundCount: prev.activityRoundCount + 1
      };
    });

    speak("Tente novamente! Você consegue!", { pitch: 1.0, rate: 0.8 });
    setTimeout(() => {
      setGameState(prev => ({ ...prev, showFeedback: false }));
      generateNewRound();
    }, 1500); // Tempo reduzido para progressão mais rápida
  }, [speak, generateNewRound]);

  // ==================== COLETORES DE DADOS DAS ATIVIDADES ====================

  /**
   * Inicializa a sessão do coletor para reconhecimento de letras
   */
  const initializeCollectors = async () => {
    try {
      if (collectorsHub && typeof collectorsHub.initializeSession === 'function') {
        await collectorsHub.initializeSession(sessionIdRef.current, {
          difficulty: gameState.difficulty,
          availableLetters: gameState.availableLetters.map(l => l.id),
          gameMode: 'letter_recognition'
        });
        console.log('📚 LetterRecognition: Coletores inicializados com sucesso');
      }
    } catch (error) {
      console.warn('⚠️ LetterRecognition: Erro ao inicializar coletores:', error);
    }
  };

  /**
   * Registra interação com os coletores de dados
   */
  const registerDataCollectors = async (isCorrect, reactionTime) => {
    try {
      if (collectorsHub.sessionActive) {
        await collectorsHub.collectComprehensiveData({
          // Dados básicos
          targetLetter: gameState.targetLetter,
          selectedLetter: LETTERS.find(l => l.id === gameState.selectedLetter),
          isCorrect,
          responseTime: reactionTime,
          sessionId: sessionIdRef.current,
          userId: user?.id || 'anonymous',
          
          // Dados V3 específicos
          activityType: gameState.currentActivity,
          activityRound: gameState.activityRoundCount || 0,
          activitiesCompleted: gameState.activitiesCompleted || [],
          
          // Dados da atividade atual
          activityData: gameState.activityData,
          
          // Métricas comportamentais V3
          behavioralMetrics: {
            ...gameState.behavioralMetrics,
            reactionTime: [...(gameState.behavioralMetrics.reactionTime || []), reactionTime],
            accuracy: [...(gameState.behavioralMetrics.accuracy || []), isCorrect ? 1 : 0],
            attentionSpan: Date.now() - gameState.roundStartTime,
            currentLevel: gameState.currentLevel,
            totalAttempts: gameState.round,
            activitySpecific: {
              ...gameState.behavioralMetrics.activitySpecific,
              // Adicionar métricas específicas baseadas na atividade atual
              ...(gameState.currentActivity === 'word_formation' && {
                wordBuildingTime: reactionTime,
                letterSequenceAccuracy: gameState.activityData?.wordAccuracy || 0
              }),
              ...(gameState.currentActivity === 'visual_discrimination' && {
                visualScanTime: reactionTime,
                targetDetectionRate: gameState.activityData?.detectionRate || 0
              }),
              ...(gameState.currentActivity === 'sequence_recognition' && {
                sequenceCompletionTime: reactionTime,
                patternRecognition: isCorrect ? 1 : 0
              })
            }
          },
          
          // Comportamento do jogador
          playerBehavior: {
            hesitationTime: reactionTime > 3000 ? reactionTime - 3000 : 0,
            confidence: reactionTime < 1000 ? 'high' : reactionTime < 3000 ? 'medium' : 'low',
            attemptPattern: gameState.round % 5 === 0 ? 'milestone' : 'regular'
          }
        });
      }
    } catch (error) {
      console.warn('📚 LetterRecognition V3: Erro ao registrar interação:', error);
    }
  };

  // Resto do código...

  // 🎯 RENDERIZADOR PRINCIPAL DE ATIVIDADES
  const renderCurrentActivity = () => {
    switch (gameState.currentActivity) {
      case 'letter_selection':
        return renderLetterSelection();
      case 'word_formation':
        return renderWordFormation();
      case 'sequence_recognition':
        return renderSequenceRecognition();
      case 'visual_discrimination':
        return renderVisualDiscrimination();
      default:
        return renderLetterSelection();
    }
  };

  // 🔤 COMPONENTE: Seleção de Letras - CORRIGIDO COM LAYOUT PADRÃO FORMAÇÃO DE PALAVRAS
  const renderLetterSelection = () => {
    const activityData = gameState.activityData.letterSelection || {};
    
    console.log('🔤 Rendering Letter Selection - Debug:', {
      activityData,
      gameState: gameState.activityData,
      currentActivity: gameState.currentActivity
    });
    
    // Verificar se dados estão inicializados
    if (!gameState.targetLetter || !gameState.availableLetters) {
      console.log('🚨 Letter selection data not initialized:', { targetLetter: gameState.targetLetter, availableLetters: gameState.availableLetters });
      return (
        <div className={styles.gameArea}>
          <div className={styles.soundActivity}>
            <div className={styles.soundIndicator}>�</div>
            <h3>🔄 Carregando seleção de letras...</h3>
            <p className={styles.activityTip}>Aguarde enquanto preparamos as letras para você!</p>
          </div>
        </div>
      );
    }

    console.log('🔤 Rendering letter selection:', {
      targetLetter: gameState.targetLetter,
      availableLetters: gameState.availableLetters,
      selectedLetter: gameState.selectedLetter
    });

    return (
      <div className={styles.gameArea}>
        <div className={styles.soundActivity}>
          <div className={styles.soundIndicator}>{gameState.targetLetter.example?.split(' ')[0] || '🔤'}</div>
          <h3>Encontre a letra: <strong>{gameState.targetLetter.letter}</strong></h3>
          <p className={styles.activityTip}>
            {gameState.targetLetter.example} - Clique na letra correta
          </p>
        </div>

        {/* Letras disponíveis - padrão MemoryGame */}
        <div className={styles.lettersGrid}>
          {gameState.availableLetters.map((letter, letterIndex) => (
            <div
              key={`${letter.id}-${letterIndex}`}
              className={`${styles.letterCard} ${
                gameState.selectedLetter === letter.id ? 'selected' : ''
              } ${
                gameState.selectedLetter === letter.id && gameState.selectedLetter === gameState.targetLetter.id
                  ? 'correct' : ''
              } ${
                gameState.selectedLetter === letter.id && gameState.selectedLetter !== gameState.targetLetter.id
                  ? 'incorrect' : ''
              }`}
              onClick={() => handleLetterSelect(letter.id)}
              onMouseEnter={() => {
                if (ttsActive && !gameState.selectedLetter) {
                  speakLetter(letter.id);
                }
              }}
              onTouchStart={() => {
                if (ttsActive && !gameState.selectedLetter) {
                  speakLetter(letter.id);
                }
              }}
              title={`Letra ${letter.letter} - ${letter.sound}`}
              style={{
                cursor: 'pointer',
                backgroundColor: letter.color
              }}
            >
              <div className={styles.letterContent}>{letter.letter}</div>
              <div className={styles.letterLabel}>
                {letter.sound}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // 🔗 COMPONENTE: Formação de Palavras - CORRIGIDO COM LAYOUT PADRÃO
  const renderWordFormation = () => {
    const wordData = gameState.activityData.wordFormation;
    
    console.log('🔤 Rendering Word Formation - Debug:', {
      wordData,
      gameState: gameState.activityData,
      currentActivity: gameState.currentActivity
    });
    
    // Verificar se dados estão inicializados
    if (!wordData || !wordData.currentWord) {
      console.log('🚨 Word formation data not initialized:', wordData);
      return (
        <div className={styles.wordFormationActivity}>
          <div className={styles.activityInstruction}>
            <h3>🔄 Carregando formação de palavras...</h3>
            <p>Aguarde enquanto preparamos as palavras para você!</p>
          </div>
        </div>
      );
    }

    const placedLetters = wordData.placedLetters || [];
    const nextEmptyIndex = placedLetters.findIndex(slot => slot === null || slot === undefined);
    const isWordComplete = placedLetters.every(slot => slot !== null && slot !== undefined);

    console.log('🔤 Rendering word formation:', {
      currentWord: wordData.currentWord,
      placedLetters,
      availableLetters: wordData.availableLetters,
      nextEmptyIndex,
      isWordComplete
    });

    return (
      <div className={styles.gameArea}>
        <div className={styles.soundActivity}>
          <div className={styles.soundIndicator}>{wordData.currentWord.emoji}</div>
          <h3>Monte a palavra: <strong>{wordData.currentWord.word}</strong></h3>
          <p className={styles.activityTip}>
            {wordData.currentWord.meaning} - Clique nas letras na ordem correta
          </p>
        </div>

        {/* Slots da palavra - padrão MemoryGame */}
        <div className={styles.lettersGrid} style={{ marginBottom: '2rem' }}>
          {wordData.currentWord.word.split('').map((correctLetter, index) => (
            <div
              key={index}
              className={`${styles.letterCard} ${
                placedLetters[index] ? 'correct' : ''
              } ${
                index === nextEmptyIndex ? 'active' : ''
              }`}
              style={{
                background: placedLetters[index]
                  ? 'var(--success-bg)'
                  : index === nextEmptyIndex
                    ? 'rgba(255, 193, 7, 0.2)'
                    : 'var(--card-background)',
                border: placedLetters[index]
                  ? '2px solid var(--success-border)'
                  : index === nextEmptyIndex
                    ? '2px solid #FFC107'
                    : '2px dashed rgba(255, 255, 255, 0.5)',
                minHeight: '80px',
                cursor: 'default'
              }}
            >
              <div className={styles.letterContent}>
                {placedLetters[index] || '_'}
              </div>
              <div className={styles.letterLabel}>
                Posição {index + 1}
              </div>
            </div>
          ))}
        </div>

        {/* Letras disponíveis - padrão MemoryGame */}
        <div className={styles.lettersGrid}>
          {wordData.availableLetters.map((letter, letterIndex) => (
            <div
              key={`${letter}-${letterIndex}`}
              className={`${styles.letterCard}`}
              onClick={() => handleWordLetterSelect(letter, letterIndex)}
              onMouseEnter={() => {
                if (ttsActive) {
                  speak(letter, { rate: 0.6, pitch: 1.1 });
                }
              }}
              onTouchStart={() => {
                if (ttsActive) {
                  speak(letter, { rate: 0.6, pitch: 1.1 });
                }
              }}
              title={`Letra ${letter}`}
              style={{
                cursor: 'pointer'
              }}
            >
              <div className={styles.letterContent}>{letter}</div>
              <div className={styles.letterLabel}>
                Clique para usar
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // 📝 COMPONENTE: Reconhecimento de Sequência - SEGUINDO PADRÃO MEMORYGAME
  const renderSequenceRecognition = () => {
    const sequenceData = gameState.activityData.sequenceRecognition;
    const currentSequence = sequenceData?.currentSequence;

    console.log('🔍 Rendering sequence recognition:', { sequenceData, currentSequence });

    if (!currentSequence) {
      console.log('🚨 Sequence recognition data not initialized:', sequenceData);
      return (
        <div className={styles.gameArea}>
          <div className={styles.soundActivity}>
            <div className={styles.soundIndicator}>📝</div>
            <h3>🔄 Carregando reconhecimento de sequência...</h3>
            <p className={styles.activityTip}>Aguarde enquanto preparamos as sequências para você!</p>
          </div>
        </div>
      );
    }

    return (
      <div className={styles.gameArea}>
        <div className={styles.soundActivity}>
          <div className={styles.soundIndicator}>📝</div>
          <h3>Complete a sequência alfabética:</h3>
          
          {/* Estilo para animação pulse */}
          <style jsx>{`
            @keyframes pulse {
              0% { transform: scale(1); opacity: 1; }
              50% { transform: scale(1.1); opacity: 0.7; }
              100% { transform: scale(1); opacity: 1; }
            }
          `}</style>

          {/* Sequência visual AMPLIADA - Padrão MemoryGame Melhorado */}
          <div className={styles.sequenceDisplay} style={{
            display: 'flex',
            gap: '1.5rem',
            justifyContent: 'center',
            margin: '3rem 0',
            fontSize: '3.5rem',
            fontWeight: 'bold'
          }}>
            {currentSequence.sequence.map((letter, index) => (
              <span key={index} className={styles.sequenceLetter} style={{
                padding: '1.5rem 2rem',
                background: 'linear-gradient(135deg, var(--card-background), rgba(255,255,255,0.1))',
                border: '3px solid var(--primary-color)',
                borderRadius: '16px',
                minWidth: '80px',
                minHeight: '80px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                textAlign: 'center',
                color: 'var(--text-color)',
                boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
                transition: 'transform 0.2s ease'
              }}>
                {letter}
              </span>
            ))}
            <span className={styles.sequenceLetter} style={{
              padding: '1.5rem 2rem',
              background: 'linear-gradient(135deg, rgba(255, 193, 7, 0.3), rgba(255, 193, 7, 0.1))',
              border: '3px solid #FFC107',
              borderRadius: '16px',
              minWidth: '80px',
              minHeight: '80px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              textAlign: 'center',
              color: '#FFD700',
              fontSize: '4rem',
              fontWeight: 'bold',
              animation: 'pulse 1.5s infinite',
              boxShadow: '0 6px 12px rgba(255, 193, 7, 0.3)'
            }}>
              ?
            </span>
          </div>

          <p className={styles.activityTip} style={{
            fontSize: '1.4rem',
            fontWeight: '600',
            color: 'var(--primary-color)',
            textAlign: 'center',
            margin: '1.5rem 0',
            padding: '1rem',
            background: 'rgba(255,255,255,0.05)',
            borderRadius: '12px',
            border: '1px solid rgba(255,255,255,0.1)'
          }}>
            {currentSequence.description}
          </p>
        </div>

        <div className={styles.lettersGrid}>
          {currentSequence.options.map((option, optionIndex) => (
            <div
              key={`${option}-${optionIndex}`}
              className={`${styles.letterCard} ${
                gameState.selectedLetter === option ? 'selected' : ''
              } ${
                gameState.selectedLetter === option && option === currentSequence.missing
                  ? 'correct' : ''
              } ${
                gameState.selectedLetter === option && option !== currentSequence.missing
                  ? 'incorrect' : ''
              }`}
              onClick={() => handleSequenceSelect(option)}
              onMouseEnter={() => {
                if (ttsActive && !gameState.selectedLetter) {
                  speak(`Letra ${option}`, { rate: 0.6, pitch: 1.2 });
                }
              }}
              onTouchStart={() => {
                if (ttsActive && !gameState.selectedLetter) {
                  speak(`Letra ${option}`, { rate: 0.6, pitch: 1.2 });
                }
              }}
              title={`Letra ${option}`}
              style={{
                cursor: gameState.selectedLetter ? 'default' : 'pointer'
              }}
            >
              <div className={styles.letterContent}>{option}</div>
              <div className={styles.letterLabel}>
                {gameState.selectedLetter === option ?
                  (option === currentSequence.missing ? '✅ Correto!' : '❌ Incorreto') :
                  'Opção'
                }
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // 👁️ COMPONENTE: Discriminação Visual - SEGUINDO PADRÃO MEMORYGAME MELHORADO
  const renderVisualDiscrimination = () => {
    const visualData = gameState.activityData.visualDiscrimination;
    const currentGroup = visualData?.currentGroup;

    console.log('🔍 Rendering visual discrimination:', { visualData, currentGroup });

    if (!currentGroup) {
      console.log('🚨 Visual discrimination data not initialized:', visualData);
      return (
        <div className={styles.gameArea}>
          <div className={styles.soundActivity}>
            <div className={styles.soundIndicator}>👁️</div>
            <h3>🔄 Carregando discriminação visual...</h3>
            <p className={styles.activityTip}>Aguarde enquanto preparamos o desafio visual para você!</p>
          </div>
        </div>
      );
    }

    return (
      <div className={styles.gameArea}>
        <div className={styles.soundActivity}>
          <div className={styles.soundIndicator}>👁️</div>
          <h3>Encontre todas as letras: <strong style={{
            color: '#FFD700',
            fontSize: '1.5em',
            textShadow: '2px 2px 4px rgba(0,0,0,0.3)'
          }}>{currentGroup.target}</strong></h3>
          
          {/* Estilo para animação target */}
          <style jsx>{`
            @keyframes targetGlow {
              0% { box-shadow: 0 0 5px #FFD700; }
              50% { box-shadow: 0 0 20px #FFD700, 0 0 30px #FFD700; }
              100% { box-shadow: 0 0 5px #FFD700; }
            }
            .target-highlight {
              animation: targetGlow 2s infinite;
            }
          `}</style>

          {/* Progresso visual melhorado */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '1rem',
            margin: '1.5rem 0',
            padding: '1rem',
            background: 'rgba(255,255,255,0.05)',
            borderRadius: '12px',
            border: '1px solid rgba(255,255,255,0.1)'
          }}>
            <span style={{ fontSize: '1.2rem', fontWeight: '600' }}>Progresso:</span>
            {Array.from({ length: visualData.totalTargets }, (_, i) => (
              <div key={i} style={{
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                background: i < visualData.foundTargets 
                  ? 'linear-gradient(135deg, #4CAF50, #45a049)' 
                  : 'rgba(255,255,255,0.1)',
                border: i < visualData.foundTargets 
                  ? '2px solid #4CAF50' 
                  : '2px solid rgba(255,255,255,0.3)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '1.2rem',
                fontWeight: 'bold',
                transition: 'all 0.3s ease'
              }}>
                {i < visualData.foundTargets ? '✅' : '⭕'}
              </div>
            ))}
          </div>

          <p className={styles.activityTip} style={{
            fontSize: '1.3rem',
            fontWeight: '600',
            color: 'var(--primary-color)',
            textAlign: 'center',
            margin: '1rem 0',
            padding: '1rem',
            background: 'rgba(255,255,255,0.05)',
            borderRadius: '12px',
            border: '1px solid rgba(255,255,255,0.1)'
          }}>
            Encontradas: <span style={{color: '#4CAF50'}}>{visualData.foundTargets}</span>/{visualData.totalTargets}
          </p>
        </div>

        {/* Grid de letras AMPLIADO - Padrão MemoryGame Melhorado */}
        <div className={styles.lettersGrid} style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
          gap: '1.5rem',
          maxWidth: '600px',
          margin: '0 auto'
        }}>
          {currentGroup.items.map((item, index) => {
            const itemKey = `${item}-${index}`;
            const isSelected = visualData.selectedItems?.includes(itemKey);
            const isTarget = item === currentGroup.target;
            const isCorrectSelection = isSelected && isTarget;
            const isIncorrectSelection = isSelected && !isTarget;

            return (
              <div
                key={itemKey}
                className={`${styles.letterCard} ${
                  isCorrectSelection ? 'correct' : 
                  isIncorrectSelection ? 'incorrect' : ''
                }`}
                onClick={() => handleVisualSelect(item, index)}
                onMouseEnter={() => {
                  if (ttsActive && !isSelected) {
                    speak(`Letra ${item}`, { rate: 0.6, pitch: 1.1 });
                  }
                }}
                onTouchStart={() => {
                  if (ttsActive && !isSelected) {
                    speak(`Letra ${item}`, { rate: 0.6, pitch: 1.1 });
                  }
                }}
                title={`Letra ${item}`}
                style={{
                  minHeight: '100px',
                  minWidth: '100px',
                  opacity: isSelected ? (isCorrectSelection ? 1 : 0.5) : 1,
                  cursor: isSelected ? 'default' : 'pointer',
                  background: isCorrectSelection 
                    ? 'linear-gradient(135deg, #4CAF50, #45a049)' 
                    : isIncorrectSelection 
                      ? 'linear-gradient(135deg, #f44336, #d32f2f)'
                      : 'var(--card-background)', // REMOVIDO: destaque dourado das letras alvo
                  border: isCorrectSelection 
                    ? '3px solid #4CAF50' 
                    : isIncorrectSelection 
                      ? '3px solid #f44336'
                      : '2px solid rgba(255,255,255,0.2)', // REMOVIDO: borda dourada das letras alvo
                  transform: isSelected ? 'scale(0.95)' : 'scale(1)',
                  transition: 'all 0.3s ease',
                  boxShadow: isCorrectSelection 
                    ? '0 8px 16px rgba(76, 175, 80, 0.3)' 
                    : isIncorrectSelection 
                      ? '0 8px 16px rgba(244, 67, 54, 0.3)'
                      : '0 4px 8px rgba(0,0,0,0.1)'
                }}
              >
                <div className={styles.letterContent} style={{
                  fontSize: '2.5rem',
                  fontWeight: 'bold',
                  color: isCorrectSelection || isIncorrectSelection ? '#fff' : 'var(--text-color)'
                }}>
                  {item}
                </div>
                <div className={styles.letterLabel} style={{
                  color: isCorrectSelection || isIncorrectSelection ? '#fff' : 'var(--text-secondary)',
                  fontWeight: '600'
                }}>
                  {isCorrectSelection ? '✅ Encontrado!' : 
                   isIncorrectSelection ? '❌ Errado' : 
                   'Letra'}
                </div>
              </div>
            );
          })}
        </div>

        {/* Indicador de conclusão */}
        {visualData.foundTargets >= visualData.totalTargets && (
          <div style={{
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            background: 'linear-gradient(135deg, #4CAF50, #45a049)',
            color: 'white',
            padding: '2rem',
            borderRadius: '20px',
            fontSize: '1.5rem',
            fontWeight: 'bold',
            textAlign: 'center',
            boxShadow: '0 10px 30px rgba(0,0,0,0.3)',
            zIndex: 1000,
            animation: 'bounce 0.5s ease-in-out'
          }}>
            🎉 Parabéns! Você encontrou todas as letras! 🎉
          </div>
        )}
      </div>
    );
  };

  // 🎯 RENDERIZAÇÃO PRINCIPAL - SEGUINDO PADRÃO DOS OUTROS JOGOS
  if (showStartScreen) {
    return (
      <GameStartScreen
        gameTitle="Reconhecimento de Letras"
        gameDescription="Aprenda o alfabeto de forma divertida e interativa"
        gameIcon="🔤"
        onStart={startGame}
        onBack={onBack}
        difficulties={[
          { id: 'easy', name: 'Fácil', description: '4 letras básicas\nIdeal para iniciantes', icon: '😊' },
          { id: 'medium', name: 'Médio', description: '6 letras variadas\nDesafio equilibrado', icon: '🎯' },
          { id: 'hard', name: 'Avançado', description: '8 letras completas\nPara especialistas', icon: '🚀' }
        ]}
      />
    );
  }

  return (
    <div className={styles.letterRecognitionGame}>
      <div className={styles.gameContent}>
        {/* Header do jogo - padrão Contagem de Números */}
        <div className={styles.gameHeader}>
          <h1 className={styles.gameTitle}>
            🔤 Reconhecimento de Letras V3
            <div style={{ fontSize: '0.7rem', opacity: 0.8, marginTop: '0.25rem' }}>
              {ACTIVITY_TYPES[gameState.currentActivity.toUpperCase()]?.name || 'Seleção de Letras'}
            </div>
          </h1>
          <button
            className={`${styles.headerTtsButton} ${ttsActive ? styles.ttsActive : ''}`}
            onClick={toggleTTS}
            title={ttsActive ? 'Desativar TTS' : 'Ativar TTS'}
            aria-label={ttsActive ? 'Desativar TTS' : 'Ativar TTS'}
          >
            {ttsActive ? '🔊' : '🔇'}
          </button>
        </div>

        {/* Header com estatísticas - padrão MemoryGame */}
        <div className={styles.gameStats}>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.score}</div>
            <div className={styles.statLabel}>Pontos</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.round}</div>
            <div className={styles.statLabel}>Rodada</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.accuracy}%</div>
            <div className={styles.statLabel}>Precisão</div>
          </div>
        </div>

        {/* Menu de atividades - padrão MemoryGame */}
        <div className={styles.activityMenu}>
          {Object.values(ACTIVITY_TYPES).map((activity) => (
            <button
              key={activity.id}
              className={`${styles.activityButton} ${
                gameState.currentActivity === activity.id ? styles.active : ''
              }`}
              onClick={() => changeActivity(activity.id)}
            >
              <span>{activity.icon}</span>
              <span>{activity.name}</span>
            </button>
          ))}
        </div>

        {/* Renderização da atividade atual */}
        {gameState.currentActivity === 'letter_selection' && renderLetterSelection()}
        {gameState.currentActivity === 'word_formation' && renderWordFormation()}
        {gameState.currentActivity === 'sequence_recognition' && renderSequenceRecognition()}
        {gameState.currentActivity === 'visual_discrimination' && renderVisualDiscrimination()}

        {/* Controles do jogo - padrão MemoryGame simplificado */}
        <div className={styles.gameControls}>
          <button className={styles.controlButton} onClick={explainGame}>
            🔊 Explicar
          </button>
          <button className={styles.controlButton} onClick={restartGame}>
            🔄 Reiniciar
          </button>
          <button className={styles.controlButton} onClick={onBack}>
            ⬅️ Voltar
          </button>
        </div>
      </div>
    </div>
  );
}

export default LetterRecognitionGame;
