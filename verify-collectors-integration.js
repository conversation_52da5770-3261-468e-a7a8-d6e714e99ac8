#!/usr/bin/env node

/**
 * @file verify-collectors-integration.js
 * @description Verifica se todos os jogos estão integrando com os coletores
 * @version 1.0.0
 */

import fs from 'fs';

console.log('🔍 VERIFICAÇÃO: Integração dos Coletores nos Jogos');
console.log('=' .repeat(70));

const games = [
  'LetterRecognition',
  'ContagemNumeros', 
  'MemoryGame',
  'ColorMatch',
  'ImageAssociation',
  'QuebraCabeca',
  'CreativePainting',
  'MusicalSequence',
  'PadroesVisuais'
];

// Padrões que indicam uso de coletores
const collectorPatterns = [
  // Chamadas diretas dos coletores
  /collectorsHub\.collectData\s*\(/g,
  /collectorsHub\.collectComprehensiveData\s*\(/g,
  /collectorsHub\.processInteraction\s*\(/g,
  /collectorsHub\.analyze\s*\(/g,
  
  // Hooks multissensoriais
  /recordMultisensoryInteraction\s*\(/g,
  /processMultisensoryData\s*\(/g,
  
  // Hooks de métricas
  /recordInteraction\s*\(/g,
  /processGameMetrics\s*\(/g,
  
  // Inicialização de sessões
  /initializeSession\s*\(/g,
  /startSession\s*\(/g
];

function analyzeGameCollectorIntegration(gameName) {
  const gameFile = `src/games/${gameName}/${gameName}Game.jsx`;
  
  if (!fs.existsSync(gameFile)) {
    return { error: 'Arquivo não encontrado' };
  }

  const content = fs.readFileSync(gameFile, 'utf8');
  
  const results = {
    gameName,
    fileSize: content.length,
    hasCollectorsHub: content.includes('collectorsHub'),
    hasMultisensoryHook: content.includes('useMultisensoryIntegration'),
    hasUnifiedGameLogic: content.includes('useUnifiedGameLogic'),
    hasTherapeuticOrchestrator: content.includes('useTherapeuticOrchestrator'),
    collectorCalls: [],
    totalCollectorCalls: 0,
    integrationScore: 0
  };

  // Verificar cada padrão
  collectorPatterns.forEach((pattern, index) => {
    const matches = [...content.matchAll(pattern)];
    if (matches.length > 0) {
      const patternNames = [
        'collectData calls',
        'collectComprehensiveData calls',
        'processInteraction calls',
        'analyze calls',
        'recordMultisensoryInteraction calls',
        'processMultisensoryData calls',
        'recordInteraction calls',
        'processGameMetrics calls',
        'initializeSession calls',
        'startSession calls'
      ];
      
      results.collectorCalls.push({
        type: patternNames[index],
        count: matches.length,
        examples: matches.slice(0, 2).map(m => m[0])
      });
      results.totalCollectorCalls += matches.length;
    }
  });

  // Calcular score de integração
  let score = 0;
  if (results.hasCollectorsHub) score += 25;
  if (results.hasMultisensoryHook) score += 25;
  if (results.hasUnifiedGameLogic) score += 25;
  if (results.hasTherapeuticOrchestrator) score += 25;
  if (results.totalCollectorCalls > 0) score += 25;
  if (results.totalCollectorCalls > 3) score += 25;

  results.integrationScore = Math.min(score, 100);

  // Verificar se há handlers de ação que deveriam chamar coletores
  const actionHandlers = [
    /handle\w*Answer\s*=/g,
    /handle\w*Click\s*=/g,
    /handle\w*Select\s*=/g,
    /handle\w*Action\s*=/g,
    /onClick\s*=\s*{[^}]*handle/g
  ];

  let totalHandlers = 0;
  actionHandlers.forEach(pattern => {
    const matches = [...content.matchAll(pattern)];
    totalHandlers += matches.length;
  });

  results.actionHandlers = totalHandlers;
  results.collectorToHandlerRatio = totalHandlers > 0 ? (results.totalCollectorCalls / totalHandlers) : 0;

  return results;
}

// Analisar todos os jogos
console.log('\n🎮 Analisando integração dos coletores...\n');

const analysisResults = {};
let totalIntegrationScore = 0;

games.forEach(gameName => {
  console.log(`📋 ${gameName}:`);
  console.log('-'.repeat(50));
  
  const analysis = analysisResults[gameName] = analyzeGameCollectorIntegration(gameName);
  
  if (analysis.error) {
    console.log(`❌ ${analysis.error}`);
    return;
  }

  // Status dos hooks
  const hooks = [];
  if (analysis.hasCollectorsHub) hooks.push('CollectorsHub');
  if (analysis.hasMultisensoryHook) hooks.push('Multisensory');
  if (analysis.hasUnifiedGameLogic) hooks.push('UnifiedLogic');
  if (analysis.hasTherapeuticOrchestrator) hooks.push('Therapeutic');
  
  console.log(`🎣 Hooks integrados: ${hooks.join(', ') || 'Nenhum'}`);
  console.log(`📊 Chamadas de coletores: ${analysis.totalCollectorCalls}`);
  console.log(`🎯 Handlers de ação: ${analysis.actionHandlers}`);
  console.log(`📈 Ratio coletor/handler: ${analysis.collectorToHandlerRatio.toFixed(2)}`);
  console.log(`⭐ Score de integração: ${analysis.integrationScore}%`);

  // Mostrar tipos de chamadas
  if (analysis.collectorCalls.length > 0) {
    console.log('🔧 Tipos de chamadas encontradas:');
    analysis.collectorCalls.forEach(call => {
      console.log(`   • ${call.type}: ${call.count}x`);
    });
  } else {
    console.log('⚠️ NENHUMA chamada de coletor encontrada!');
  }

  totalIntegrationScore += analysis.integrationScore;
  console.log('');
});

// Resumo final
console.log('='.repeat(70));
console.log('📊 RESUMO DA INTEGRAÇÃO DOS COLETORES');
console.log('='.repeat(70));

// Ranking por score de integração
const ranking = Object.entries(analysisResults)
  .filter(([_, analysis]) => !analysis.error)
  .sort(([_, a], [__, b]) => b.integrationScore - a.integrationScore);

console.log('\n🏆 RANKING DE INTEGRAÇÃO:');
ranking.forEach(([gameName, analysis], index) => {
  const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '📊';
  const status = analysis.integrationScore >= 75 ? '✅' : analysis.integrationScore >= 50 ? '⚠️' : '❌';
  console.log(`${medal} ${index + 1}. ${gameName}: ${analysis.integrationScore}% ${status}`);
});

// Estatísticas gerais
const validAnalyses = Object.values(analysisResults).filter(a => !a.error);
const avgScore = totalIntegrationScore / validAnalyses.length;
const totalCollectorCalls = validAnalyses.reduce((sum, a) => sum + a.totalCollectorCalls, 0);
const totalHandlers = validAnalyses.reduce((sum, a) => sum + a.actionHandlers, 0);

console.log('\n📈 ESTATÍSTICAS GERAIS:');
console.log(`   • Score médio de integração: ${avgScore.toFixed(1)}%`);
console.log(`   • Total de chamadas de coletores: ${totalCollectorCalls}`);
console.log(`   • Total de handlers de ação: ${totalHandlers}`);
console.log(`   • Ratio geral coletor/handler: ${(totalCollectorCalls / totalHandlers).toFixed(2)}`);

// Identificar jogos que precisam de correção
console.log('\n🚨 JOGOS QUE PRECISAM DE CORREÇÃO:');
const needsCorrection = ranking.filter(([_, analysis]) => analysis.integrationScore < 75);

if (needsCorrection.length === 0) {
  console.log('✅ Todos os jogos estão bem integrados!');
} else {
  needsCorrection.forEach(([gameName, analysis]) => {
    console.log(`❌ ${gameName}: ${analysis.integrationScore}% - Precisa melhorar integração`);
    
    const issues = [];
    if (!analysis.hasCollectorsHub) issues.push('Falta CollectorsHub');
    if (!analysis.hasMultisensoryHook) issues.push('Falta Multisensory');
    if (analysis.totalCollectorCalls === 0) issues.push('Sem chamadas de coletores');
    if (analysis.collectorToHandlerRatio < 0.5) issues.push('Poucos coletores para handlers');
    
    console.log(`   Problemas: ${issues.join(', ')}`);
  });
}

// Estimativa de métricas por jogo
console.log('\n📊 ESTIMATIVA DE MÉTRICAS POR JOGO (com integração correta):');
const collectorsPerGame = {
  'LetterRecognition': 14,
  'ContagemNumeros': 10,
  'MemoryGame': 15,
  'ColorMatch': 4,
  'ImageAssociation': 4,
  'QuebraCabeca': 7,
  'CreativePainting': 8,
  'MusicalSequence': 11,
  'PadroesVisuais': 16
};

let totalEstimatedMetrics = 0;
ranking.forEach(([gameName, analysis]) => {
  const collectors = collectorsPerGame[gameName] || 5;
  const integrationFactor = analysis.integrationScore / 100;
  const estimatedMetrics = Math.round(collectors * 5 * 5 * integrationFactor); // coletores * métricas * ações * fator
  totalEstimatedMetrics += estimatedMetrics;
  
  const status = analysis.integrationScore >= 75 ? '✅' : '⚠️';
  console.log(`   ${status} ${gameName}: ~${estimatedMetrics} métricas/sessão (${collectors} coletores)`);
});

console.log(`\n🎯 TOTAL ESTIMADO: ~${totalEstimatedMetrics} métricas por sessão completa`);
console.log(`🎯 POTENCIAL MÁXIMO: ~${Object.values(collectorsPerGame).reduce((a, b) => a + b, 0) * 25} métricas`);

console.log('\n✅ VERIFICAÇÃO COMPLETA FINALIZADA!');
console.log('\n💡 PRÓXIMOS PASSOS:');
console.log('   1. Corrigir jogos com score < 75%');
console.log('   2. Adicionar chamadas de coletores nos handlers de ação');
console.log('   3. Testar integração completa');
console.log('   4. Verificar se métricas estão sendo salvas no dashboard');
