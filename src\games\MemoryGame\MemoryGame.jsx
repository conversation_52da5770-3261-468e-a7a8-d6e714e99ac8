import React, { useState, useEffect, useCallback, useRef, useContext, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { v4 as uuidv4 } from 'uuid';
import { useAccessibilityContext } from '../../components/context/AccessibilityContext';
import { useGameAudio } from '../../games/shared/GameUtils';
import { SystemContext } from '../../components/context/SystemContext';
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic';
import { useTherapeuticOrchestrator } from '../../hooks/useTherapeuticOrchestrator';
import { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration';
import { MemoryGameCollectorsHub } from './collectors';
import { MemoryGameMetrics } from './MemoryGameMetrics';
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen';
// 🔊 SISTEMA TTS PADRONIZADO - Portal Betina V3
import { useStandardTTS, StandardTTSButton, TTS_MESSAGES } from '../../components/shared/StandardTTS';
import styles from './MemoryGame.module.css';
import {
  DIFFICULTY_LEVELS,
  MEMORY_CARDS,
  GAME_CONFIG,
  ENCOURAGEMENT_MESSAGES,
  THEME_CONFIG,
  ACTIVITY_CONFIG,
  ACTIVITY_DIFFICULTY_CONFIG,
  ANIMATION_CONFIG
} from './MemoryGameConfig';
import sequenceThemes from './sequenceThemes';

// Activity types definition
const ACTIVITY_TYPES = {
  pair_matching: {
    id: 'pair_matching',
    name: 'Pares de Cartas',
    icon: '🧩',
    description: 'Encontre todos os pares de cartas iguais'
  },
  sequence_memory: {
    id: 'sequence_memory',
    name: 'Sequência de Cores',
    icon: '🧠',
    description: 'Memorize e repita a sequência de cores'
  },
  spatial_location: {
    id: 'spatial_location',
    name: 'Posições Espaciais',
    icon: '📍',
    description: 'Lembre-se das posições espaciais'
  },
  image_reconstruction: {
    id: 'image_reconstruction',
    name: 'Reconstrução de Imagem',
    icon: '🖼️',
    description: 'Reconstrua a imagem original'
  },
  number_sequence: {
    id: 'number_sequence',
    name: 'Sequência Numérica',
    icon: '🔢',
    description: 'Complete a sequência numérica'
  }
};

function MemoryGame({ onBack }) {
  const { user, ttsEnabled = true } = useContext(SystemContext);
  const { settings } = useAccessibilityContext();
  const { playSound } = useGameAudio();
  const sessionIdRef = useRef(uuidv4());

  // =====================================================
  // 🔊 SISTEMA TTS PADRONIZADO - Portal Betina V3
  // =====================================================
  const {
    ttsActive,
    toggleTTS,
    speak,
    stopSpeaking,
    speakGameInstructions,
    speakFeedback,
    speakProgress,
    speakGameEnd
  } = useStandardTTS('memory_game');

  // Instruções específicas do jogo - CONSTANTE PARA EVITAR RE-RENDERS
  const gameInstructions = useRef("Encontre os pares de cartas iguais virando duas cartas por vez. Use sua memória para lembrar onde estão as cartas.").current;

  // Backend integration hooks
  const {
    startUnifiedSession,
    endUnifiedSession,
    recordInteraction,
    updateMetrics,
    sessionId,
    isSessionActive
  } = useUnifiedGameLogic('memory');

  const shouldUseTherapeutic = user?.id && user.id !== 'anonymous' && user.id !== '';
  const therapeuticOrchestrator = useTherapeuticOrchestrator({
    userId: shouldUseTherapeutic ? user.id : null
  });

  const [collectorsHub] = useState(() => {
    if (window.globalSystemInstance?.gameSpecificProcessors?.gameCollectors?.MemoryGame?.hub) {
      console.log('🧠 Reutilizando instância existente do MemoryGame CollectorsHub');
      return window.globalSystemInstance.gameSpecificProcessors.gameCollectors.MemoryGame.hub;
    }
    console.log('🧠 Criando nova instância do MemoryGame CollectorsHub');
    return new MemoryGameCollectorsHub();
  });

  const {
    initializeSession: initMultisensory,
    recordInteraction: recordMultisensoryInteraction,
    finalizeSession: finalizeMultisensory,
    multisensoryData,
    isInitialized: multisensoryInitialized
  } = useMultisensoryIntegration(sessionId, {
    gameType: 'memory-game',
    sensorTypes: {
      visual: true,
      haptic: true,
      tts: ttsEnabled,
      gestural: true,
      biometric: true
    },
    adaptiveMode: true,
    autoUpdate: true,
    enablePatternAnalysis: true,
    logLevel: 'info',
    learningStyle: user?.profile?.learningStyle || 'visual'
  });

  // Referências para clicks
  const lastClickRef = useRef(0);
  
  // Game state
  // CORRIGIDO: Estado inicial estabilizado com useMemo para evitar re-renders
  const initialGameState = useMemo(() => ({
    status: 'waiting',
    difficulty: GAME_CONFIG.initialDifficulty,
    activityTimerStarted: false,
    currentActivity: GAME_CONFIG.activityRotation[0],
    activitiesCompleted: 0,
    rotationIndex: 0,
    activityAttempts: 0,
    theme: null,
    score: 0,
    attempts: 0,
    startTime: null,
    level: 1,
    gameStarted: false,
    showingCards: false,
    canPlay: false,
    feedback: null,
    lives: GAME_CONFIG.maxLives,
    moves: 0,
    elapsedTime: 0,
    lastClickTime: 0,
    bonusPoints: 0,
    streak: 0,
    perfectMatches: 0,
    hintsUsed: 0,
    timeBonus: 0,
    difficultyBonus: 0,
    sequenceMemorized: false,
    showingSequence: false,
    specialTaskActive: false,
    showActivityMenu: false, // NOVO: Controla exibição do menu de atividades
    themeProgress: { groupsFormed: 0, requiredGroups: 0, currentTask: null },
    activityData: {
      pair_matching: { cards: [], flippedCards: [], matchedCards: [] },
      sequence_memory: { sequence: [], userSequence: [], showingSequence: false, currentRound: 1 },
      spatial_location: { grid: [], targetPositions: [], userSelections: [], showingPositions: false, currentRound: 1 },
      image_reconstruction: { pieces: [], targetImage: [], userArrangement: [], showPreview: false, completed: false },
      number_sequence: { numbers: [], userAnswer: '', missingNumber: null, answered: false }
    },
    metrics: new MemoryGameMetrics()
  }), []);

  const [gameState, setGameState] = useState(initialGameState);

  const [showStartScreen, setShowStartScreen] = useState(true);
  const [showEncouragement, setShowEncouragement] = useState(false);
  const [cognitiveAnalysisVisible, setCognitiveAnalysisVisible] = useState(false);
  const [analysisResults, setAnalysisResults] = useState(null);
  const metricsRef = useRef(gameState.metrics);
  const timerRef = useRef(null);

  // TTS automático ao iniciar o jogo - CORRIGIDO
  useEffect(() => {
    if (gameState.status === 'playing' && ttsActive) {
      setTimeout(() => {
        speakGameInstructions('Jogo da Memória', gameInstructions);
      }, 1000);
    }
  }, [gameState.status, ttsActive, speakGameInstructions]); // REMOVIDO: gameInstructions das dependências

  // Utility functions
  const shuffleArray = useCallback((array) => {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  }, []);

  const startTimer = useCallback(() => {
    if (timerRef.current) return;
    timerRef.current = setInterval(() => {
      setGameState(prev => ({
        ...prev,
        elapsedTime: prev.startTime ? Date.now() - prev.startTime : 0
      }));
    }, 1000);
  }, []);

  const stopTimer = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  const formatTime = useCallback((timeInMs) => {
    if (!timeInMs || timeInMs < 0) return '00:00';
    const seconds = Math.floor(timeInMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }, []);

  const startActivityTimer = useCallback(() => {
    if (!gameState.activityTimerStarted && gameState.status === 'playing') {
      setGameState(prev => ({
        ...prev,
        activityTimerStarted: true,
        startTime: Date.now()
      }));
      startTimer();
    }
  }, [gameState.activityTimerStarted, gameState.status, startTimer]);

  // Backend and metrics integration
  const recordMemoryInteraction = useCallback((firstCard, secondCard, isCorrect, responseTime, attemptNumber) => {
    try {
      const interactionData = {
        type: 'memory_pair_attempt',
        data: {
          firstCard: firstCard ? { id: firstCard.id, pairId: firstCard.pairId, name: firstCard.name } : null,
          secondCard: secondCard ? { id: secondCard.id, pairId: secondCard.pairId, name: secondCard.name } : null,
          isCorrect,
          responseTime,
          attemptNumber,
          difficulty: gameState.difficulty,
          timestamp: Date.now()
        }
      };
      
      // Usar o coletor especializado
      if (collectorsHub && collectorsHub.collectors.pairMatchingV3) {
        collectorsHub.collectors.pairMatchingV3.collect({
          pairs: [
            {
              firstCardId: firstCard ? firstCard.id : null,
              secondCardId: secondCard ? secondCard.id : null,
              isMatch: isCorrect,
              timeTaken: responseTime,
              attemptNumber: attemptNumber
            }
          ],
          userMatches: isCorrect ? 1 : 0,
          visualMemoryScore: isCorrect ? 1 : 0,
          difficulty: gameState.difficulty
        });
      }
      
      // Integração com outros sistemas
      if (recordInteraction) recordInteraction(interactionData);
      if (metricsRef.current) metricsRef.current.recordInteraction(interactionData);
      if (recordMultisensoryInteraction) recordMultisensoryInteraction('memory_interaction', interactionData);
    } catch (error) {
      console.warn('⚠️ Erro ao registrar interação de memória:', error);
    }
  }, [gameState.difficulty, recordInteraction, recordMultisensoryInteraction, collectorsHub]);

  const finalizeMultisensorySession = useCallback(async () => {
    try {
      const sessionDuration = gameState.startTime ? (Date.now() - gameState.startTime) : 0;
      const finalAccuracy = gameState.attempts > 0 ? (gameState.score / (gameState.attempts * 100)) : 0;
      const multisensoryReport = await finalizeMultisensory({
        finalScore: gameState.score,
        finalAccuracy,
        totalInteractions: gameState.attempts,
        sessionDuration,
        difficulty: gameState.difficulty,
        matchedPairs: gameState.activityData.pair_matching.matchedCards.length / 2,
        totalPairs: gameState.activityData.pair_matching.cards.length / 2
      });
      console.log('🔄 MemoryGame: Relatório multissensorial final:', multisensoryReport);
    } catch (error) {
      console.warn('⚠️ MemoryGame: Erro ao finalizar sessão multissensorial:', error);
    }
  }, [gameState, finalizeMultisensory]);

  useEffect(() => {
    if (gameState.status === 'completed' && multisensoryInitialized) {
      finalizeMultisensorySession();
    }
  }, [gameState.status, multisensoryInitialized, finalizeMultisensorySession]);

  // CORRIGIDO: useEffect com dependências fixas para evitar loop infinito
  useEffect(() => {
    if (gameState.metrics && startUnifiedSession && recordInteraction && updateMetrics) {
      gameState.metrics.connectToBackend({
        startSession: startUnifiedSession,
        recordInteraction,
        updateMetrics
      });
    }
  }, [startUnifiedSession, recordInteraction, updateMetrics]); // REMOVIDO: gameState.metrics das dependências

  // Special tasks functions
  const getSpecialTaskForTheme = useCallback((themeConfig, difficulty) => {
    switch (difficulty) {
      case DIFFICULTY_LEVELS.EASY:
        return 'sequence_memorization';
      case DIFFICULTY_LEVELS.MEDIUM:
        return 'speed_grouping';
      case DIFFICULTY_LEVELS.HARD:
        return 'constellation_formation';
      default:
        return null;
    }
  }, []);

  const showSequencePreview = useCallback((cards) => {
    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        pair_matching: {
          ...prev.activityData.pair_matching,
          cards: prev.activityData.pair_matching.cards.map(card => ({ ...card, isFlipped: true }))
        }
      },
      showingSequence: true
    }));
    speak("Memorize a posição das cartas! Elas serão escondidas em breve.", { rate: 0.9 });
    setTimeout(() => {
      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          pair_matching: {
            ...prev.activityData.pair_matching,
            cards: prev.activityData.pair_matching.cards.map(card => ({ ...card, isFlipped: false }))
          }
        },
        showingSequence: false,
        sequenceMemorized: true,
        status: 'playing'
      }));
      speak("Agora encontre os pares!", { rate: 1.0 });
    }, 3000);
  }, [speak]);

  const checkSpecialTaskCompletion = useCallback((matchedCards, cards, theme) => {
    if (!theme) return false;
    switch (theme.name) {
      case 'Diversão Mista':
        return matchedCards.length >= cards.length;
      case 'Mundo Colorido':
        const colorGroups = THEME_CONFIG[gameState.difficulty].colorCategories;
        const matchedByCategory = Object.keys(colorGroups).reduce((acc, cat) => {
          acc[cat] = matchedCards.filter(id => {
            const card = cards.find(c => c.uniqueId === id);
            return card && colorGroups[cat].includes(card.pairId);
          }).length / 2;
          return acc;
        }, {});
        return Object.values(matchedByCategory).every(count => count >= 1);
      case 'Desafio dos Campeões':
        const championGroups = THEME_CONFIG[gameState.difficulty].championGroups;
        const matchedByGroup = Object.keys(championGroups).reduce((acc, group) => {
          acc[group] = matchedCards.filter(id => {
            const card = cards.find(c => c.uniqueId === id);
            return card && championGroups[group].includes(card.pairId);
          }).length / 2;
          return acc;
        }, {});
        return Object.values(matchedByGroup).every(count => count >= 1);
      default:
        return false;
    }
  }, [gameState.difficulty]);

  const getThematicEncouragement = useCallback((difficulty, isSpecialTask = false) => {
    const messages = ENCOURAGEMENT_MESSAGES[difficulty] || ENCOURAGEMENT_MESSAGES.general;
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];
    if (isSpecialTask) {
      switch (difficulty) {
        case DIFFICULTY_LEVELS.MEDIUM:
          return 'Ótimo! Você organizou as cores perfeitamente! 🌈';
        case DIFFICULTY_LEVELS.HARD:
          return 'Incrível! Você formou uma constelação de campeões! ⭐';
        default:
          return randomMessage;
      }
    }
    return randomMessage;
  }, []);

  // Initialize game
  const initializeGame = useCallback(async (difficulty) => {
    const cardSet = MEMORY_CARDS[difficulty];
    const themeConfig = THEME_CONFIG[difficulty];
    if (!cardSet || !themeConfig) return;

    const shuffledCards = shuffleArray([...cardSet]).map((card, index) => ({
      ...card,
      uniqueId: `${card.id}-${index}`,
      isFlipped: false, // Cartas começam com o ponto de interrogação (não viradas)
      isMatched: false
    }));

    const specialTask = getSpecialTaskForTheme(themeConfig, difficulty);
    const activityData = {};
    GAME_CONFIG.activityRotation.forEach(activity => {
      activityData[activity] = activity === 'pair_matching'
        ? { cards: shuffledCards, flippedCards: [], matchedCards: [] }
        : initializeActivityData(activity, difficulty);
    });

    setGameState(prev => ({
      ...prev,
      status: 'playing',
      difficulty,
      theme: themeConfig,
      activityTimerStarted: false,
      currentActivity: GAME_CONFIG.activityRotation[0],
      activitiesCompleted: 0,
      rotationIndex: 0,
      activityAttempts: 0,
      score: 0,
      attempts: 0,
      startTime: null,
      level: 1,
      gameStarted: true,
      showingCards: false,
      canPlay: true,
      feedback: null,
      lives: GAME_CONFIG.maxLives,
      moves: 0,
      elapsedTime: 0,
      bonusPoints: 0,
      streak: 0,
      perfectMatches: 0,
      hintsUsed: 0,
      timeBonus: 0,
      difficultyBonus: 0,
      sequenceMemorized: false,
      showingSequence: false,
      specialTaskActive: !!specialTask,
      themeProgress: { groupsFormed: 0, requiredGroups: themeConfig.requiredGroups || 0, currentTask: specialTask },
      activityData,
      metrics: new MemoryGameMetrics()
    }));
    metricsRef.current = new MemoryGameMetrics();

    setShowStartScreen(false);

    try {
      await initMultisensory(sessionIdRef.current, {
        difficulty,
        theme: themeConfig.name,
        gameMode: 'memory_matching',
        cardsCount: shuffledCards.length,
        userId: user?.id || 'anonymous'
      });
    } catch (error) {
      console.warn('⚠️ Erro ao inicializar integração multissensorial:', error);
    }

    speak(`${themeConfig.narrative} Encontre os pares iguais para completar a aventura.`, { rate: 0.8 });

    if (themeConfig.hasSequenceTask) {
      setTimeout(() => showSequencePreview(shuffledCards), 2000);
    }

    metricsRef.current.startGame({
      gameType: 'MemoryGame',
      difficulty,
      theme: themeConfig.name,
      cardsCount: shuffledCards.length,
      timestamp: Date.now()
    });
  }, [shuffleArray, speak, initMultisensory, user, getSpecialTaskForTheme, showSequencePreview]);

  const initializeActivityData = useCallback((activity, difficulty) => {
    switch (activity) {
      case 'pair_matching':
        return { cards: [], flippedCards: [], matchedCards: [] };
      case 'sequence_memory':
        return { sequence: [], userSequence: [], showingSequence: false, currentRound: 1, activeColorIndex: null };
      case 'spatial_location':
        return { grid: [], targetPositions: [], userSelections: [], showingPositions: false, currentRound: 1 };
      case 'image_reconstruction':
        return { pieces: [], targetImage: [], userArrangement: [], showPreview: false, completed: false, currentRound: 1 };
      case 'number_sequence':
        return { numbers: [], userAnswer: '', missingNumber: null, answered: false, currentRound: 1, completed: false };
      default:
        return {};
    }
  }, []);

  const handleRestart = useCallback(() => {
    stopTimer();
    setGameState({
      status: 'waiting',
      difficulty: GAME_CONFIG.initialDifficulty,
      activityTimerStarted: false,
      currentActivity: GAME_CONFIG.activityRotation[0],
      activitiesCompleted: 0,
      rotationIndex: 0,
      activityAttempts: 0,
      theme: null,
      score: 0,
      attempts: 0,
      startTime: null,
      level: 1,
      gameStarted: false,
      showingCards: false,
      canPlay: false,
      feedback: null,
      lives: GAME_CONFIG.maxLives,
      moves: 0,
      elapsedTime: 0,
      bonusPoints: 0,
      streak: 0,
      perfectMatches: 0,
      hintsUsed: 0,
      timeBonus: 0,
      difficultyBonus: 0,
      sequenceMemorized: false,
      showingSequence: false,
      specialTaskActive: false,
      themeProgress: { groupsFormed: 0, requiredGroups: 0, currentTask: null },
      activityData: {
        pair_matching: { cards: [], flippedCards: [], matchedCards: [] },
        sequence_memory: { sequence: [], userSequence: [], showingSequence: false, currentRound: 1, activeColorIndex: null },
        spatial_location: { grid: [], targetPositions: [], userSelections: [], showingPositions: false, currentRound: 1 },
        image_reconstruction: { pieces: [], targetImage: [], userArrangement: [], showPreview: false, completed: false, currentRound: 1 },
        number_sequence: { numbers: [], userAnswer: '', missingNumber: null, answered: false, currentRound: 1, completed: false }
      },
      metrics: new MemoryGameMetrics()
    });
    metricsRef.current = new MemoryGameMetrics();
    setShowStartScreen(true);
    speak("Jogo reiniciado! Escolha uma nova dificuldade.", { rate: 0.9 });
  }, [stopTimer, speak]);

  const explainGame = useCallback(() => {
    const explanation = `
      Bem-vindo ao Jogo da Memória! 
      Complete atividades para testar sua memória:
      - Pares de Cartas: Encontre pares iguais.
      - Sequência de Cores: Repita a sequência mostrada.
      - Posições Espaciais: Lembre-se das posições destacadas.
      - Reconstrução de Imagem: Reconstrua o padrão.
      - Sequência Numérica: Complete a sequência.
      Você tem ${gameState.lives} vidas. Boa sorte!
    `;
    speak(explanation, { rate: 0.8 });
  }, [gameState.lives, speak]);

  const handleCardClick = useCallback((cardUniqueId) => {
    if (gameState.status !== 'playing' || gameState.activityData.pair_matching.flippedCards.length >= 2 || !gameState.canPlay) return;

    startActivityTimer();
    const card = gameState.activityData.pair_matching.cards.find(c => c.uniqueId === cardUniqueId);
    if (!card || card.isFlipped || card.isMatched) return;

    const newFlippedCards = [...gameState.activityData.pair_matching.flippedCards, cardUniqueId];

    setGameState(prev => {
      const updatedCards = prev.activityData.pair_matching.cards.map(c =>
        c.uniqueId === cardUniqueId ? { ...c, isFlipped: true } : c
      );
      return {
        ...prev,
        canPlay: false,
        moves: prev.moves + 1,
        activityData: {
          ...prev.activityData,
          pair_matching: {
            ...prev.activityData.pair_matching,
            cards: updatedCards,
            flippedCards: newFlippedCards
          }
        },
        activityAttempts: newFlippedCards.length === 2 ? prev.activityAttempts + 1 : prev.activityAttempts
      };
    });

    if (newFlippedCards.length === 2) {
      setTimeout(() => {
        setGameState(prev => {
          const [firstId, secondId] = newFlippedCards;
          const firstCard = prev.activityData.pair_matching.cards.find(c => c.uniqueId === firstId);
          const secondCard = prev.activityData.pair_matching.cards.find(c => c.uniqueId === secondId);

          if (!firstCard || !secondCard) {
            return {
              ...prev,
              canPlay: true,
              activityData: {
                ...prev.activityData,
                pair_matching: { ...prev.activityData.pair_matching, flippedCards: [] }
              }
            };
          }

          const isMatch = firstCard.pairId === secondCard.pairId;
          recordMemoryInteraction(firstCard, secondCard, isMatch, Date.now() - prev.startTime, prev.activityAttempts + 1);

          if (isMatch) {
            const newMatchedCards = [...prev.activityData.pair_matching.matchedCards, firstId, secondId];
            const updatedCards = prev.activityData.pair_matching.cards.map(c =>
              c.uniqueId === firstId || c.uniqueId === secondId ? { ...c, isMatched: true } : c
            );
            const message = ENCOURAGEMENT_MESSAGES.pair_matching[Math.floor(Math.random() * ENCOURAGEMENT_MESSAGES.pair_matching.length)];
            speak(message, { rate: 1.0, pitch: 1.2 });

            const specialTaskCompleted = checkSpecialTaskCompletion(newMatchedCards, updatedCards, prev.theme);
            if (specialTaskCompleted) {
              const specialMessage = getThematicEncouragement(prev.difficulty, true);
              setTimeout(() => speak(specialMessage, { rate: 0.9, pitch: 1.3 }), 1500);
              setGameState(prev => ({
                ...prev,
                themeProgress: { ...prev.themeProgress, groupsFormed: prev.themeProgress.groupsFormed + 1 }
              }));
            }

            if (newMatchedCards.length >= updatedCards.length) {
              setTimeout(() => {
                setGameState(prev => ({
                  ...prev,
                  activityData: {
                    ...prev.activityData,
                    pair_matching: { ...prev.activityData.pair_matching, completed: true }
                  }
                }));
                handleActivityCompletion();
              }, 1000);
            }

            return {
              ...prev,
              score: prev.score + GAME_CONFIG.scoring.basePoints + GAME_CONFIG.scoring.perfectRound,
              perfectMatches: prev.perfectMatches + 1,
              streak: prev.streak + 1,
              canPlay: true,
              activityData: {
                ...prev.activityData,
                pair_matching: {
                  ...prev.activityData.pair_matching,
                  cards: updatedCards,
                  matchedCards: newMatchedCards,
                  flippedCards: []
                }
              }
            };
          } else {
            speak('Ops! Tente novamente!', { rate: 0.9, pitch: 0.8 });
            const updatedCards = prev.activityData.pair_matching.cards.map(c =>
              c.uniqueId === firstId || c.uniqueId === secondId ? { ...c, isFlipped: false } : c
            );
            return {
              ...prev,
              lives: prev.lives - 1,
              streak: 0,
              canPlay: true,
              activityData: {
                ...prev.activityData,
                pair_matching: {
                  ...prev.activityData.pair_matching,
                  cards: updatedCards,
                  flippedCards: []
                }
              }
            };
          }
        });
      }, GAME_CONFIG.timing.cardFlipDuration);
    } else {
      setGameState(prev => ({ ...prev, canPlay: true }));
    }
  }, [gameState, speak, startActivityTimer, recordMemoryInteraction, checkSpecialTaskCompletion, getThematicEncouragement]);

  const handleActivityCompletion = useCallback(() => {
    const message = `${ACTIVITY_CONFIG[gameState.currentActivity].name} concluída com sucesso!`;
    try {
      speak(message);
    } catch (e) {
      console.warn('Falha ao usar TTS para mensagem de conclusão de atividade:', e);
    }
    metricsRef.current.recordActivityComplete({
      activity: gameState.currentActivity,
      attempts: gameState.activityAttempts,
      score: gameState.score,
      timeElapsed: gameState.elapsedTime
    });

    // ALTERADO: Não passa automaticamente para próxima atividade
    // O usuário deve escolher quando avançar ou continuar na mesma atividade
    setGameState(prev => ({ 
      ...prev, 
      status: 'activity_completed',
      showActivityMenu: true // Mostrar menu de atividades para o usuário escolher
    }));
    
    try {
      speak('Atividade concluída! Você pode escolher continuar na mesma atividade ou tentar uma nova!', { rate: 0.9, pitch: 1.1 });
    } catch (e) {
      console.warn('Falha ao usar TTS para mensagem de opção:', e);
    }
  }, [gameState.currentActivity, gameState.activityAttempts, gameState.score, gameState.elapsedTime, speak]);

  // NOVA FUNÇÃO: Permitir usuário escolher próxima ação após completar atividade
  const handleUserActivityChoice = useCallback((choice) => {
    if (choice === 'continue_same') {
      // Continuar na mesma atividade - resetar dados da atividade atual
      const activityKey = gameState.currentActivity;
      setGameState(prev => ({
        ...prev,
        status: 'playing',
        showActivityMenu: false,
        activityAttempts: 0,
        activityTimerStarted: false,
        activitiesCompleted: prev.activitiesCompleted + 1, // Incrementar contador
        activityData: {
          ...prev.activityData,
          [activityKey]: initializeActivityData(activityKey, prev.difficulty)
        }
      }));
      
      try {
        speak(`Continuando na atividade ${ACTIVITY_CONFIG[gameState.currentActivity].name}!`, { rate: 0.9 });
      } catch (e) {
        console.warn('Falha ao usar TTS:', e);
      }
    } else if (choice === 'new_activity') {
      // Mostrar menu de seleção de atividades
      setGameState(prev => ({
        ...prev,
        showActivityMenu: true
      }));
    }
  }, [gameState.currentActivity, initializeActivityData, speak]);

  // FUNÇÃO MODIFICADA: Permitir seleção manual de atividade
  const selectActivity = useCallback((activityKey) => {
    setGameState(prev => ({
      ...prev,
      currentActivity: activityKey,
      status: 'playing',
      showActivityMenu: false,
      activityAttempts: 0,
      activityTimerStarted: false,
      activitiesCompleted: prev.activitiesCompleted + (prev.currentActivity !== activityKey ? 1 : 0),
      activityData: {
        ...prev.activityData,
        [activityKey]: initializeActivityData(activityKey, prev.difficulty)
      }
    }));
    
    try {
      speak(`Iniciando atividade: ${ACTIVITY_CONFIG[activityKey].name}`, { rate: 0.9 });
    } catch (e) {
      console.warn('Falha ao usar TTS:', e);
    }
  }, [initializeActivityData, speak]);

  const rotateToNextActivity = useCallback(() => {
    setGameState(prev => {
      const nextIndex = (prev.rotationIndex + 1) % GAME_CONFIG.activityRotation.length;
      const nextActivity = GAME_CONFIG.activityRotation[nextIndex];
      return {
        ...prev,
        currentActivity: nextActivity,
        rotationIndex: nextIndex,
        activitiesCompleted: prev.activitiesCompleted + 1,
        activityAttempts: 0,
        activityTimerStarted: false,
        activityData: {
          ...prev.activityData,
          [nextActivity]: initializeActivityData(nextActivity, prev.difficulty)
        }
      };
    });
    speak(`Próxima atividade: ${ACTIVITY_CONFIG[gameState.currentActivity].name}`);
  }, [gameState.currentActivity, initializeActivityData, speak]);

  const renderActivityInterface = useCallback(() => {
    switch (gameState.currentActivity) {
      case 'pair_matching':
        return renderPairMatchingActivity();
      case 'sequence_memory':
        return renderSequenceMemoryActivity();
      case 'spatial_location':
        return renderSpatialLocationActivity();
      case 'image_reconstruction':
        return renderImageReconstructionActivity();
      case 'number_sequence':
        return renderNumberSequenceActivity();
      default:
        return <div>Atividade não encontrada</div>;
    }
  }, [gameState.currentActivity, gameState.activityData, gameState.difficulty]);

  const renderPairMatchingActivity = useCallback(() => {
    // Verificar se activityData e pair_matching existem
    if (!gameState.activityData || !gameState.activityData.pair_matching) {
      const initialData = initializeActivityData('pair_matching', gameState.difficulty);
      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          pair_matching: initialData
        }
      }));
      return <div>Inicializando atividade...</div>;
    }

    const { cards, flippedCards, matchedCards } = gameState.activityData.pair_matching;
    if (!cards || !cards.length) {
      const newCards = initializeActivityData('pair_matching', gameState.difficulty).cards;
      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          pair_matching: { cards: newCards, flippedCards: [], matchedCards: [] }
        }
      }));
      return <div>Carregando cartas...</div>;
    }

    const gridCols = ACTIVITY_DIFFICULTY_CONFIG.pair_matching[gameState.difficulty].gridSize.split('x')[1];

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>🧩 Combinação de Pares</h2>
          <p className={styles.instructions}>Encontre todos os pares de cartas iguais</p>
        </div>
        <div className={styles.gridContainer} style={{ gridTemplateColumns: `repeat(${gridCols}, 1fr)` }}>
          {cards.map(card => (
            <div
              key={card.uniqueId}
              className={`${styles.card} ${card.isFlipped || card.isMatched ? styles.flipped : ''}`}
              onClick={() => handleCardClick(card.uniqueId)}
              role="button"
              tabIndex={0}
              aria-label={`Carta ${card.isFlipped ? card.name : 'não revelada'}`}
              onKeyDown={(e) => e.key === 'Enter' && handleCardClick(card.uniqueId)}
            >
              <div className={styles.cardInner}>
                <div className={styles.cardFront}>?</div>
                <div className={styles.cardBack}>{card.symbol}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }, [gameState.activityData.pair_matching, gameState.difficulty, handleCardClick]);

  const renderSequenceMemoryActivity = useCallback(() => {
    // Verificar se activityData e sequence_memory existem
    if (!gameState.activityData || !gameState.activityData.sequence_memory) {
      const initialData = initializeActivityData('sequence_memory', gameState.difficulty);
      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          sequence_memory: initialData
        }
      }));
      return <div>Inicializando atividade...</div>;
    }

    const { sequence, userSequence, showingSequence, currentRound, activeColorIndex = null } = gameState.activityData.sequence_memory;
    const config = ACTIVITY_DIFFICULTY_CONFIG.sequence_memory[gameState.difficulty];

    if (!sequence.length) {
      // Garantir que temos cores suficientes
      const allColors = [...config.elements];
      const newSequence = shuffleArray(allColors).slice(0, config.sequenceLength);
      
      console.log('🎮 Sequência gerada:', newSequence);
      
      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          sequence_memory: {
            sequence: newSequence,
            userSequence: [],
            showingSequence: true,
            currentRound: 1,
            activeColorIndex: null
          }
        }
      }));
      
      // Usar TTS para instruções, é mais confiável que os sons
      try {
        speak('Observe a sequência de cores com atenção!', { rate: 0.9 });
      } catch (e) {
        console.warn('Falha ao usar TTS para instruções:', e);
      }
      
      // Mostrar cada cor individualmente para reforço
      newSequence.forEach((color, index) => {
        const colorName = color === '#f44336' ? 'vermelha' : 
                          color === '#2196f3' ? 'azul' : 
                          color === '#4caf50' ? 'verde' : 
                          color === '#ffeb3b' ? 'amarela' : 
                          color === '#9c27b0' ? 'roxa' : 
                          color === '#ff9800' ? 'laranja' : '';
                          
        // Usar setTimeout para destacar cada cor em sequência
        setTimeout(() => {
          try {
            // Usar TTS para mencionar a cor em vez de sons
            if (ttsActive && index < 3) { // Limitar para evitar muito ruído
              speak(colorName, { rate: 1.0, volume: 0.4 });
            }
          } catch (e) {
            console.warn("Erro ao falar nome da cor:", e);
          }
        }, config.showTime / 2 + (index * 400));
      });
      
      // Aumente o tempo de visualização para facilitar o aprendizado
      setTimeout(() => {
        setGameState(prev => ({
          ...prev,
          activityData: {
            ...prev.activityData,
            sequence_memory: { 
              ...prev.activityData.sequence_memory, 
              showingSequence: false,
              activeColorIndex: null 
            }
          }
        }));
        try {
          speak('Agora repita a sequência na ordem correta.', { rate: 0.9 });
        } catch (e) {
          console.warn('Falha ao usar TTS para instruções:', e);
        }
      }, config.showTime + 800);
      return <div>Preparando sequência...</div>;
    }

    // Função para adicionar classe temporária de feedback a um elemento
    const addTempClass = (color, className, duration = 500) => {
      const element = document.querySelector(`button[data-color="${color}"]`);
      if (element) {
        element.classList.add(className);
        setTimeout(() => {
          element.classList.remove(className);
        }, duration);
      }
    };

    const handleColorClick = (color) => {
      startActivityTimer();
      if (showingSequence) return;

      // Evitar múltiplos cliques rápidos
      if (userSequence.length > 0 && Date.now() - gameState.lastClickTime < 300) {
        return;
      }
      lastClickRef.current = Date.now();

      const newUserSequence = [...userSequence, color];
      const isCorrect = color === sequence[userSequence.length];
      
      // Adicionar classe temporária para feedback visual
      addTempClass(color, styles.buttonPress);
      
      // Adicionar classe para feedback correto/incorreto
      if (isCorrect) {
        addTempClass(color, styles.correctFeedback, 800);
      } else {
        addTempClass(color, styles.incorrectFeedback, 500);
      }
      
      // Remover destaque após um curto período
      setTimeout(() => {
        setGameState(prev => ({
          ...prev,
          activityData: {
            ...prev.activityData,
            sequence_memory: { 
              ...prev.activityData.sequence_memory, 
              activeColorIndex: null 
            }
          }
        }));
      }, 600);

      // Atualizar o estado de clique
      setGameState(prev => ({
        ...prev,
        lastClickTime: Date.now(),
        activityData: {
          ...prev.activityData,
          sequence_memory: { 
            ...prev.activityData.sequence_memory, 
            userSequence: newUserSequence,
            activeColorIndex: color  // Destacar a cor clicada
          }
        },
        activityAttempts: prev.activityAttempts + 1
      }));

      if (!isCorrect) {
        try {
          speak('Ops! Vamos tentar novamente.', { rate: 0.9 });
        } catch (e) {
          console.warn('Falha ao usar TTS para feedback de erro:', e);
        }
        setTimeout(() => {
          setGameState(prev => ({
            ...prev,
            lives: prev.lives - 1,
            activityData: {
              ...prev.activityData,
              sequence_memory: { ...prev.activityData.sequence_memory, userSequence: [], showingSequence: true, activeColorIndex: null }
            }
          }));
          setTimeout(() => {
            setGameState(prev => ({
              ...prev,
              activityData: {
                ...prev.activityData,
                sequence_memory: { ...prev.activityData.sequence_memory, showingSequence: false }
              }
            }));
          }, config.showTime);
        }, 1000);
        return;
      }

      if (newUserSequence.length === sequence.length) {
        const rounds = GAME_CONFIG.activitySettings.roundsPerActivity[gameState.difficulty.toUpperCase()];
        setGameState(prev => ({
          ...prev,
          score: prev.score + GAME_CONFIG.scoring.perfectRound
        }));

        if (currentRound >= rounds) {
          handleActivityCompletion();
        } else {
          try {
            speak(`Rodada ${currentRound} completa!`, { rate: 0.9 });
          } catch (e) {
            console.warn('Falha ao usar TTS para feedback de conclusão:', e);
          }
          setTimeout(() => {
            setGameState(prev => ({
              ...prev,
              activityData: {
                ...prev.activityData,
                sequence_memory: {
                  sequence: shuffleArray(config.elements).slice(0, config.sequenceLength),
                  userSequence: [],
                  showingSequence: true,
                  currentRound: currentRound + 1,
                  activeColorIndex: null
                }
              }
            }));
            setTimeout(() => {
              setGameState(prev => ({
                ...prev,
                activityData: {
                  ...prev.activityData,
                  sequence_memory: { 
                    ...prev.activityData.sequence_memory, 
                    showingSequence: false,
                    activeColorIndex: null 
                  }
                }
              }));
            }, config.showTime);
          }, 2000);
        }
      }
    };

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>🧠 Memória Sequencial</h2>
          <p className={styles.instructions}>
            {showingSequence ? '👀 Observe a sequência!' : '🔄 Repita a sequência de cores na ordem mostrada.'}
          </p>
          <div className={styles.roundInfo}>
            <span className={styles.roundBadge}>
              Rodada {currentRound}/{GAME_CONFIG.activitySettings.roundsPerActivity[gameState.difficulty.toUpperCase()]}
            </span>
          </div>
        </div>
        <div className={styles.sequenceContainer}>
          {showingSequence && (
            <div className={styles.sequenceDisplay}>
              <h3 className={styles.sequenceTitle}>Memorize esta sequência:</h3>
              <div className={styles.sequenceRow}>
                {sequence.map((color, index) => (
                  <motion.div
                    key={`seq-${index}`}
                    className={`${styles.sequenceItem} ${
                      color === '#f44336' ? styles.red :
                      color === '#2196f3' ? styles.blue :
                      color === '#4caf50' ? styles.green :
                      color === '#ffeb3b' ? styles.yellow :
                      color === '#9c27b0' ? styles.purple :
                      color === '#ff9800' ? styles.orange : ''
                    }`}
                    animate={{ 
                      scale: 1.3,
                      boxShadow: '0 8px 25px rgba(255, 255, 255, 0.5)'
                    }}
                    whileHover={{ scale: 1.1 }}
                    transition={{
                      ...ANIMATION_CONFIG.sequenceReveal,
                      delay: index * 0.4
                    }}
                  />
                ))}
              </div>
            </div>
          )}
          {!showingSequence && (
            <div className={styles.colorButtons}>
              {config.elements.map(color => (
                <motion.button
                  key={color}
                  data-color={color}
                  className={`${styles.colorButton} ${
                    color === '#f44336' ? styles.red :
                    color === '#2196f3' ? styles.blue :
                    color === '#4caf50' ? styles.green :
                    color === '#ffeb3b' ? styles.yellow :
                    color === '#9c27b0' ? styles.purple :
                    color === '#ff9800' ? styles.orange : ''
                  } ${activeColorIndex === color ? styles.activeButton : ''}`}
                  onClick={() => handleColorClick(color)}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  animate={userSequence.includes(color) ? { scale: 1.2 } : { scale: 1 }}
                  transition={ANIMATION_CONFIG.colorButtonPress}
                  role="button"
                  tabIndex={0}
                  aria-label={`Cor ${color === '#f44336' ? 'vermelha' : 
                             color === '#2196f3' ? 'azul' : 
                             color === '#4caf50' ? 'verde' : 
                             color === '#ffeb3b' ? 'amarela' : 
                             color === '#9c27b0' ? 'roxa' : 
                             color === '#ff9800' ? 'laranja' : color}`}
                  onKeyDown={(e) => e.key === 'Enter' && handleColorClick(color)}
                >
                  {/* Removido o texto que mostrava a cor */}
                </motion.button>
              ))}
            </div>
          )}
          {!showingSequence && (
            <div className={styles.feedbackContainer}>
              <div className={styles.progressBar}>
                <div 
                  className={styles.progressFill} 
                  style={{ width: `${(userSequence.length / sequence.length) * 100}%` }}
                ></div>
              </div>
              <div className={styles.feedback}>
                <span>Progresso: </span>
                <span className={styles.progressCounter}>{userSequence.length}/{sequence.length}</span>
              </div>
              {userSequence.length > 0 && (
                <div className={styles.sequenceHistory}>
                  {userSequence.map((color, index) => (
                    <div 
                      key={`history-${index}`} 
                      className={`${styles.historyDot} ${
                        color === '#f44336' ? styles.red :
                        color === '#2196f3' ? styles.blue :
                        color === '#4caf50' ? styles.green :
                        color === '#ffeb3b' ? styles.yellow :
                        color === '#9c27b0' ? styles.purple :
                        color === '#ff9800' ? styles.orange : ''
                      }`}
                    ></div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  }, [gameState.activityData.sequence_memory, gameState.difficulty, speak, playSound, shuffleArray, startActivityTimer]);

  const renderSpatialLocationActivity = useCallback(() => {
    // Verificar se activityData e spatial_location existem
    if (!gameState.activityData || !gameState.activityData.spatial_location) {
      const initialData = initializeActivityData('spatial_location', gameState.difficulty);
      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          spatial_location: initialData
        }
      }));
      return <div>Inicializando atividade...</div>;
    }

    const { grid, targetPositions, userSelections, showingPositions, currentRound } = gameState.activityData.spatial_location;
    const config = ACTIVITY_DIFFICULTY_CONFIG.spatial_location[gameState.difficulty];

    if (!grid.length) {
      const gridSize = config.gridSize * config.gridSize;
      const newGrid = Array(gridSize).fill(null).map((_, i) => ({ id: i, revealed: false }));
      const newTargets = shuffleArray([...Array(gridSize).keys()]).slice(0, config.targetCount);

      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          spatial_location: {
            grid: newGrid,
            targetPositions: newTargets,
            userSelections: [],
            showingPositions: true,
            currentRound: 1
          }
        }
      }));
      speak('Memorize as posições destacadas!', { rate: 0.9 });
      setTimeout(() => {
        setGameState(prev => ({
          ...prev,
          activityData: {
            ...prev.activityData,
            spatial_location: { ...prev.activityData.spatial_location, showingPositions: false }
          }
        }));
        speak('Agora clique nas posições destacadas.', { rate: 0.9 });
      }, config.showTime);
      return <div>Preparando grid...</div>;
    }

    const handleCellClick = (position) => {
      startActivityTimer();
      if (showingPositions || userSelections.includes(position)) return;

      const newSelections = [...userSelections, position];
      const isCorrect = targetPositions.includes(position);
      playSound(isCorrect ? 'correct' : 'incorrect');

      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          spatial_location: { ...prev.activityData.spatial_location, userSelections: newSelections }
        },
        activityAttempts: prev.activityAttempts + 1
      }));

      if (newSelections.length === targetPositions.length) {
        const correctSelections = newSelections.filter(pos => targetPositions.includes(pos));
        const score = Math.round((correctSelections.length / targetPositions.length) * GAME_CONFIG.scoring.perfectRound);
        const rounds = GAME_CONFIG.activitySettings.roundsPerActivity[gameState.difficulty.toUpperCase()];

        setGameState(prev => ({
          ...prev,
          score: prev.score + score
        }));

        // Registrar dados da rodada usando o coletor especializado
        if (collectorsHub && collectorsHub.collectors.spatialLocation) {
          try {
            collectorsHub.collectors.spatialLocation.collect({
              originalLocations: targetPositions,
              userLocations: newSelections,
              gridSize: config.gridSize,
              responseTime: Date.now() - (gameState.startTime || Date.now()),
              accuracy: correctSelections.length / targetPositions.length,
              difficulty: gameState.difficulty,
              round: currentRound,
              timestamp: Date.now()
            });
            console.log(`🧠 Dados da rodada ${currentRound} de localização espacial coletados`);
          } catch (e) {
            console.warn('⚠️ Erro ao coletar dados de localização espacial:', e);
          }
        }
        
        if (currentRound >= rounds) {
          setGameState(prev => ({
            ...prev,
            activityData: {
              ...prev.activityData,
              spatial_location: { ...prev.activityData.spatial_location, completed: true }
            }
          }));
          speak(score >= 80 ? 'Excelente memória espacial!' : 'Bom trabalho, continue praticando!', { rate: 0.9 });
          setTimeout(handleActivityCompletion, 2000);
        } else {
          try {
            speak(`Rodada ${currentRound} completa!`, { rate: 0.9 });
          } catch (e) {
            console.warn('Falha ao usar TTS para feedback de conclusão:', e);
          }
          setTimeout(() => {
            // Preparar próxima rodada
            const gridSize = config.gridSize * config.gridSize;
            const newGrid = Array(gridSize).fill(null).map((_, i) => ({ id: i, revealed: false }));
            const newTargets = shuffleArray([...Array(gridSize).keys()]).slice(0, config.targetCount);
            
            setGameState(prev => ({
              ...prev,
              activityData: {
                ...prev.activityData,
                spatial_location: {
                  grid: newGrid,
                  targetPositions: newTargets,
                  userSelections: [],
                  showingPositions: true,
                  currentRound: currentRound + 1
                }
              }
            }));
            
            speak('Memorize as novas posições!', { rate: 0.9 });
            setTimeout(() => {
              setGameState(prev => ({
                ...prev,
                activityData: {
                  ...prev.activityData,
                  spatial_location: { ...prev.activityData.spatial_location, showingPositions: false }
                }
              }));
              speak('Agora clique nas posições destacadas.', { rate: 0.9 });
            }, config.showTime);
          }, 2000);
        }
      }
    };

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>📍 Posições Espaciais</h2>
          <p className={styles.instructions}>
            {showingPositions ? 'Memorize as posições!' : 'Clique nas posições destacadas.'}
          </p>
          <div className={styles.roundInfo}>
            <span className={styles.roundBadge}>
              Rodada {currentRound}/{GAME_CONFIG.activitySettings.roundsPerActivity[gameState.difficulty.toUpperCase()]}
            </span>
          </div>
        </div>
        <div className={styles.spatialContainer}>
          <div className={styles.spatialGrid} style={{ gridTemplateColumns: `repeat(${config.gridSize}, 1fr)` }}>
            {grid.map(cell => (
              <motion.div
                key={`cell-${cell.id}`}
                className={`${styles.spatialCell} ${
                  showingPositions && targetPositions.includes(cell.id) ? styles.highlighted :
                  userSelections.includes(cell.id) ? (targetPositions.includes(cell.id) ? styles.correct : styles.incorrect) : ''
                }`}
                onClick={() => handleCellClick(cell.id)}
                whileHover={{ scale: showingPositions ? 1 : 1.05 }}
                whileTap={{ scale: showingPositions ? 1 : 0.95 }}
                role="button"
                tabIndex={0}
                aria-label={`Célula ${cell.id + 1}`}
                onKeyDown={(e) => e.key === 'Enter' && handleCellClick(cell.id)}
              >
                {userSelections.includes(cell.id) && !showingPositions && (
                  <span>{targetPositions.includes(cell.id) ? '✓' : '✗'}</span>
                )}
              </motion.div>
            ))}
          </div>
          {!showingPositions && userSelections.length > 0 && (
            <div className={styles.feedback}>
              Seleções: {userSelections.length}/{targetPositions.length}
            </div>
          )}
        </div>
      </div>
    );
  }, [gameState.activityData.spatial_location, gameState.difficulty, gameState.startTime, speak, playSound, shuffleArray, startActivityTimer, collectorsHub, handleActivityCompletion]);

  const renderImageReconstructionActivity = useCallback(() => {
    // Verificar se activityData e image_reconstruction existem
    if (!gameState.activityData || !gameState.activityData.image_reconstruction) {
      const initialData = initializeActivityData('image_reconstruction', gameState.difficulty);
      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          image_reconstruction: initialData
        }
      }));
      return <div>Inicializando atividade...</div>;
    }

    const { pieces, targetImage, userArrangement, showPreview, completed } = gameState.activityData.image_reconstruction;
    const config = ACTIVITY_DIFFICULTY_CONFIG.image_reconstruction[gameState.difficulty];

    if (!pieces.length) {
      const selectedTheme = sequenceThemes[Math.floor(Math.random() * sequenceThemes.length)];
      const selectedEmojis = shuffleArray([...selectedTheme.emojis]).slice(0, config.pieces);
      const imagePieces = selectedEmojis.map((emoji, index) => ({ id: `piece-${index}`, emoji, position: index }));
      const target = [...imagePieces];
      const shuffledPieces = shuffleArray([...imagePieces]);

      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          image_reconstruction: {
            pieces: shuffledPieces,
            targetImage: target,
            userArrangement: [],
            showPreview: true,
            completed: false,
            themeName: selectedTheme.name,
            currentRound: 1
          }
        }
      }));
      speak(`Observe o padrão de ${selectedTheme.name}.`, { rate: 0.9 });
      setTimeout(() => {
        setGameState(prev => ({
          ...prev,
          activityData: {
            ...prev.activityData,
            image_reconstruction: { ...prev.activityData.image_reconstruction, showPreview: false }
          }
        }));
        speak('Reconstrua o padrão!', { rate: 0.9 });
      }, 5000);
      return <div>Preparando imagem...</div>;
    }

    const handlePieceDrag = (pieceId, targetIndex) => {
      startActivityTimer();
      if (completed) return;

      const draggedPiece = pieces.find(p => p.id === pieceId);
      if (!draggedPiece || userArrangement.some(item => item.position === targetIndex)) return;

      const newArrangement = [...userArrangement.filter(item => item.piece.id !== pieceId), { piece: draggedPiece, position: targetIndex }];
      playSound('click');

      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          image_reconstruction: { ...prev.activityData.image_reconstruction, userArrangement: newArrangement }
        },
        activityAttempts: prev.activityAttempts + 1
      }));

      if (newArrangement.length === pieces.length) {
        const correctPieces = newArrangement.filter(item => item.piece.id === targetImage[item.position].id).length;
        const score = Math.round((correctPieces / pieces.length) * GAME_CONFIG.scoring.perfectRound);
        const { currentRound } = gameState.activityData.image_reconstruction;
        const config = ACTIVITY_DIFFICULTY_CONFIG.image_reconstruction[gameState.difficulty];
        // Número total de rodadas baseado na dificuldade
        const rounds = gameState.difficulty === 'easy' ? 2 : gameState.difficulty === 'medium' ? 3 : 4;
        
        // Registrar dados da rodada usando o coletor especializado
        if (collectorsHub && collectorsHub.collectors.imageReconstruction) {
          try {
            collectorsHub.collectors.imageReconstruction.collect({
              originalImage: targetImage,
              reconstructedImage: newArrangement.map(item => item.piece),
              imageComplexity: config.imageComplexity,
              responseTime: Date.now() - (gameState.startTime || Date.now()),
              accuracy: (correctPieces / pieces.length) * 100,
              difficulty: gameState.difficulty,
              visualElements: pieces.length,
              timestamp: new Date().toISOString(),
              round: currentRound,
              totalRounds: rounds
            });
            console.log(`🖼️ Dados da rodada ${currentRound} de reconstrução de imagem coletados`);
          } catch (e) {
            console.warn('⚠️ Erro ao coletar dados de reconstrução de imagem:', e);
          }
        }
        
        setGameState(prev => ({
          ...prev,
          score: prev.score + score,
          activityData: {
            ...prev.activityData,
            image_reconstruction: { 
              ...prev.activityData.image_reconstruction, 
              completed: currentRound >= rounds,
              score
            }
          }
        }));
        
        speak(score > 90 ? 'Perfeito!' : 'Bom trabalho!', { rate: 0.9 });
        
        if (currentRound >= rounds) {
          setTimeout(handleActivityCompletion, 2000);
        } else {
          try {
            speak(`Rodada ${currentRound} completa!`, { rate: 0.9 });
          } catch (e) {
            console.warn('Falha ao usar TTS para feedback de conclusão:', e);
          }
          setTimeout(() => {
            // Preparar próxima rodada
            const selectedTheme = sequenceThemes[Math.floor(Math.random() * sequenceThemes.length)];
            const selectedEmojis = shuffleArray([...selectedTheme.emojis]).slice(0, config.pieces);
            const imagePieces = selectedEmojis.map((emoji, index) => ({ id: `piece-${index}`, emoji, position: index }));
            const target = [...imagePieces];
            const shuffledPieces = shuffleArray([...imagePieces]);
            
            setGameState(prev => ({
              ...prev,
              activityData: {
                ...prev.activityData,
                image_reconstruction: {
                  pieces: shuffledPieces,
                  targetImage: target,
                  userArrangement: [],
                  showPreview: true,
                  completed: false,
                  themeName: selectedTheme.name,
                  currentRound: currentRound + 1
                }
              }
            }));
            
            speak(`Observe o padrão de ${selectedTheme.name}.`, { rate: 0.9 });
            setTimeout(() => {
              setGameState(prev => ({
                ...prev,
                activityData: {
                  ...prev.activityData,
                  image_reconstruction: { ...prev.activityData.image_reconstruction, showPreview: false }
                }
              }));
              speak('Reconstrua o padrão!', { rate: 0.9 });
            }, 5000);
          }, 2000);
        }
      }
    };

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>🖼️ Reconstrução de Imagem</h2>
          <p className={styles.roundInfo}>Rodada {gameState.activityData.image_reconstruction.currentRound}</p>
          <p className={styles.instructions}>
            {showPreview ? 'Observe o padrão!' : completed ? 'Atividade completada!' : 'Reconstrua o padrão.'}
          </p>
        </div>
        <div className={styles.imageContainer}>
          {showPreview ? (
            <div className={styles.imagePreview}>
              <div className={styles.emojiPreviewGrid} style={{ gridTemplateColumns: `repeat(${Math.sqrt(pieces.length)}, 1fr)` }}>
                {targetImage.map(piece => (
                  <div key={piece.id} className={styles.previewItem}>{piece.emoji}</div>
                ))}
              </div>
            </div>
          ) : (
            <>
              <div className={styles.reconstructionGrid} style={{ gridTemplateColumns: `repeat(${Math.sqrt(pieces.length)}, 1fr)` }}>
                {Array(pieces.length).fill(null).map((_, index) => {
                  const arrangedPiece = userArrangement.find(item => item.position === index);
                  return (
                    <div
                      key={`grid-${index}`}
                      className={styles.dropZone}
                      onClick={() => arrangedPiece && handlePieceDrag(arrangedPiece.piece.id, index)}
                      role="button"
                      tabIndex={0}
                      aria-label={arrangedPiece ? `Posição ocupada por ${arrangedPiece.piece.emoji}` : `Posição ${index + 1} vazia`}
                      onKeyDown={(e) => e.key === 'Enter' && arrangedPiece && handlePieceDrag(arrangedPiece.piece.id, index)}
                    >
                      {arrangedPiece && (
                        <motion.div
                          className={styles.puzzlePiece}
                          initial={{ scale: 0.8, opacity: 0 }}
                          animate={{ scale: 1, opacity: 1 }}
                          transition={{ duration: 0.3 }}
                        >
                          {arrangedPiece.piece.emoji}
                        </motion.div>
                      )}
                    </div>
                  );
                })}
              </div>
              <div className={styles.availablePieces}>
                {pieces.map(piece => {
                  if (userArrangement.some(item => item.piece.id === piece.id)) return null;
                  return (
                    <motion.div
                      key={piece.id}
                      className={styles.pieceContainer}
                      onClick={() => {
                        const occupied = userArrangement.map(item => item.position);
                        const available = Array(pieces.length).fill(null).map((_, i) => i).filter(pos => !occupied.includes(pos));
                        if (available.length) handlePieceDrag(piece.id, available[0]);
                      }}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      role="button"
                      tabIndex={0}
                      aria-label={`Peça ${piece.emoji}`}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          const occupied = userArrangement.map(item => item.position);
                          const available = Array(pieces.length).fill(null).map((_, i) => i).filter(pos => !occupied.includes(pos));
                          if (available.length) handlePieceDrag(piece.id, available[0]);
                        }
                      }}
                    >
                      {piece.emoji}
                    </motion.div>
                  );
                })}
              </div>
              {userArrangement.length > 0 && !completed && (
                <div className={styles.feedback}>Progresso: {userArrangement.length}/{pieces.length}</div>
              )}
              {completed && (
                <div className={styles.feedback}>
                  <p>Pontuação final: {gameState.activityData.image_reconstruction.score}%</p>
                  <p>Atividade concluída com sucesso!</p>
                </div>
              )}
              {!completed && gameState.activityData.image_reconstruction.score && (
                <div className={styles.feedback}>
                  <p>Pontuação da rodada: {gameState.activityData.image_reconstruction.score}%</p>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    );
  }, [gameState.activityData.image_reconstruction, gameState.difficulty, gameState.startTime, speak, playSound, shuffleArray, startActivityTimer, sequenceThemes, collectorsHub, handleActivityCompletion]);

  const renderNumberSequenceActivity = useCallback(() => {
    // Verificar se activityData e number_sequence existem
    if (!gameState.activityData || !gameState.activityData.number_sequence) {
      const initialData = initializeActivityData('number_sequence', gameState.difficulty);
      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          number_sequence: initialData
        }
      }));
      return <div>Inicializando atividade...</div>;
    }

    const { numbers, userAnswer, missingNumber, answered, currentRound, completed } = gameState.activityData.number_sequence;
    const config = ACTIVITY_DIFFICULTY_CONFIG.number_sequence[gameState.difficulty];

    if (!numbers.length && !answered) {
      const start = Math.floor(Math.random() * 10) + 1;
      const diff = Math.floor(Math.random() * 5) + 1;
      const sequence = Array(config.sequenceLength).fill(null).map((_, i) => start + i * diff);
      const visibleSequence = [...sequence];
      const missing = visibleSequence.pop();

      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          number_sequence: {
            numbers: visibleSequence,
            fullSequence: sequence,
            missingNumber: missing,
            userAnswer: '',
            answered: false
          }
        }
      }));
      speak('Descubra o próximo número da sequência!', { rate: 0.9 });
      return <div>Preparando sequência...</div>;
    }

    const handleSubmit = () => {
      startActivityTimer();
      const isCorrect = parseInt(userAnswer) === missingNumber;
      // Número total de rodadas baseado na dificuldade
      const rounds = gameState.difficulty === 'easy' ? 2 : gameState.difficulty === 'medium' ? 3 : 4;
      
      // Registrar dados da rodada usando o coletor especializado
      if (collectorsHub && collectorsHub.collectors.numberSequence) {
        try {
          collectorsHub.collectors.numberSequence.collect({
            originalSequence: [...numbers, missingNumber],
            userSequence: [...numbers, parseInt(userAnswer)],
            sequenceType: config.pattern,
            responseTime: Date.now() - (gameState.startTime || Date.now()),
            accuracy: isCorrect ? 100 : 0,
            difficulty: gameState.difficulty,
            numericalProperties: {
              length: numbers.length + 1,
              maxNumber: Math.max(...numbers, missingNumber),
              pattern: config.pattern
            },
            timestamp: new Date().toISOString(),
            round: currentRound,
            totalRounds: rounds
          });
          console.log(`🔢 Dados da rodada ${currentRound} de sequência numérica coletados`);
        } catch (e) {
          console.warn('⚠️ Erro ao coletar dados de sequência numérica:', e);
        }
      }

      const shouldComplete = currentRound >= rounds;
      
      setGameState(prev => ({
        ...prev,
        score: prev.score + (isCorrect ? GAME_CONFIG.scoring.perfectRound : 0),
        activityAttempts: prev.activityAttempts + 1,
        activityData: {
          ...prev.activityData,
          number_sequence: { 
            ...prev.activityData.number_sequence, 
            answered: true, 
            isCorrect,
            completed: shouldComplete
          }
        }
      }));
      
      speak(isCorrect ? 'Correto!' : `Incorreto. Era ${missingNumber}.`, { rate: 0.9 });
      
      if (shouldComplete) {
        setTimeout(handleActivityCompletion, 2000);
      } else if (isCorrect) {
        try {
          speak(`Rodada ${currentRound} completa!`, { rate: 0.9 });
        } catch (e) {
          console.warn('Falha ao usar TTS para feedback de conclusão:', e);
        }
        setTimeout(() => {
          // Preparar próxima rodada
          const start = Math.floor(Math.random() * 10) + 1;
          const diff = Math.floor(Math.random() * 5) + 1;
          const sequence = Array(config.sequenceLength).fill(null).map((_, i) => start + i * diff);
          const visibleSequence = [...sequence];
          const missing = visibleSequence.pop();
          
          setGameState(prev => ({
            ...prev,
            activityData: {
              ...prev.activityData,
              number_sequence: {
                numbers: visibleSequence,
                fullSequence: sequence,
                missingNumber: missing,
                userAnswer: '',
                answered: false,
                currentRound: currentRound + 1
              }
            }
          }));
          
          speak('Descubra o próximo número da nova sequência!', { rate: 0.9 });
        }, 2000);
      } else {
        // Se errou, conclui a atividade
        setTimeout(handleActivityCompletion, 2000);
      }
    };

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>🔢 Sequência Numérica</h2>
          <p className={styles.roundInfo}>Rodada {currentRound}</p>
          <p className={styles.instructions}>
            {answered ? (gameState.activityData.number_sequence.isCorrect ? 'Correto!' : `Incorreto. Era ${missingNumber}.`) : 'Qual é o próximo número?'}
          </p>
        </div>
        <div className={styles.numberSequenceContainer}>
          <div className={styles.numberRow}>
            {numbers.map((num, index) => (
              <div key={`num-${index}`} className={styles.numberCard}>{num}</div>
            ))}
            <div className={`${styles.numberCard} ${styles.missingNumber}`}>{answered ? missingNumber : '?'}</div>
          </div>
          {!answered && (
            <div className={styles.answerContainer}>
              <input
                type="number"
                value={userAnswer}
                onChange={e => setGameState(prev => ({
                  ...prev,
                  activityData: {
                    ...prev.activityData,
                    number_sequence: { ...prev.activityData.number_sequence, userAnswer: e.target.value }
                  }
                }))}
                className={styles.numberInput}
                placeholder="?"
                aria-label="Resposta da sequência numérica"
              />
              <button
                className={styles.submitButton}
                onClick={handleSubmit}
                disabled={!userAnswer}
                role="button"
                tabIndex={0}
                aria-label="Verificar resposta"
                onKeyDown={(e) => e.key === 'Enter' && userAnswer && handleSubmit()}
              >
                Verificar
              </button>
            </div>
          )}
        </div>
      </div>
    );
  }, [gameState.activityData.number_sequence, gameState.difficulty, gameState.startTime, speak, startActivityTimer, collectorsHub, handleActivityCompletion]);

  if (showStartScreen) {
    return (
      <GameStartScreen
        gameTitle="Jogo da Memória"
        gameDescription="Desenvolva sua memória visual de forma divertida"
        gameIcon="🧠"
        difficulties={[
          { id: 'easy', name: 'Fácil', description: '4 pares\nIdeal para iniciantes', icon: '😊' },
          { id: 'medium', name: 'Médio', description: '6 pares\nDesafio equilibrado', icon: '🎯' },
          { id: 'hard', name: 'Avançado', description: '8 pares\nPara especialistas', icon: '🚀' }
        ]}
        onStart={initializeGame}
        onBack={onBack}
      />
    );
  }

  return (
    <div className={styles.memoryGame}>
      <div className={styles.gameHeader}>
        <h1 className={styles.gameTitle}>
          🧠 Jogo da Memória V3
          <div style={{ fontSize: '0.7rem', opacity: 0.8 }}>{gameState.difficulty}</div>
        </h1>
        <StandardTTSButton
          ttsActive={ttsActive}
          toggleTTS={toggleTTS}
          size="normal"
          position="header"
        />
      </div>
      <div className={styles.gameStats}>
        <div className={styles.statCard}>
          <div className={styles.statValue}>{gameState.score}</div>
          <div className={styles.statLabel}>Pontos</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statValue}>{gameState.lives}</div>
          <div className={styles.statLabel}>Vidas</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statValue}>{formatTime(gameState.elapsedTime)}</div>
          <div className={styles.statLabel}>Tempo</div>
        </div>
      </div>
      
      {/* Menu de atividades - VISÍVEL SEMPRE como no Contagem de Números */}
      <div className={styles.activityMenu}>
        {GAME_CONFIG.activityRotation.map(activity => (
          <button
            key={activity}
            className={`${styles.activityButton} ${gameState.currentActivity === activity ? styles.active : ''}`}
            onClick={() => selectActivity(activity)}
            role="button"
            tabIndex={0}
            aria-label={`Selecionar ${ACTIVITY_CONFIG[activity].name}`}
          >
            <span className={styles.activityIcon}>{ACTIVITY_CONFIG[activity].icon}</span>
            <span className={styles.activityName}>{ACTIVITY_CONFIG[activity].name}</span>
            {gameState.currentActivity === activity && (
              <span className={styles.activeIndicator}>●</span>
            )}
          </button>
        ))}
      </div>
      {renderActivityInterface()}
      <div className={styles.gameControls}>
        <button
          className={styles.controlButton}
          onClick={explainGame}
          role="button"
          tabIndex={0}
          aria-label="Explicar o jogo"
          onKeyDown={(e) => e.key === 'Enter' && explainGame()}
        >
          Explicar
        </button>
        <button
          className={styles.controlButton}
          onClick={() => speak(`Instruções para ${ACTIVITY_CONFIG[gameState.currentActivity].name}.`)}
          role="button"
          tabIndex={0}
          aria-label="Repetir instruções"
          onKeyDown={(e) => e.key === 'Enter' && speak(`Instruções para ${ACTIVITY_CONFIG[gameState.currentActivity].name}.`)}
        >
          Repetir
        </button>
        <button
          className={styles.controlButton}
          onClick={handleRestart}
          role="button"
          tabIndex={0}
          aria-label="Reiniciar jogo"
          onKeyDown={(e) => e.key === 'Enter' && handleRestart()}
        >
          Reiniciar
        </button>
        <button
          className={styles.controlButton}
          onClick={onBack}
          role="button"
          tabIndex={0}
          aria-label="Voltar ao menu"
          onKeyDown={(e) => e.key === 'Enter' && onBack()}
        >
          Voltar
        </button>
      </div>
    </div>
  );
}

export default MemoryGame;