/**
 * 💰 Portal Betina V3 - Sistema de Preços e Planos
 * Configurações de planos de acesso aos dashboards premium
 */

export const PRICING_PLANS = {
  basic: {
    id: 'basic',
    name: 'Plano Básico',
    price: 97.00,
    currency: 'BRL',
    period: 'mensal',
    description: 'Acesso básico aos dashboards essenciais',
    features: [
      '📊 Dashboard de Performance',
      '📈 Relatórios básicos de progresso',
      '🎯 Métricas de jogos individuais',
      '📱 Acesso via web',
      '💬 Suporte por email'
    ],
    limitations: [
      'Até 3 perfis de usuário',
      'Histórico de 30 dias',
      'Relatórios mensais'
    ],
    dashboardAccess: [
      'performance',
      'basic_metrics'
    ],
    popular: false
  },

  premium: {
    id: 'premium',
    name: 'Plano Premium',
    price: 197.00,
    currency: 'BRL',
    period: 'mensal',
    description: 'Acesso completo com análises avançadas de IA',
    features: [
      '🧠 Análise IA Avançada',
      '📊 Dashboard Neuropedagógico',
      '🎮 Métricas Multissensoriais',
      '📈 Relatórios detalhados com insights',
      '🔄 Sincronização em tempo real',
      '📱 App mobile (em breve)',
      '💬 Suporte prioritário',
      '🎯 Recomendações personalizadas'
    ],
    limitations: [
      'Até 10 perfis de usuário',
      'Histórico de 12 meses'
    ],
    dashboardAccess: [
      'performance',
      'ai_analysis',
      'neuropedagogical',
      'multisensory_metrics',
      'advanced_reports'
    ],
    popular: true
  },

  professional: {
    id: 'professional',
    name: 'Plano Profissional',
    price: 397.00,
    currency: 'BRL',
    period: 'mensal',
    description: 'Solução completa para terapeutas e instituições',
    features: [
      '🏥 Gestão de múltiplos pacientes',
      '👥 Colaboração em equipe',
      '📋 Relatórios para laudos',
      '🔒 Conformidade LGPD',
      '📊 Analytics institucionais',
      '🎓 Treinamentos exclusivos',
      '📞 Suporte telefônico',
      '🔧 Customizações avançadas',
      '📤 Exportação de dados',
      '🔄 Integração com sistemas externos'
    ],
    limitations: [
      'Usuários ilimitados',
      'Histórico completo',
      'Backup automático'
    ],
    dashboardAccess: [
      'performance',
      'ai_analysis',
      'neuropedagogical',
      'multisensory_metrics',
      'advanced_reports',
      'institutional_analytics',
      'team_management',
      'custom_reports'
    ],
    popular: false
  }
}

export const PAYMENT_CONFIG = {
  methods: {
    pix: {
      enabled: true,
      name: 'PIX',
      description: 'Pagamento instantâneo via PIX',
      processingTime: 'Imediato',
      icon: '💳'
    }
  },
  
  pixConfig: {
    merchantName: 'Portal Betina V3',
    merchantCity: 'São Paulo',
    merchantCEP: '01310-100',
    pixKey: '<EMAIL>', // Chave PIX da empresa
    description: 'Assinatura Portal Betina V3'
  },

  discounts: {
    annual: {
      percentage: 20,
      description: 'Desconto de 20% no pagamento anual'
    },
    student: {
      percentage: 30,
      description: 'Desconto estudantil (com comprovação)'
    },
    institutional: {
      percentage: 15,
      description: 'Desconto para instituições (5+ licenças)'
    }
  }
}

export const REGISTRATION_FIELDS = {
  personal: {
    firstName: {
      required: true,
      label: 'Nome',
      placeholder: 'Seu primeiro nome',
      validation: 'min:2|max:50'
    },
    lastName: {
      required: true,
      label: 'Sobrenome',
      placeholder: 'Seu sobrenome',
      validation: 'min:2|max:50'
    },
    email: {
      required: true,
      label: 'Email',
      placeholder: '<EMAIL>',
      validation: 'email'
    },
    phone: {
      required: false,
      label: 'Telefone (opcional)',
      placeholder: '11999999999',
      validation: 'min:10|max:11'
    }
  },

  usage: {
    intendedUse: {
      required: true,
      label: 'Como pretende usar o sistema?',
      type: 'select',
      options: [
        'Acompanhamento de filho(a)',
        'Atendimento profissional',
        'Pesquisa acadêmica',
        'Uso institucional',
        'Desenvolvimento profissional'
      ]
    }
  }
}

export const APPROVAL_STATUS = {
  PENDING: 'pending',
  PAYMENT_PENDING: 'payment_pending',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  EXPIRED: 'expired'
}

export const APPROVAL_MESSAGES = {
  [APPROVAL_STATUS.PENDING]: {
    title: 'Cadastro em Análise',
    message: 'Seu cadastro está sendo analisado pela nossa equipe. Você receberá um email em até 24 horas.',
    color: 'orange'
  },
  [APPROVAL_STATUS.PAYMENT_PENDING]: {
    title: 'Pagamento Pendente',
    message: 'Cadastro aprovado! Realize o pagamento via PIX para ativar sua conta.',
    color: 'blue'
  },
  [APPROVAL_STATUS.APPROVED]: {
    title: 'Conta Ativada',
    message: 'Parabéns! Sua conta foi ativada com sucesso. Você já pode acessar os dashboards.',
    color: 'green'
  },
  [APPROVAL_STATUS.REJECTED]: {
    title: 'Cadastro Rejeitado',
    message: 'Infelizmente seu cadastro não foi aprovado. Entre em contato para mais informações.',
    color: 'red'
  },
  [APPROVAL_STATUS.EXPIRED]: {
    title: 'Cadastro Expirado',
    message: 'O prazo para pagamento expirou. Faça um novo cadastro se ainda tiver interesse.',
    color: 'gray'
  }
}

/**
 * Função para calcular preço com desconto
 */
export const calculatePrice = (planId, discountType = null, isAnnual = false) => {
  const plan = PRICING_PLANS[planId]
  if (!plan) return 0

  let price = plan.price
  
  // Aplicar desconto anual
  if (isAnnual) {
    price = price * 12 * (1 - PAYMENT_CONFIG.discounts.annual.percentage / 100)
  }
  
  // Aplicar outros descontos
  if (discountType && PAYMENT_CONFIG.discounts[discountType]) {
    price = price * (1 - PAYMENT_CONFIG.discounts[discountType].percentage / 100)
  }
  
  return price
}

/**
 * Função para gerar código PIX
 */
export const generatePixCode = (amount, planId, userId) => {
  const config = PAYMENT_CONFIG.pixConfig
  const description = `${config.description} - ${PRICING_PLANS[planId]?.name}`
  
  // Em produção, usar biblioteca oficial do PIX
  const pixCode = `00020126580014BR.GOV.BCB.PIX0136${config.pixKey}0208${description}5204000053039865802BR5925${config.merchantName}6009${config.merchantCity}61080100000062070503***6304`
  
  return {
    code: pixCode,
    qrCode: `data:image/svg+xml;base64,${btoa(`<svg>QR Code para ${amount}</svg>`)}`, // Placeholder
    amount,
    expiresAt: new Date(Date.now() + 30 * 60 * 1000), // 30 minutos
    reference: `PIX-${planId}-${userId}-${Date.now()}`
  }
}
