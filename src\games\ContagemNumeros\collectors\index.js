/**
 * 🔢 NUMBER COUNTING COLLECTORS HUB V3
 * Hub integrador para todos os coletores especializados do NumberCounting V3
 * Suporte para 6 atividades diversificadas
 * Portal Betina V3
 */

import { NumericalCognitionCollector } from './NumericalCognitionCollector.js';
import { AttentionFocusCollector } from './AttentionFocusCollector.js';
import { VisualProcessingCollector } from './VisualProcessingCollector.js';
import { MathematicalReasoningCollector } from './MathematicalReasoningCollector.js';
import { ErrorPatternCollector } from './ErrorPatternCollector.js';
import { EstimationSkillsCollector } from './EstimationSkillsCollector.js';
import { SequenceAnalysisCollector } from './SequenceAnalysisCollector.js';
import { ComparisonSkillsCollector } from './ComparisonSkillsCollector.js';
import { SoundMatchingCollector } from './SoundMatchingCollector.js';
import { PatternRecognitionCollector } from './PatternRecognitionCollector.js';

export class NumberCountingCollectorsHub {
  constructor() {
    this._collectors = {
      numericalCognition: new NumericalCognitionCollector(),
      attentionFocus: new AttentionFocusCollector(),
      visualProcessing: new VisualProcessingCollector(),
      mathematicalReasoning: new MathematicalReasoningCollector(),
      errorPattern: new ErrorPatternCollector(),
      estimationSkills: new EstimationSkillsCollector(),
      sequenceAnalysis: new SequenceAnalysisCollector(),
      comparisonSkills: new ComparisonSkillsCollector(),
      soundMatching: new SoundMatchingCollector(),
      patternRecognition: new PatternRecognitionCollector()
    };

    this.analysisHistory = [];
    this.currentSession = null;
    this.performanceBaseline = null;
    this.cognitiveProfile = null;

    // 🎯 CONFIGURAÇÕES ESPECÍFICAS V3 - PARA 6 ATIVIDADES
    this.gameSpecificConfig = {
      minAttempts: 3,
      maxAnalysisTime: 5000,
      significantChangeThreshold: 0.15,
      adaptiveDifficultyEnabled: true,

      // 🎯 CONFIGURAÇÕES POR ATIVIDADE V3
      activityConfigs: {
        number_counting: {
          focusMetric: 'counting_accuracy',
          timeWeight: 0.3,
          accuracyWeight: 0.7,
          patterns: ['sequential_errors', 'magnitude_errors']
        },
        sound_matching: {
          focusMetric: 'auditory_processing',
          timeWeight: 0.4,
          accuracyWeight: 0.6,
          patterns: ['audio_confusion', 'delay_patterns']
        },
        number_estimation: {
          focusMetric: 'estimation_accuracy',
          timeWeight: 0.2,
          accuracyWeight: 0.8,
          patterns: ['underestimation', 'overestimation', 'magnitude_bias']
        },
        sequence_completion: {
          focusMetric: 'pattern_recognition',
          timeWeight: 0.5,
          accuracyWeight: 0.5,
          patterns: ['sequence_logic', 'arithmetic_progression']
        },
        number_comparison: {
          focusMetric: 'comparison_logic',
          timeWeight: 0.3,
          accuracyWeight: 0.7,
          patterns: ['magnitude_comparison', 'relative_errors']
        },
        pattern_recognition: {
          focusMetric: 'abstract_reasoning',
          timeWeight: 0.6,
          accuracyWeight: 0.4,
          patterns: ['pattern_complexity', 'abstraction_level']
        }
      }
    };

    // 🧠 INICIALIZAR ANÁLISE COMPORTAMENTAL V3
    this.behavioralAnalysis = {
      activityPreferences: {},
      learningProgression: {},
      cognitivePatterns: {},
      adaptiveRecommendations: []
    };
  }

  /**
   * Getter para coletores - necessário para GameSpecificProcessors
   */
  get collectors() {
    return this._collectors;
  }

  /**
   * 🎯 EXECUTA ANÁLISE COMPLETA V3 - SUPORTE PARA 6 ATIVIDADES
   */
  async runCompleteAnalysis(gameData) {
    try {
      console.log('🔢 Iniciando análise completa do NumberCounting V3...');

      if (!this.validateGameData(gameData)) {
        throw new Error('Dados do jogo inválidos para análise V3');
      }

      const startTime = Date.now();

      // 🎯 PREPARAR DADOS ESPECÍFICOS PARA CADA COLETOR V3
      const collectorData = this.prepareCollectorDataV3(gameData);

      // 🎯 ANÁLISE POR ATIVIDADE V3
      const activityAnalysis = await this.analyzeByActivity(gameData);

      // Executar todos os coletores em paralelo para eficiência
      const analysisPromises = [
        this.collectors.numericalCognition.analyze(collectorData.numericalCognition),
        this.collectors.attentionFocus.analyze(collectorData.attentionFocus),
        this.collectors.visualProcessing.analyze(collectorData.visualProcessing),
        this.collectors.mathematicalReasoning.analyze(collectorData.mathematicalReasoning),
        this.collectors.errorPattern.analyze(collectorData.errorPattern),
        this.collectors.estimationSkills.analyze(collectorData.estimation),
        this.collectors.sequenceAnalysis.analyze(collectorData.sequence),
        this.collectors.comparisonSkills.analyze(collectorData.comparison),
        this.collectors.soundMatching.analyze(collectorData.sound),
        this.collectors.patternRecognition.analyze(collectorData.pattern)
      ];

      const [
        numericalCognitionResults,
        attentionFocusResults,
        visualProcessingResults,
        mathematicalReasoningResults,
        errorPatternResults,
        estimationSkillsResults,
        sequenceAnalysisResults,
        comparisonSkillsResults,
        soundMatchingResults,
        patternRecognitionResults
      ] = await Promise.all(analysisPromises);

      // 🎯 INTEGRAR RESULTADOS COM ANÁLISE POR ATIVIDADE V3
      const integratedAnalysis = this.integrateAnalysisResultsV3({
        numericalCognition: numericalCognitionResults,
        attentionFocus: attentionFocusResults,
        visualProcessing: visualProcessingResults,
        mathematicalReasoning: mathematicalReasoningResults,
        errorPattern: errorPatternResults,
        estimationSkills: estimationSkillsResults,
        sequenceAnalysis: sequenceAnalysisResults,
        comparisonSkills: comparisonSkillsResults,
        soundMatching: soundMatchingResults,
        patternRecognition: patternRecognitionResults,
        activityAnalysis
      }, gameData);

      // 🧠 CALCULAR MÉTRICAS DE SÍNTESE V3
      const synthesisMetrics = this.calculateSynthesisMetricsV3(integratedAnalysis, gameData);

      // 🎯 GERAR INSIGHTS COGNITIVOS ESPECÍFICOS V3
      const cognitiveInsights = this.generateCognitiveInsightsV3(integratedAnalysis, gameData);

      // 🎯 CRIAR PERFIL DE DESENVOLVIMENTO V3
      const developmentProfile = this.createDevelopmentProfileV3(integratedAnalysis, activityAnalysis);

      // 🎯 GERAR RECOMENDAÇÕES ADAPTATIVAS V3
      const adaptiveRecommendations = this.generateAdaptiveRecommendations(integratedAnalysis, activityAnalysis);

      const analysisTime = Date.now() - startTime;

      const completeAnalysis = {
        timestamp: new Date().toISOString(),
        gameId: 'numbercounting_v3',
        sessionId: gameData.sessionId,
        analysisTime,
        version: '3.0.0',

        // 🎯 RESULTADOS INDIVIDUAIS DOS COLETORES V3
        detailedResults: {
          numericalCognition: numericalCognitionResults,
          attentionFocus: attentionFocusResults,
          visualProcessing: visualProcessingResults,
          mathematicalReasoning: mathematicalReasoningResults,
          errorPattern: errorPatternResults,
          estimationSkills: estimationSkillsResults,
          sequenceAnalysis: sequenceAnalysisResults,
          comparisonSkills: comparisonSkillsResults,
          soundMatching: soundMatchingResults,
          patternRecognition: patternRecognitionResults
        },

        // 🎯 ANÁLISE POR ATIVIDADE V3
        activityAnalysis,

        // Análise integrada
        integratedAnalysis,
        synthesisMetrics,
        cognitiveInsights,
        developmentProfile,
        adaptiveRecommendations,

        // 📊 METADADOS DA ANÁLISE V3
        metadata: {
          totalAttempts: gameData.attempts?.length || 0,
          activitiesPlayed: this.getActivitiesPlayed(gameData),
          difficulty: gameData.difficulty,
          accuracy: this.calculateOverallAccuracy(gameData),
          avgResponseTime: this.calculateAverageResponseTime(gameData),
          dataQuality: this.assessDataQuality(gameData),
          engagementLevel: this.calculateEngagementLevel(gameData),
          cognitiveLoad: this.estimateCognitiveLoad(gameData)
        }
      };

      // Armazenar na história
      this.analysisHistory.push(completeAnalysis);

      // 🧠 ATUALIZAR PERFIL COGNITIVO V3
      this.updateCognitiveProfileV3(completeAnalysis);

      console.log(`✅ Análise NumberCounting V3 concluída em ${analysisTime}ms`);

      return completeAnalysis;

    } catch (error) {
      console.error('❌ Erro na análise completa NumberCounting V3:', error);
      return this.getErrorAnalysisV3(error, gameData);
    }
  }

  /**
   * Alias para runCompleteAnalysis para compatibilidade
   */
  async processGameData(gameData) {
    return await this.runCompleteAnalysis(gameData);
  }

  /**
   * Valida os dados do jogo antes da análise
   */
  validateGameData(gameData) {
    if (!gameData) {
      console.warn('NumberCountingCollectorsHub: Dados do jogo não fornecidos');
      return false;
    }

    if (!gameData.attempts || !Array.isArray(gameData.attempts)) {
      console.warn('NumberCountingCollectorsHub: Tentativas do jogo não encontradas');
      return false;
    }

    if (gameData.attempts.length < this.gameSpecificConfig.minAttempts) {
      console.warn(`NumberCountingCollectorsHub: Número insuficiente de tentativas (${gameData.attempts.length})`);
      return false;
    }

    const validAttempts = gameData.attempts.every(attempt =>
      typeof attempt.correctAnswer === 'number' &&
      typeof attempt.userAnswer === 'number' &&
      typeof attempt.responseTime === 'number' &&
      typeof attempt.isCorrect === 'boolean'
    );

    if (!validAttempts) {
      console.warn('NumberCountingCollectorsHub: Estrutura das tentativas inválida');
      return false;
    }

    return true;
  }

  /**
   * 🎯 ANÁLISE POR ATIVIDADE V3
   */
  async analyzeByActivity(gameData) {
    console.log('🎯 Analisando por atividade V3...');

    const activityData = {};
    const activitiesPlayed = this.getActivitiesPlayed(gameData);

    for (const activity of activitiesPlayed) {
      const activityAttempts = this.getActivityAttempts(gameData, activity);

      if (activityAttempts.length > 0) {
        activityData[activity] = {
          totalAttempts: activityAttempts.length,
          correctAnswers: activityAttempts.filter(a => a.isCorrect).length,
          accuracy: this.calculateActivityAccuracy(activityAttempts),
          averageResponseTime: this.calculateActivityAverageTime(activityAttempts),
          learningCurve: this.calculateLearningCurve(activityAttempts),
          errorPatterns: this.identifyActivityErrorPatterns(activityAttempts, activity),
          cognitiveMetrics: this.calculateActivityCognitiveMetrics(activityAttempts, activity),
          difficultyProgression: this.analyzeDifficultyProgression(activityAttempts),
          engagementIndicators: this.calculateActivityEngagement(activityAttempts)
        };
      }
    }

    return activityData;
  }

  /**
   * 🎯 OBTER ATIVIDADES JOGADAS
   */
  getActivitiesPlayed(gameData) {
    if (!gameData || !gameData.attempts) return [];

    const activities = new Set();
    gameData.attempts.forEach(attempt => {
      if (attempt.activityType) {
        activities.add(attempt.activityType);
      }
    });

    return Array.from(activities);
  }

  /**
   * 🎯 OBTER TENTATIVAS DE UMA ATIVIDADE ESPECÍFICA
   */
  getActivityAttempts(gameData, activity) {
    if (!gameData || !gameData.attempts) return [];
    return gameData.attempts.filter(attempt => attempt.activityType === activity);
  }

  /**
   * Identifica eventos de distração
   */
  identifyDistractionEvents(gameData) {
    if (!gameData || !gameData.attempts) return [];

    const distractionEvents = [];

    gameData.attempts.forEach((attempt, index) => {
      if (attempt.responseTime && attempt.responseTime > 10000) {
        distractionEvents.push({
          attemptIndex: index,
          type: 'slow_response',
          duration: attempt.responseTime,
          timestamp: attempt.timestamp
        });
      }

      if (index > 0 && !attempt.isCorrect && !gameData.attempts[index - 1].isCorrect) {
        distractionEvents.push({
          attemptIndex: index,
          type: 'consecutive_errors',
          timestamp: attempt.timestamp
        });
      }
    });

    return distractionEvents;
  }

  /**
   * Calcula métricas de foco
   */
  calculateFocusMetrics(gameData) {
    if (!gameData || !gameData.attempts) {
      return { avgResponseTime: 0, consistency: 0, attentionSpan: 0 };
    }

    const responseTimes = gameData.attempts
      .filter(attempt => attempt.responseTime)
      .map(attempt => attempt.responseTime);

    const avgResponseTime = responseTimes.length > 0
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
      : 0;

    const variance = responseTimes.length > 1
      ? responseTimes.reduce((sum, time) => sum + Math.pow(time - avgResponseTime, 2), 0) / responseTimes.length
      : 0;

    const consistency = variance > 0 ? 1 / (1 + Math.sqrt(variance) / avgResponseTime) : 1;

    const attentionSpan = this.calculateAttentionSpan(gameData);

    return {
      avgResponseTime,
      consistency: Math.max(0, Math.min(1, consistency)),
      attentionSpan
    };
  }

  /**
   * Calcula o span de atenção
   */
  calculateAttentionSpan(gameData) {
    if (!gameData || !gameData.attempts) return 0;

    let maxSpan = 0;
    let currentSpan = 0;

    gameData.attempts.forEach(attempt => {
      if (attempt.responseTime && attempt.responseTime < 8000) {
        currentSpan++;
        maxSpan = Math.max(maxSpan, currentSpan);
      } else {
        currentSpan = 0;
      }
    });

    return maxSpan;
  }

  /**
   * Avalia a complexidade visual
   */
  assessVisualComplexity(gameData) {
    if (!gameData || !gameData.attempts) return 'low';

    let complexityScore = 0;

    gameData.attempts.forEach(attempt => {
      if (attempt.numbers && attempt.numbers.length > 5) complexityScore += 2;
      if (attempt.patterns && attempt.patterns.length > 3) complexityScore += 2;
      if (attempt.visualElements && attempt.visualElements > 10) complexityScore += 1;
      if (attempt.difficulty && attempt.difficulty > 3) complexityScore += 1;
    });

    const avgComplexity = gameData.attempts.length > 0 ? complexityScore / gameData.attempts.length : 0;

    if (avgComplexity > 3) return 'high';
    if (avgComplexity > 1.5) return 'medium';
    return 'low';
  }

  /**
   * Extrai padrões espaciais
   */
  extractSpatialPatterns(gameData) {
    if (!gameData || !gameData.attempts) return [];

    const patterns = [];

    gameData.attempts.forEach(attempt => {
      if (attempt.position) {
        patterns.push(attempt.position);
      }
      if (attempt.spatialArrangement) {
        patterns.push(attempt.spatialArrangement);
      }
    });

    return patterns;
  }

  /**
   * Extrai tipos de raciocínio
   */
  extractReasoningTypes(gameData) {
    if (!gameData || !gameData.attempts) return ['basic_counting'];

    const reasoningTypes = new Set();

    gameData.attempts.forEach(attempt => {
      if (attempt.activityType === 'number_comparison') {
        reasoningTypes.add('comparative_reasoning');
      }
      if (attempt.activityType === 'sequence_completion') {
        reasoningTypes.add('sequential_reasoning');
      }
      if (attempt.activityType === 'number_estimation') {
        reasoningTypes.add('estimation_reasoning');
      }
      if (attempt.activityType === 'pattern_recognition') {
        reasoningTypes.add('pattern_reasoning');
      }
      if (attempt.isCorrect && attempt.responseTime < 2000) {
        reasoningTypes.add('intuitive_reasoning');
      }
    });

    if (reasoningTypes.size === 0) {
      reasoningTypes.add('basic_counting');
    }

    return Array.from(reasoningTypes);
  }

  /**
   * Avalia a complexidade do problema
   */
  assessProblemComplexity(gameData) {
    if (!gameData || !gameData.attempts) return 'low';

    let complexityScore = 0;

    gameData.attempts.forEach(attempt => {
      if (attempt.difficulty) complexityScore += attempt.difficulty;
      if (attempt.numbers && attempt.numbers.some(n => n > 20)) complexityScore += 1;
      if (attempt.operationSteps && attempt.operationSteps > 1) complexityScore += 2;
    });

    const avgComplexity = gameData.attempts.length > 0 ? complexityScore / gameData.attempts.length : 0;

    if (avgComplexity > 4) return 'high';
    if (avgComplexity > 2) return 'medium';
    return 'low';
  }

  /**
   * Categoriza erros
   */
  categorizeErrors(gameData) {
    if (!gameData || !gameData.attempts) return {};

    const errorTypes = {};

    gameData.attempts.forEach(attempt => {
      if (!attempt.isCorrect) {
        const errorType = this.identifyErrorType(attempt);
        errorTypes[errorType] = (errorTypes[errorType] || 0) + 1;
      }
    });

    return errorTypes;
  }

  /**
   * Identifica tipo de erro
   */
  identifyErrorType(attempt) {
    if (!attempt.answer || !attempt.targetValue) return 'unknown';

    const difference = Math.abs(attempt.answer - attempt.targetValue);

    if (difference === 1) return 'off_by_one';
    if (difference <= 3) return 'close_estimate';
    if (attempt.answer > attempt.targetValue * 2) return 'significant_overestimate';
    if (attempt.answer < attempt.targetValue / 2) return 'significant_underestimate';
    if (difference > 10) return 'major_miscalculation';

    return 'moderate_error';
  }

  /**
   * Analisa padrões de recuperação
   */
  analyzeRecoveryPatterns(gameData) {
    if (!gameData || !gameData.attempts) return {};

    const recoveryPatterns = {
      immediateRecovery: 0,
      gradualRecovery: 0,
      persistentErrors: 0
    };

    for (let i = 1; i < gameData.attempts.length; i++) {
      const prevAttempt = gameData.attempts[i - 1];
      const currentAttempt = gameData.attempts[i];

      if (!prevAttempt.isCorrect && currentAttempt.isCorrect) {
        recoveryPatterns.immediateRecovery++;
      } else if (!prevAttempt.isCorrect && !currentAttempt.isCorrect) {
        recoveryPatterns.persistentErrors++;
      }
    }

    return recoveryPatterns;
  }

  /**
   * 🎯 PREPARAR DADOS PARA COLETORES V3
   */
  prepareCollectorDataV3(gameData) {
    const baseData = {
      attempts: gameData.attempts,
      sessionId: gameData.sessionId,
      difficulty: gameData.difficulty,
      activitiesPlayed: this.getActivitiesPlayed(gameData),
      sessionDuration: gameData.sessionDuration || 0,
      timestamp: Date.now()
    };

    return {
      numericalCognition: {
        ...baseData,
        focus: 'numerical_concepts',
        numericalRange: this.extractNumericalRange(gameData),
        operationTypes: this.extractOperationTypes(gameData)
      },
      attentionFocus: {
        ...baseData,
        focus: 'sustained_attention',
        distractionEvents: this.identifyDistractionEvents(gameData),
        focusMetrics: this.calculateFocusMetrics(gameData)
      },
      visualProcessing: {
        ...baseData,
        focus: 'visual_numerical_processing',
        visualComplexity: this.assessVisualComplexity(gameData),
        spatialPatterns: this.extractSpatialPatterns(gameData)
      },
      mathematicalReasoning: {
        ...baseData,
        focus: 'mathematical_logic',
        reasoningTypes: this.extractReasoningTypes(gameData),
        problemComplexity: this.assessProblemComplexity(gameData)
      },
      errorPattern: {
        ...baseData,
        focus: 'error_analysis',
        errorTypes: this.categorizeErrors(gameData),
        recoveryPatterns: this.analyzeRecoveryPatterns(gameData)
      },
      estimation: {
        ...baseData,
        numberEstimation: this.filterByActivity(gameData, 'number_estimation'),
        focus: 'estimation_skills'
      },
      sequence: {
        ...baseData,
        sequenceCompletion: this.filterByActivity(gameData, 'sequence_completion'),
        focus: 'sequence_analysis'
      },
      comparison: {
        ...baseData,
        numberComparison: this.filterByActivity(gameData, 'number_comparison'),
        focus: 'comparison_skills'
      },
      sound: {
        ...baseData,
        soundMatching: this.filterByActivity(gameData, 'sound_matching'),
        focus: 'auditory_processing'
      },
      pattern: {
        ...baseData,
        patternRecognition: this.filterByActivity(gameData, 'pattern_recognition'),
        focus: 'pattern_analysis'
      }
    };
  }

  /**
   * Filtra dados por atividade específica
   */
  filterByActivity(gameData, activityType) {
    if (!gameData.attempts) return [];
    return gameData.attempts.filter(attempt => attempt.activityType === activityType);
  }

  /**
   * Extrai faixa numérica
   */
  extractNumericalRange(gameData) {
    if (!gameData || !gameData.attempts) {
      return { min: 0, max: 10 };
    }

    let minValue = Infinity;
    let maxValue = -Infinity;

    gameData.attempts.forEach(attempt => {
      if (attempt.numbers && Array.isArray(attempt.numbers)) {
        attempt.numbers.forEach(num => {
          if (typeof num === 'number') {
            minValue = Math.min(minValue, num);
            maxValue = Math.max(maxValue, num);
          }
        });
      }

      if (typeof attempt.answer === 'number') {
        minValue = Math.min(minValue, attempt.answer);
        maxValue = Math.max(maxValue, attempt.answer);
      }

      if (typeof attempt.targetValue === 'number') {
        minValue = Math.min(minValue, attempt.targetValue);
        maxValue = Math.max(maxValue, attempt.targetValue);
      }
    });

    return minValue === Infinity || maxValue === -Infinity ? { min: 0, max: 10 } : { min: minValue, max: maxValue };
  }

  /**
   * Extrai tipos de operação
   */
  extractOperationTypes(gameData) {
    if (!gameData || !gameData.attempts) {
      return ['counting'];
    }

    const operationTypes = new Set();

    gameData.attempts.forEach(attempt => {
      if (attempt.activityType) operationTypes.add(attempt.activityType);
      if (attempt.operationType) operationTypes.add(attempt.operationType);
      if (attempt.numbers && attempt.numbers.length > 1) operationTypes.add('comparison');
      if (attempt.isEstimation) operationTypes.add('estimation');
      if (attempt.hasSound || attempt.soundFile) operationTypes.add('auditory_matching');
      if (attempt.patterns) operationTypes.add('pattern_recognition');
    });

    return Array.from(operationTypes);
  }

  /**
   * Extrai padrões de resposta
   */
  extractResponsePatterns(gameData) {
    if (!gameData || !gameData.attempts) {
      return { patterns: [], frequency: {} };
    }

    const patterns = [];
    const frequency = {};

    gameData.attempts.forEach(attempt => {
      if (attempt.responsePattern) {
        patterns.push(attempt.responsePattern);
        frequency[attempt.responsePattern] = (frequency[attempt.responsePattern] || 0) + 1;
      }

      if (attempt.responseTime && attempt.responseTime < 1000) {
        patterns.push('quick_response');
        frequency['quick_response'] = (frequency['quick_response'] || 0) + 1;
      }

      if (attempt.confidence && attempt.confidence < 0.5) {
        patterns.push('low_confidence');
        frequency['low_confidence'] = (frequency['low_confidence'] || 0) + 1;
      }
    });

    return { patterns, frequency };
  }

  /**
   * Extrai progressão de dificuldade
   */
  extractDifficultyProgression(gameData) {
    if (!gameData || !gameData.attempts) {
      return { levels: [], progression: 'stable' };
    }

    const levels = [];
    let lastLevel = null;
    let progressionType = 'stable';

    gameData.attempts.forEach(attempt => {
      if (attempt.difficulty !== undefined) {
        levels.push(attempt.difficulty);

        if (lastLevel !== null) {
          if (attempt.difficulty > lastLevel) {
            progressionType = 'increasing';
          } else if (attempt.difficulty < lastLevel) {
            progressionType = 'decreasing';
          }
        }

        lastLevel = attempt.difficulty;
      }
    });

    return { levels, progression: progressionType };
  }

  /**
   * Extrai métricas de desempenho
   */
  extractPerformanceMetrics(gameData) {
    if (!gameData || !gameData.attempts) {
      return {
        accuracy: 0,
        averageResponseTime: 0,
        totalAttempts: 0,
        correctAttempts: 0
      };
    }

    const totalAttempts = gameData.attempts.length;
    let correctAttempts = 0;
    let totalResponseTime = 0;
    let responseTimeCount = 0;

    gameData.attempts.forEach(attempt => {
      if (attempt.isCorrect) correctAttempts++;
      if (attempt.responseTime && typeof attempt.responseTime === 'number') {
        totalResponseTime += attempt.responseTime;
        responseTimeCount++;
      }
    });

    const accuracy = totalAttempts > 0 ? correctAttempts / totalAttempts : 0;
    const averageResponseTime = responseTimeCount > 0 ? totalResponseTime / responseTimeCount : 0;

    return {
      accuracy,
      averageResponseTime,
      totalAttempts,
      correctAttempts
    };
  }

  /**
   * Calcula acurácia geral
   */
  calculateOverallAccuracy(gameData) {
    const metrics = this.extractPerformanceMetrics(gameData);
    return metrics.accuracy;
  }

  /**
   * Calcula tempo médio de resposta
   */
  calculateAverageResponseTime(gameData) {
    const metrics = this.extractPerformanceMetrics(gameData);
    return metrics.averageResponseTime;
  }

  /**
   * Avalia qualidade dos dados
   */
  assessDataQuality(gameData) {
    if (!gameData || !gameData.attempts) return 'poor';

    const validAttempts = gameData.attempts.every(attempt =>
      attempt.correctAnswer !== undefined &&
      attempt.userAnswer !== undefined &&
      attempt.responseTime !== undefined &&
      attempt.isCorrect !== undefined
    );

    const sufficientAttempts = gameData.attempts.length >= this.gameSpecificConfig.minAttempts;
    const variety = new Set(gameData.attempts.map(a => a.activityType)).size > 1;

    if (validAttempts && sufficientAttempts && variety) return 'high';
    if (validAttempts && sufficientAttempts) return 'medium';
    return 'poor';
  }

  /**
   * Calcula nível de engajamento
   */
  calculateEngagementLevel(gameData) {
    if (!gameData || !gameData.attempts) return 0;

    const totalAttempts = gameData.attempts.length;
    const responseTimes = gameData.attempts
      .filter(a => a.responseTime)
      .map(a => a.responseTime);
    const avgResponseTime = responseTimes.length > 0
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
      : 0;

    const engagementScore = totalAttempts * 0.4 + (avgResponseTime < 5000 ? 0.6 : 0.3);
    return Math.min(1, Math.max(0, engagementScore));
  }

  /**
   * Estima carga cognitiva
   */
  estimateCognitiveLoad(gameData) {
    if (!gameData || !gameData.attempts) return 'low';

    const complexity = this.assessProblemComplexity(gameData);
    const responseTimes = gameData.attempts
      .filter(a => a.responseTime)
      .map(a => a.responseTime);
    const avgResponseTime = responseTimes.length > 0
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
      : 0;

    if (complexity === 'high' || avgResponseTime > 7000) return 'high';
    if (complexity === 'medium' || avgResponseTime > 4000) return 'medium';
    return 'low';
  }

  /**
   * Calcula acurácia por atividade
   */
  calculateActivityAccuracy(activityAttempts) {
    if (!activityAttempts || activityAttempts.length === 0) return 0;
    const correct = activityAttempts.filter(a => a.isCorrect).length;
    return correct / activityAttempts.length;
  }

  /**
   * Calcula tempo médio por atividade
   */
  calculateActivityAverageTime(activityAttempts) {
    if (!activityAttempts || activityAttempts.length === 0) return 0;
    const times = activityAttempts.filter(a => a.responseTime).map(a => a.responseTime);
    return times.length > 0 ? times.reduce((sum, time) => sum + time, 0) / times.length : 0;
  }

  /**
   * Calcula curva de aprendizado
   */
  calculateLearningCurve(activityAttempts) {
    if (!activityAttempts || activityAttempts.length < 2) return [];

    const curve = [];
    let correctStreak = 0;

    activityAttempts.forEach(attempt => {
      if (attempt.isCorrect) {
        correctStreak++;
      } else {
        correctStreak = 0;
      }
      curve.push({ attempt: attempt, streak: correctStreak });
    });

    return curve;
  }

  /**
   * Identifica padrões de erro por atividade
   */
  identifyActivityErrorPatterns(activityAttempts, activity) {
    if (!activityAttempts || activityAttempts.length === 0) return [];

    const patterns = this.gameSpecificConfig.activityConfigs[activity]?.patterns || [];
    const errors = activityAttempts.filter(a => !a.isCorrect).map(a => this.identifyErrorType(a));
    return [...new Set([...patterns, ...errors])];
  }

  /**
   * Calcula métricas cognitivas por atividade
   */
  calculateActivityCognitiveMetrics(activityAttempts, activity) {
    if (!activityAttempts || activityAttempts.length === 0) return {};

    const config = this.gameSpecificConfig.activityConfigs[activity] || {};
    const accuracy = this.calculateActivityAccuracy(activityAttempts);
    const avgTime = this.calculateActivityAverageTime(activityAttempts);

    return {
      focusMetric: config.focusMetric || 'unknown',
      weightedScore: (accuracy * (config.accuracyWeight || 0.5)) + (Math.max(0, 1 - avgTime / 10000) * (config.timeWeight || 0.5))
    };
  }

  /**
   * Analisa progressão de dificuldade
   */
  analyzeDifficultyProgression(activityAttempts) {
    return this.extractDifficultyProgression({ attempts: activityAttempts });
  }

  /**
   * Calcula indicadores de engajamento por atividade
   */
  calculateActivityEngagement(activityAttempts) {
    if (!activityAttempts || activityAttempts.length === 0) return 0;
    const totalAttempts = activityAttempts.length;
    const avgTime = this.calculateActivityAverageTime(activityAttempts);
    return Math.min(1, totalAttempts * 0.4 + (avgTime < 5000 ? 0.6 : 0.3));
  }

  /**
   * Integra resultados da análise V3
   */
  integrateAnalysisResultsV3(results, gameData) {
    const integrated = {
      numericalSkills: results.numericalCognition.score || 0,
      attentionMetrics: results.attentionFocus.metrics || {},
      visualProcessing: results.visualProcessing.complexity || 'low',
      reasoningAbility: results.mathematicalReasoning.level || 'basic',
      errorPatterns: results.errorPattern.patterns || [],
      estimationAccuracy: results.estimationSkills.accuracy || 0,
      sequenceSkills: results.sequenceAnalysis.score || 0,
      comparisonSkills: results.comparisonSkills.score || 0,
      auditoryProcessing: results.soundMatching.score || 0,
      patternRecognition: results.patternRecognition.score || 0,
      activityPerformance: results.activityAnalysis
    };

    return integrated;
  }

  /**
   * Calcula métricas de síntese V3
   */
  calculateSynthesisMetricsV3(integratedAnalysis, gameData) {
    const weights = {
      numericalSkills: 0.2,
      attentionMetrics: 0.15,
      visualProcessing: 0.15,
      reasoningAbility: 0.2,
      estimationAccuracy: 0.15,
      sequenceSkills: 0.1,
      comparisonSkills: 0.1,
      auditoryProcessing: 0.05,
      patternRecognition: 0.05
    };

    let compositeScore = 0;
    Object.keys(weights).forEach(key => {
      const value = typeof integratedAnalysis[key] === 'number' ? integratedAnalysis[key] : 0;
      compositeScore += value * weights[key];
    });

    return {
      compositeScore,
      performanceTrend: this.analyzePerformanceTrend(gameData),
      cognitiveLoad: this.estimateCognitiveLoad(gameData)
    };
  }

  /**
   * Gera insights cognitivos V3
   */
  generateCognitiveInsightsV3(integratedAnalysis, gameData) {
    const insights = [];

    if (integratedAnalysis.numericalSkills > 0.8) {
      insights.push('Forte habilidade em conceitos numéricos');
    } else if (integratedAnalysis.numericalSkills < 0.4) {
      insights.push('Necessita reforço em conceitos numéricos básicos');
    }

    if (integratedAnalysis.attentionMetrics.consistency < 0.5) {
      insights.push('Inconsistência na atenção sustentada detectada');
    }

    if (integratedAnalysis.errorPatterns.includes('significant_overestimate')) {
      insights.push('Tendência a superestimar em tarefas de estimativa');
    }

    return insights;
  }

  /**
   * Cria perfil de desenvolvimento V3
   */
  createDevelopmentProfileV3(integratedAnalysis, activityAnalysis) {
    return {
      strengths: Object.keys(integratedAnalysis.activityPerformance).filter(activity => 
        integratedAnalysis.activityPerformance[activity].accuracy > 0.7),
      weaknesses: Object.keys(integratedAnalysis.activityPerformance).filter(activity => 
        integratedAnalysis.activityPerformance[activity].accuracy < 0.4),
      progress: this.calculateProgressMetrics(activityAnalysis)
    };
  }

  /**
   * Gera recomendações adaptativas
   */
  generateAdaptiveRecommendations(integratedAnalysis, activityAnalysis) {
    const recommendations = [];

    Object.keys(activityAnalysis).forEach(activity => {
      if (activityAnalysis[activity].accuracy < 0.5) {
        recommendations.push(`Praticar mais ${activity} com dificuldade reduzida`);
      }
      if (activityAnalysis[activity].averageResponseTime > 7000) {
        recommendations.push(`Focar em velocidade para ${activity}`);
      }
    });

    return recommendations;
  }

  /**
   * Atualiza perfil cognitivo V3
   */
  updateCognitiveProfileV3(completeAnalysis) {
    this.cognitiveProfile = {
      ...this.cognitiveProfile,
      lastAnalysis: completeAnalysis,
      performanceHistory: this.analysisHistory.map(a => a.metadata.accuracy)
    };
  }

  /**
   * Analisa tendência de desempenho
   */
  analyzePerformanceTrend(gameData) {
    const metrics = this.extractPerformanceMetrics(gameData);
    return metrics.accuracy > 0.7 ? 'positive' : metrics.accuracy < 0.4 ? 'negative' : 'stable';
  }

  /**
   * Calcula métricas de progresso
   */
  calculateProgressMetrics(activityAnalysis) {
    const progress = {};
    Object.keys(activityAnalysis).forEach(activity => {
      progress[activity] = activityAnalysis[activity].learningCurve.slice(-1)[0]?.streak || 0;
    });
    return progress;
  }

  /**
   * Retorna análise de erro V3
   */
  getErrorAnalysisV3(error, gameData) {
    return {
      error: error.message,
      timestamp: new Date().toISOString(),
      gameId: 'numbercounting_v3',
      sessionId: gameData?.sessionId || 'unknown',
      metadata: {
        totalAttempts: gameData?.attempts?.length || 0,
        dataQuality: 'poor'
      }
    };
  }
}