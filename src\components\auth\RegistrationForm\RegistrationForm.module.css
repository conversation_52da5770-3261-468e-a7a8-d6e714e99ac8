/**
 * 🎨 Portal Betina V3 - Estilos do Formulário de Cadastro
 */

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 20px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  border: 2px solid #6366f1;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 2px solid #6366f1;
  background: rgba(99, 102, 241, 0.1);
}

.header h2 {
  color: #ffffff;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.closeButton {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.closeButton:hover {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.progressBar {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24px 32px;
  gap: 16px;
}

.progressStep {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  color: #9ca3af;
  font-weight: 600;
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.progressStep.active {
  background: #6366f1;
  color: #ffffff;
  border-color: #6366f1;
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.4);
}

.content {
  padding: 32px;
}

.step h3 {
  color: #ffffff;
  margin: 0 0 24px 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.fieldGroup {
  margin-bottom: 20px;
}

.label {
  display: block;
  color: #e5e7eb;
  font-weight: 500;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.required {
  color: #ef4444;
  margin-left: 4px;
}

.input,
.select {
  width: 100%;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.input:focus,
.select:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

.input::placeholder {
  color: #9ca3af;
}

.input.error,
.select.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

.errorText {
  color: #fecaca;
  font-size: 0.8rem;
  margin-top: 4px;
  display: block;
}

.planSelection {
  margin-top: 32px;
}

.planSelection h3 {
  color: #ffffff;
  margin-bottom: 20px;
  text-align: center;
}

.plansGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.planCard {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.planCard:hover {
  border-color: #6366f1;
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.2);
}

.planCard.selected {
  border-color: #6366f1;
  background: rgba(99, 102, 241, 0.1);
  box-shadow: 0 0 30px rgba(99, 102, 241, 0.3);
}

.planCard.popular {
  border-color: #f59e0b;
}

.popularBadge {
  position: absolute;
  top: -2px;
  right: 20px;
  background: #f59e0b;
  color: #ffffff;
  padding: 4px 12px;
  border-radius: 0 0 8px 8px;
  font-size: 0.8rem;
  font-weight: 600;
}

.planCard h4 {
  color: #ffffff;
  margin: 0 0 12px 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.price {
  color: #ffffff;
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.period {
  font-size: 0.9rem;
  color: #9ca3af;
  font-weight: 400;
}

.description {
  color: #d1d5db;
  font-size: 0.9rem;
  margin-bottom: 16px;
}

.features {
  list-style: none;
  padding: 0;
  margin: 0;
}

.features li {
  color: #e5e7eb;
  font-size: 0.85rem;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

.features li:before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #10b981;
  font-weight: 600;
}

.moreFeatures {
  color: #9ca3af !important;
  font-style: italic;
}

.paymentStep {
  text-align: center;
}

.successMessage h3 {
  color: #10b981;
  margin-bottom: 16px;
}

.successMessage p {
  color: #d1d5db;
  margin-bottom: 32px;
}

.paymentInfo {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
}

.planSummary {
  margin-bottom: 24px;
}

.planSummary h4 {
  color: #ffffff;
  margin-bottom: 8px;
}

.amount {
  color: #3b82f6;
  font-size: 2rem;
  font-weight: 700;
}

.pixSection h4 {
  color: #ffffff;
  margin-bottom: 16px;
}

.pixCode {
  margin-bottom: 16px;
}

.pixCode label {
  display: block;
  color: #e5e7eb;
  margin-bottom: 8px;
  text-align: left;
}

.codeContainer {
  display: flex;
  gap: 8px;
}

.pixInput {
  flex: 1;
  padding: 12px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #ffffff;
  font-family: monospace;
  font-size: 0.9rem;
}

.copyButton {
  padding: 12px 16px;
  background: #6366f1;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.copyButton:hover {
  background: #4f46e5;
}

.pixInstructions {
  color: #d1d5db;
  font-size: 0.9rem;
  text-align: left;
  line-height: 1.6;
  margin-bottom: 16px;
}

.registrationId {
  color: #9ca3af;
  font-size: 0.9rem;
  padding: 12px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

.nextSteps {
  text-align: left;
}

.nextSteps h4 {
  color: #ffffff;
  margin-bottom: 12px;
}

.nextSteps ol {
  color: #d1d5db;
  padding-left: 20px;
}

.nextSteps li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-top: 2px solid rgba(255, 255, 255, 0.1);
}

.prevButton,
.nextButton,
.submitButton,
.closeModalButton {
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.prevButton {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.prevButton:hover {
  background: rgba(255, 255, 255, 0.2);
}

.nextButton,
.submitButton {
  background: #6366f1;
  color: #ffffff;
}

.nextButton:hover,
.submitButton:hover {
  background: #4f46e5;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

.submitButton:disabled {
  background: #6b7280;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.closeModalButton {
  background: #10b981;
  color: #ffffff;
  margin: 0 auto;
}

.closeModalButton:hover {
  background: #059669;
}
