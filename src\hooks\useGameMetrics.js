/**
 * @deprecated ESTE HOOK ESTÁ DEPRECADO
 * @reason Consolidado no useGameManager
 * @replacement useGameManager.js
 * @action REMOVER
 * @date 2025-07-14
 * 
 * ⚠️ Este hook será removido em versões futuras.
 * Por favor, use useGameManager.js ao invés deste hook.
 */

/**
 * @file useGameMetrics.js
 * @description Hook para coleta unificada de métricas de jogos com resiliência
 * @version 3.0.0
 */

import { useState, useCallback, useRef, useEffect } from 'react'
import { generateUniqueId } from '../utils/CommonUtils'
import { useResilientDatabase } from './useResilientDatabase'
import { useSystem } from '../components/context/SystemContext'
import { usePremium } from '../context/PremiumContext'
import { useDashboardAuth } from './useDashboardAuth'

/**
 * Hook para gerenciar métricas de jogos com sistema de resiliência
 * @param {string} gameId - Identificador do jogo
 * @param {string} userId - Identificador do usuário
 * @param {Object} options - Opções de configuração
 * @returns {Object} API para gerenciar métricas do jogo
 */
export function useGameMetrics (gameId, userId, options = {}) {
  // Estado para gerenciar a sessão de métricas
  const [sessionId] = useState(() => options.sessionId || generateUniqueId())
  const [metrics, setMetrics] = useState([])
  const [status, setStatus] = useState({
    isCollecting: false,
    lastSyncTime: null,
    syncErrors: 0,
    metricsCount: 0
  })

  // Referências para valores que não precisam causar re-renderização
  const startTime = useRef(null)
  const currentMetrics = useRef({})
  const syncInterval = useRef(null)

  // Hook para acesso ao banco de dados resiliente e sistema integrado
  const { saveGameMetrics, isReady: isDatabaseReady } = useResilientDatabase()
  const system = useSystem() // Acesso ao backend integrado
  const { isPremium } = usePremium() // Verificação de usuário premium
  const { shouldSaveMetrics, getLoginInfo } = useDashboardAuth() // Verificação de login no dashboard

  // Configuração com valores padrão
  const config = {
    syncInterval: 30000, // 30 segundos
    autoSync: true,
    detailedMetrics: true,
    ...options
  }

  /**
   * Inicia a sessão de coleta de métricas
   * @param {Object} initialData - Dados iniciais para a sessão
   * @returns {Object} Dados da sessão iniciada
   */
  const startMetricsSession = useCallback((initialData = {}) => {
    if (!gameId || !userId) {
      console.error('❌ gameId e userId são necessários para iniciar coleta de métricas')
      return null
    }

    try {
      // Registrar horário de início
      startTime.current = new Date()

      // Construir dados iniciais
      const sessionData = {
        sessionId,
        gameId,
        userId,
        deviceInfo: {
          userAgent: navigator.userAgent,
          screenSize: `${window.innerWidth}x${window.innerHeight}`,
          language: navigator.language
        },
        startTime: startTime.current.toISOString(),
        ...initialData
      }

      // Adicionar às métricas e atualizar estado
      const initialMetric = {
        type: 'SESSION_START',
        timestamp: new Date().toISOString(),
        data: sessionData
      }

      setMetrics([initialMetric])
      currentMetrics.current = sessionData

      setStatus(prev => ({
        ...prev,
        isCollecting: true,
        metricsCount: 1
      }))

      // Iniciar sincronização automática se habilitada
      if (config.autoSync) {
        startAutoSync()
      }

      console.log(`✅ Sessão de métricas iniciada: ${sessionId}`)
      return { sessionId, ...sessionData }
    } catch (error) {
      console.error('❌ Erro ao iniciar sessão de métricas:', error)
      return null
    }
  }, [gameId, userId, sessionId, config.autoSync])

  /**
   * Finaliza a sessão de coleta de métricas
   * @returns {Object} Dados completos da sessão
   */
  const endMetricsSession = useCallback(async () => {
    if (!status.isCollecting) return null

    try {
      // Parar sincronização automática
      if (syncInterval.current) {
        clearInterval(syncInterval.current)
        syncInterval.current = null
      }

      // Calcular duração
      const endTime = new Date()
      const duration = startTime.current
        ? endTime.getTime() - startTime.current.getTime()
        : 0

      // Construir métrica final
      const finalMetric = {
        type: 'SESSION_END',
        timestamp: endTime.toISOString(),
        data: {
          sessionId,
          gameId,
          userId,
          endTime: endTime.toISOString(),
          duration,
          metricsCount: metrics.length
        }
      }

      // Adicionar às métricas
      const updatedMetrics = [...metrics, finalMetric]
      setMetrics(updatedMetrics)

      // Atualizar estado
      setStatus(prev => ({
        ...prev,
        isCollecting: false,
        metricsCount: updatedMetrics.length
      }))

      // Sincronizar uma última vez baseado no tipo de usuário
      try {
        const sessionData = {
          metrics: updatedMetrics,
          sessionId,
          endTime: endTime.toISOString(),
          duration
        }

        const saveResult = await saveMetricsData(sessionData)
        
        if (saveResult) {
          setStatus(prev => ({
            ...prev,
            lastSyncTime: new Date()
          }))
        } else {
          setStatus(prev => ({
            ...prev,
            syncErrors: prev.syncErrors + 1
          }))
        }
      } catch (error) {
        console.error('❌ Erro ao sincronizar métricas finais:', error)
        setStatus(prev => ({
          ...prev,
          syncErrors: prev.syncErrors + 1
        }))
      }

      console.log(`✅ Sessão de métricas finalizada: ${sessionId}`)
      return {
        sessionId,
        metrics: updatedMetrics,
        duration,
        startTime: startTime.current,
        endTime
      }
    } catch (error) {
      console.error('❌ Erro ao finalizar sessão de métricas:', error)
      return null
    }
  }, [status.isCollecting, metrics, sessionId, gameId, userId, isDatabaseReady, saveGameMetrics])

  /**
   * Adiciona uma nova métrica à sessão
   * @param {string} metricType - Tipo da métrica
   * @param {Object} data - Dados da métrica
   */
  const addMetric = useCallback((metricType, data = {}) => {
    if (!status.isCollecting) {
      console.warn('⚠️ Tentativa de adicionar métrica sem sessão ativa')
      return
    }

    try {
      const newMetric = {
        type: metricType,
        timestamp: new Date().toISOString(),
        data,
        sessionId,
        gameId
      }

      setMetrics(prev => [...prev, newMetric])
      setStatus(prev => ({
        ...prev,
        metricsCount: prev.metricsCount + 1
      }))

      // Atualizar métricas atuais para uso em outras funções
      currentMetrics.current = {
        ...currentMetrics.current,
        [metricType]: data
      }

      return newMetric
    } catch (error) {
      console.error('❌ Erro ao adicionar métrica:', error)
    }
  }, [status.isCollecting, sessionId, gameId])

  /**
   * Inicia sincronização automática de métricas
   */
  const startAutoSync = useCallback(() => {
    // Limpar intervalo existente, se houver
    if (syncInterval.current) {
      clearInterval(syncInterval.current)
    }

    // Criar novo intervalo
    syncInterval.current = setInterval(async () => {
      if (!status.isCollecting || metrics.length === 0) return

      try {
        const partialData = {
          metrics: [...metrics], // Enviar cópia para evitar mudanças durante sincronização
          sessionId,
          partial: true
        }

        const saveResult = await saveMetricsData(partialData)
        
        if (saveResult) {
          setStatus(prev => ({
            ...prev,
            lastSyncTime: new Date()
          }))
          
          console.log(`✅ Métricas sincronizadas: ${metrics.length}`)
        } else {
          setStatus(prev => ({
            ...prev,
            syncErrors: prev.syncErrors + 1
          }))
        }
      } catch (error) {
        console.error('❌ Erro ao sincronizar métricas:', error)
        setStatus(prev => ({
          ...prev,
          syncErrors: prev.syncErrors + 1
        }))
      }
    }, config.syncInterval)

    return () => {
      if (syncInterval.current) {
        clearInterval(syncInterval.current)
      }
    }
  }, [metrics, status.isCollecting, sessionId, saveMetricsData, config.syncInterval])

  // Limpar recursos quando o componente for desmontado
  useEffect(() => {
    return () => {
      if (syncInterval.current) {
        clearInterval(syncInterval.current)
      }

      // Finalizar sessão se ainda estiver ativa
      if (status.isCollecting) {
        endMetricsSession().catch(error =>
          console.error('❌ Erro ao finalizar sessão automaticamente:', error)
        )
      }
    }
  }, [status.isCollecting, endMetricsSession])

  /**
   * Processa métricas com o sistema de análise integrado
   * @param {Object} gameAction - Ação do jogo para análise
   * @returns {Object} Análise completa da ação
   */
  const processWithAnalysis = useCallback(async (gameAction) => {
    if (!system || !status.isCollecting) {
      console.warn('⚠️ Sistema não disponível ou sessão não ativa')
      return null
    }

    try {
      // Adicionar métrica da ação
      const metric = addMetric('GAME_ACTION', gameAction)

      // Processar com o SystemOrchestrator
      const analysisResult = await system.services.core.orchestrator.processGameAction(sessionId, {
        ...gameAction,
        userId,
        gameId,
        sessionId,
        timestamp: new Date().toISOString()
      })

      // Adicionar métricas da análise
      if (analysisResult.analysis) {
        addMetric('BEHAVIORAL_ANALYSIS', analysisResult.analysis.behavioral)
        addMetric('COGNITIVE_ANALYSIS', analysisResult.analysis.cognitive)
        addMetric('AUTISM_ANALYSIS', analysisResult.analysis.autism)
      }

      if (analysisResult.metrics) {
        addMetric('PERFORMANCE_METRICS', analysisResult.metrics)
      }

      if (analysisResult.therapeuticInsights) {
        addMetric('THERAPEUTIC_INSIGHTS', analysisResult.therapeuticInsights)
      }

      if (analysisResult.adaptations) {
        addMetric('ADAPTIVE_RECOMMENDATIONS', analysisResult.adaptations)
      }

      return analysisResult
    } catch (error) {
      console.error('❌ Erro ao processar com análise:', error)
      return null
    }
  }, [system, status.isCollecting, addMetric, sessionId, userId, gameId])

  /**
   * Obtém insights em tempo real baseados nas métricas coletadas
   * @returns {Object} Insights e recomendações
   */
  const getRealTimeInsights = useCallback(async () => {
    if (!system || metrics.length === 0) {
      return { insights: [], recommendations: [] }
    }

    try {
      // Analisar métricas com AnalyticsService
      const analytics = await system.services.analytics.analyticsService.analyzeGameSession({
        sessionId,
        userId,
        gameId,
        metrics: metrics.slice(-10) // Últimas 10 métricas para análise rápida
      })

      // Obter recomendações adaptativas
      const adaptiveRecommendations = system.services.adaptive.adaptiveEngine.processSessionAdaptation(
        userId,
        { sessionMetrics: metrics, analytics }
      )

      return {
        insights: analytics.insights || [],
        recommendations: adaptiveRecommendations.difficulty ? [
          `Dificuldade: ${adaptiveRecommendations.difficulty.action}`,
          `Ritmo: ${adaptiveRecommendations.pacing?.action || 'manter'}`,
          `Conteúdo: ${adaptiveRecommendations.content?.action || 'manter'}`
        ] : [],
        shouldAdapt: adaptiveRecommendations.difficulty?.action !== 'manter' || false,
        adaptationLevel: adaptiveRecommendations.difficulty?.level || 'none'
      }
    } catch (error) {
      console.error('❌ Erro ao obter insights:', error)
      return { insights: [], recommendations: [] }
    }
  }, [system, metrics, sessionId, userId, gameId])

  /**
   * Salva dados localmente (para usuários free)
   * @param {string} key - Chave do localStorage
   * @param {Object} data - Dados a serem salvos
   */
  const saveToLocalStorage = useCallback((key, data) => {
    try {
      const storageData = {
        timestamp: new Date().toISOString(),
        userId,
        gameId,
        sessionId,
        ...data
      }
      
      localStorage.setItem(key, JSON.stringify(storageData))
      console.log(`💾 Dados salvos localmente: ${key}`)
      return true
    } catch (error) {
      console.error('❌ Erro ao salvar no localStorage:', error)
      return false
    }
  }, [userId, gameId, sessionId])

  /**
   * Salva dados remotamente (para usuários premium)
   * @param {Object} data - Dados a serem salvos
   */
  const saveToRemote = useCallback(async (data) => {
    try {
      if (!isDatabaseReady) {
        console.warn('⚠️ Database não disponível, salvando localmente como backup')
        return saveToLocalStorage(`betina_metrics_${sessionId}`, data)
      }

      await saveGameMetrics(userId, gameId, data)
      console.log(`🌐 Dados salvos remotamente para usuário premium`)
      
      // Salvar também localmente como backup
      saveToLocalStorage(`betina_metrics_backup_${sessionId}`, data)
      
      return true
    } catch (error) {
      console.error('❌ Erro ao salvar remotamente:', error)
      
      // Fallback para localStorage
      return saveToLocalStorage(`betina_metrics_${sessionId}`, data)
    }
  }, [isDatabaseReady, saveGameMetrics, userId, gameId, sessionId, saveToLocalStorage])

  /**
   * Salva dados apenas se há login ativo no dashboard
   * @param {Object} data - Dados a serem salvos
   */
  const saveMetricsData = useCallback(async (data) => {
    // 🔐 VERIFICAR SE HÁ LOGIN ATIVO NO DASHBOARD
    if (!shouldSaveMetrics()) {
      const loginInfo = getLoginInfo()
      console.log('🎮 Jogo gratuito - métricas não salvas (sem login no dashboard)', {
        hasLogin: loginInfo.isLoggedIn,
        loginType: loginInfo.loginType
      })
      return false // Não salvar métricas
    }

    // ✅ HÁ LOGIN ATIVO - SALVAR MÉTRICAS
    const loginInfo = getLoginInfo()
    console.log('✅ Login ativo detectado - salvando métricas:', {
      loginType: loginInfo.loginType,
      userInfo: loginInfo.userInfo
    })

    if (isPremium()) {
      // Usuário premium: salvar remotamente + backup local
      console.log('👑 Dashboard logado + Premium: salvando dados remotamente')
      return await saveToRemote(data)
    } else {
      // Usuário com login: salvar localmente
      console.log('🔐 Dashboard logado: salvando dados localmente')
      return saveToLocalStorage(`betina_metrics_${sessionId}`, data)
    }
  }, [shouldSaveMetrics, getLoginInfo, isPremium, saveToRemote, saveToLocalStorage, sessionId])

  return {
    // Estado
    sessionId,
    metrics,
    status,
    isCollecting: status.isCollecting,
    metricsCount: status.metricsCount,

    // Métodos principais
    startSession: startMetricsSession,
    endSession: endMetricsSession,
    addMetric,    // Métodos de conveniência para métricas comuns
    recordGameStart: () => addMetric('GAME_START', { timestamp: new Date().toISOString() }),
    recordGameEnd: (results) => addMetric('GAME_END', {
      timestamp: new Date().toISOString(),
      results
    }),
    recordAction: (action, data) => addMetric('USER_ACTION', { action, ...data }),
    recordScore: (score, level) => addMetric('SCORE_UPDATE', { score, level }),
    recordError: (error, context) => addMetric('ERROR', {
      message: error.message || error,
      stack: error.stack,
      context
    }),

    // Integração com sistema de análise
    processWithAnalysis,
    getRealTimeInsights,
    
    // Métodos de análise rápida
    trackEvent: processWithAnalysis, // Alias para compatibilidade
    getMetrics: () => metrics,
    getCurrentMetrics: () => currentMetrics.current
  }
}

export default useGameMetrics
