/**
 * @file LetterRecognition.module.css
 * @description Estilos modulares para o Jogo de Reconhecimento de Letras - BASEADO NO CONTAGEM NÚMEROS
 * @version 7.0.0 - Layout copiado do ContagemNumeros que funciona perfeitamente
 */

/* Variáveis CSS para consistência e reutilização - EXATO DO CONTAGEM NÚMEROS */
:root {
  --card-background: rgba(255, 255, 255, 0.1);
  --card-border: 1px solid rgba(255, 255, 255, 0.2);
  --card-blur: blur(10px);
  --success-bg: rgba(76, 175, 80, 0.3);
  --success-border: rgba(76, 175, 80, 0.5);
  --error-bg: rgba(244, 67, 54, 0.3);
  --error-border: rgba(244, 67, 54, 0.5);
  --primary-font: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --game-background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-color: #667eea;
  --primary-hover: #5a6fd8;
}

/* Animações - EXATO DO MEMORYGAME */
@keyframes buttonPressEffect {
  0% { transform: scale(1); }
  50% { transform: scale(1.15); box-shadow: 0 10px 30px rgba(255, 255, 255, 0.4); }
  100% { transform: scale(1); }
}

@keyframes correctFeedback {
  0% { box-shadow: 0 0 0 rgba(76, 175, 80, 0.4); }
  50% { box-shadow: 0 0 30px rgba(76, 175, 80, 0.8); }
  100% { box-shadow: 0 0 0 rgba(76, 175, 80, 0.4); }
}

@keyframes incorrectFeedback {
  0% { box-shadow: 0 0 0 rgba(244, 67, 54, 0.4); }
  25% { transform: translateX(-5px); }
  50% { transform: translateX(5px); box-shadow: 0 0 20px rgba(244, 67, 54, 0.8); }
  75% { transform: translateX(-5px); }
  100% { transform: translateX(0); box-shadow: 0 0 0 rgba(244, 67, 54, 0.4); }
}

/* Container principal - EXATO DO MEMORYGAME */
.letterRecognitionGame {
  min-height: 100vh;
  background: var(--gradient-bg);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  color: white;
  font-family: var(--primary-font);
}

/* Conteúdo do jogo - EXATO DO MEMORYGAME */
.gameContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Header do jogo - EXATO DO MEMORYGAME */
.gameHeader {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem 3rem 1rem 1rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  position: relative;
  min-height: 70px;
}

.gameTitle {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: white;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.activitySubtitle {
  font-size: 0.7rem;
  opacity: 0.8;
  margin-top: 0.25rem;
  background: var(--card-background);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  border: var(--card-border);
}

/* Botão TTS no header - EXATO DO MEMORYGAME */
.headerTtsButton {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  color: white;
  z-index: 10;
}

.headerTtsButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

.headerTtsButton:active {
  transform: scale(0.95);
}

/* Estados do toggle TTS - EXATO DO MEMORYGAME */
.ttsActive {
  background: var(--success-bg) !important;
  border-color: var(--success-border) !important;
}

.ttsInactive {
  background: var(--error-bg) !important;
  border-color: var(--error-border) !important;
}

/* Menu de atividades - EXATO DO MEMORYGAME */
.activityMenu {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

.activitySelector {
  margin-bottom: 2rem;
}

.selectorTitle {
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-align: left;
}

.activityButtons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  justify-content: center;
}

.activityButton {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.activityButton:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.activityButton.active {
  background: var(--success-bg) !important;
  border: 2px solid var(--success-border) !important;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

/* Elementos de atividade - EXATO DO MEMORYGAME */
.activityIcon {
  font-size: 1.2rem;
  margin-right: 0.5rem;
}

.activityName {
  flex: 1;
  text-align: left;
}

.activeIndicator {
  color: #00ff00;
  font-size: 0.8rem;
  margin-left: auto;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.7; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}

/* Estatísticas do jogo - EXATO DO MEMORYGAME */
.gameStats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.statCard {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.statCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4CAF50, #2196F3, #FF9800, #E91E63);
  opacity: 0.7;
}

.statValue {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: white;
}

.statLabel {
  font-size: 0.8rem;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.8);
}

/* Área do jogo principal - EXATO DO MEMORYGAME */
.gameArea {
  flex: 1;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

/* Grid de letras - seguindo padrão de cards do MemoryGame */
.lettersGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  width: 100%;
  max-width: 600px;
  margin: 2rem 0;
}

/* Card de letra - baseado no card do MemoryGame */
.letterCard {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  position: relative;
  overflow: hidden;
}

.letterCard:hover {
  transform: translateY(-4px);
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.letterCard:active {
  transform: translateY(-2px);
}

/* Estados do card de letra - EXATO DO MEMORYGAME */
.letterCard.correct {
  background: var(--success-bg) !important;
  border: 2px solid var(--success-border) !important;
  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.4);
  animation: correctFeedback 0.6s ease-out;
}

.letterCard.incorrect {
  background: var(--error-bg) !important;
  border: 2px solid var(--error-border) !important;
  box-shadow: 0 4px 20px rgba(244, 67, 54, 0.4);
  animation: incorrectFeedback 0.6s ease-out;
}

.letterCard.selected {
  background: rgba(255, 193, 7, 0.3) !important;
  border: 2px solid #FFC107 !important;
  box-shadow: 0 4px 20px rgba(255, 193, 7, 0.4);
  transform: scale(1.05);
}

.letterCard.active {
  background: rgba(33, 150, 243, 0.2) !important;
  border: 2px solid #2196F3 !important;
  box-shadow: 0 0 15px rgba(33, 150, 243, 0.5);
  animation: activePulse 2s ease-in-out infinite;
}

.letterCard.disabled {
  opacity: 0.5;
  cursor: not-allowed !important;
  background: rgba(128, 128, 128, 0.2) !important;
}

@keyframes activePulse {
  0%, 100% {
    box-shadow: 0 0 15px rgba(33, 150, 243, 0.5);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 25px rgba(33, 150, 243, 0.8);
    transform: scale(1.02);
  }
}

/* Conteúdo da letra - seguindo padrão MemoryGame */
.letterContent {
  font-size: 3rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.letterLabel {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

/* Botão de som - seguindo padrão MemoryGame */
.soundButton {
  background: rgba(74, 144, 226, 0.3);
  border: 2px solid rgba(74, 144, 226, 0.5);
  border-radius: 12px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  margin: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.soundButton:hover {
  background: rgba(74, 144, 226, 0.5);
  transform: scale(1.05);
}

/* Botão para repetir instrução - seguindo padrão MemoryGame */
.repeatButton {
  background: rgba(74, 144, 226, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  font-weight: 600;
  margin-left: 1rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.repeatButton:hover {
  background: rgba(74, 144, 226, 1);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.4);
}

.repeatButton:active {
  transform: scale(0.95);
}

/* Feedback e controles - seguindo padrão MemoryGame */
.feedback {
  text-align: center;
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 12px;
  font-weight: 600;
  animation: fadeIn 0.3s ease-out;
}

.feedback.success {
  background: var(--success-bg);
  border: 2px solid var(--success-border);
  color: white;
}

.feedback.error {
  background: var(--error-bg);
  border: 2px solid var(--error-border);
  color: white;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Controles do jogo - PADRÃO CONTAGEMNUMEROS */
.gameControls {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.controlButton {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 120px;
  justify-content: center;
}

.controlButton:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.controlButton:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* ===== ATIVIDADE DE FORMAÇÃO DE PALAVRAS ===== */
.wordFormationActivity {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  padding: 2rem;
}

.wordTarget {
  text-align: center;
  padding: 2rem;
  background: rgba(156, 39, 176, 0.2);
  border: 2px solid rgba(156, 39, 176, 0.5);
  border-radius: 16px;
  margin-bottom: 2rem;
}

.wordEmoji {
  font-size: 4rem;
  margin-bottom: 1rem;
  display: block;
}

.wordMeaning {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
}

.wordSlots {
  display: flex;
  gap: 0.5rem;
  margin: 2rem 0;
  justify-content: center;
  flex-wrap: wrap;
}

.wordSlot {
  width: 60px;
  height: 60px;
  border: 2px dashed rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 700;
  color: white;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.wordSlot.filled {
  background: rgba(76, 175, 80, 0.3);
  border-color: rgba(76, 175, 80, 0.6);
  border-style: solid;
}

.availableLetters {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 2rem;
}

.availableLetter {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  min-width: 50px;
  text-align: center;
}

.availableLetter:hover {
  transform: scale(1.05);
  background: rgba(255, 255, 255, 0.2);
}

.availableLetter.used {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(128, 128, 128, 0.2);
}

/* ===== MELHORIAS PARA FORMAÇÃO DE PALAVRAS ===== */
.soundActivity {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 2rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 16px;
  backdrop-filter: var(--card-blur);
  margin-bottom: 2rem;
}

.soundActivity h3 {
  color: white;
  font-size: 2rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
  line-height: 1.3;
}

.soundIndicator {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: soundPulse 2s ease-in-out infinite;
  text-align: center;
}

@keyframes soundPulse {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.1); opacity: 1; }
}

.activityTip {
  color: rgba(255, 255, 255, 0.95);
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  line-height: 1.5;
  font-weight: 500;
  max-width: 600px;
  text-align: center;
  background: rgba(255, 193, 7, 0.2);
  padding: 1rem 1.5rem;
  border-radius: 12px;
  border-left: 4px solid #FFC107;
}

.activityInstruction {
  text-align: center;
  padding: 2rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 16px;
  backdrop-filter: var(--card-blur);
}

.activityInstruction h3 {
  color: white;
  font-size: 1.8rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.activityInstruction p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  line-height: 1.5;
}

/* Responsividade - seguindo padrão MemoryGame + Formação de Palavras */
@media (max-width: 768px) {
  .letterRecognitionGame {
    padding: 0.5rem;
  }

  .gameArea {
    padding: 1rem;
    min-height: 300px;
  }

  .lettersGrid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.5rem;
  }

  .letterCard {
    padding: 1.5rem 0.5rem;
    min-height: 100px;
  }

  .letterContent {
    font-size: 2.5rem;
  }

  .gameStats {
    grid-template-columns: repeat(2, 1fr);
  }

  /* Responsividade para Formação de Palavras */
  .wordFormationActivity {
    padding: 1rem;
    gap: 1.5rem;
  }

  .wordTarget {
    padding: 1.5rem;
  }

  .wordEmoji {
    font-size: 3rem;
  }

  .wordMeaning {
    font-size: 1.3rem;
  }

  .wordSlots {
    gap: 0.3rem;
    margin: 1.5rem 0;
  }

  .wordSlot {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }

  .availableLetters {
    gap: 0.5rem;
    margin-top: 1.5rem;
  }

  .availableLetter {
    padding: 0.8rem;
    font-size: 1.3rem;
    min-width: 45px;
  }

  .soundActivity {
    padding: 1.5rem;
    min-height: 150px;
  }

  .soundActivity h3 {
    font-size: 1.6rem;
    margin-bottom: 1rem;
  }

  .soundIndicator {
    font-size: 3rem;
  }

  .activityTip {
    font-size: 1rem;
    padding: 0.8rem 1rem;
    margin-bottom: 1rem;
  }
}


