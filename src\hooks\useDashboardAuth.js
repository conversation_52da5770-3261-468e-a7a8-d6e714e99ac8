/**
 * @file useDashboardAuth.js
 * @description Hook para verificar autenticação ativa no dashboard
 * @version 1.0.0
 */

import { useState, useEffect, useCallback } from 'react'

/**
 * Hook para verificar se há login ativo no dashboard
 * Usado para determinar se métricas devem ser salvas
 */
export const useDashboardAuth = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [loginType, setLoginType] = useState(null)
  const [lastCheck, setLastCheck] = useState(null)

  /**
   * Verifica login ativo no DASHBOARD DE MÉTRICAS
   * (Não confundir com dashboard administrativo)
   */
  const checkDashboardLogin = useCallback(() => {
    // 📊 VERIFICAR APENAS O DASHBOARD DE MÉTRICAS
    const authToken = localStorage.getItem('authToken')
    const userData = localStorage.getItem('userData')

    let hasValidLogin = false
    let currentLoginType = null
    let userInfo = null

    // Verificar se há login ativo no dashboard de métricas
    if (authToken && userData) {
      try {
        const parsedUserData = JSON.parse(userData)
        if (parsedUserData.email) {
          hasValidLogin = true
          currentLoginType = 'metrics_dashboard'
          userInfo = {
            email: parsedUserData.email,
            name: parsedUserData.name || 'Usuário',
            role: parsedUserData.role || 'user',
            type: 'metrics_user'
          }
        }
      } catch (error) {
        console.warn('📊 Dados de usuário inválidos no dashboard de métricas')
        // Limpar dados corrompidos
        localStorage.removeItem('authToken')
        localStorage.removeItem('userData')
      }
    }

    setIsLoggedIn(hasValidLogin)
    setLoginType(currentLoginType)
    setLastCheck(new Date())

    // Log do resultado
    if (hasValidLogin) {
      console.log(`📊 Login ativo no dashboard de métricas: ${userInfo.email}`)
    } else {
      console.log('🚫 Nenhum login ativo no dashboard de métricas')
    }

    return {
      isLoggedIn: hasValidLogin,
      loginType: currentLoginType,
      userInfo
    }
  }, [])

  /**
   * Verifica se métricas devem ser salvas
   */
  const shouldSaveMetrics = useCallback(() => {
    const result = checkDashboardLogin()
    return result.isLoggedIn
  }, [checkDashboardLogin])

  /**
   * Obtém informações detalhadas do login
   */
  const getLoginInfo = useCallback(() => {
    const result = checkDashboardLogin()
    
    let userInfo = null
    
    if (result.isLoggedIn) {
      switch (result.loginType) {
        case 'dashboard_user':
          try {
            const userData = JSON.parse(localStorage.getItem('userData'))
            userInfo = {
              email: userData.email,
              name: userData.name || 'Usuário',
              type: 'user'
            }
          } catch (error) {
            userInfo = { type: 'user', email: 'unknown' }
          }
          break
          
        case 'admin_dashboard':
        case 'portal_admin':
        case 'integrated_admin':
          userInfo = {
            type: 'admin',
            role: 'administrator'
          }
          break
          
        default:
          userInfo = { type: 'unknown' }
      }
    }

    return {
      isLoggedIn: result.isLoggedIn,
      loginType: result.loginType,
      userInfo,
      lastCheck
    }
  }, [checkDashboardLogin, lastCheck])

  // Verificar login ao montar o componente
  useEffect(() => {
    checkDashboardLogin()
  }, [checkDashboardLogin])

  // Verificar periodicamente se o login ainda é válido
  useEffect(() => {
    const interval = setInterval(() => {
      checkDashboardLogin()
    }, 60000) // Verificar a cada minuto

    return () => clearInterval(interval)
  }, [checkDashboardLogin])

  // Escutar mudanças no localStorage
  useEffect(() => {
    const handleStorageChange = (e) => {
      // Verificar se a mudança afeta tokens de autenticação
      const authKeys = [
        'authToken', 'userData', 'admin_token', 'adminAuth', 
        'adminLoginTime', 'portalBetina_adminAuth', 'betina_admin_session'
      ]
      
      if (authKeys.includes(e.key)) {
        console.log(`🔄 Mudança detectada em ${e.key} - verificando login`)
        setTimeout(checkDashboardLogin, 100) // Pequeno delay para garantir que a mudança foi aplicada
      }
    }

    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [checkDashboardLogin])

  return {
    // Estado
    isLoggedIn,
    loginType,
    lastCheck,
    
    // Métodos
    checkDashboardLogin,
    shouldSaveMetrics,
    getLoginInfo,
    
    // Métodos de conveniência
    hasUserLogin: () => loginType === 'dashboard_user',
    hasAdminLogin: () => ['admin_dashboard', 'portal_admin', 'integrated_admin'].includes(loginType),
    canSaveMetrics: shouldSaveMetrics
  }
}

export default useDashboardAuth
