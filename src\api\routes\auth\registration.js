/**
 * 📝 Portal Betina V3 - Rotas de Cadastro e Pagamento
 * Endpoints para registro de novos usuários e processamento de pagamentos
 */

import express from 'express'
import { globalRateLimit, strictRateLimit } from '../../middleware/security/rateLimiter.js'
import { validateInput, schemas, sanitizeInput } from '../../middleware/validation/inputValidator.js'
import { asyncHandler, createError } from '../../middleware/error/errorHandler.js'
import { authenticate, requireAdmin } from '../../middleware/auth/jwt.js'
import { PRICING_PLANS, APPROVAL_STATUS, generatePixCode } from '../../../config/pricingPlans.js'
import bcrypt from 'bcryptjs'
import Joi from 'joi'

const router = express.Router()

// Mock database para demonstração (em produção usar banco real)
const registrations = new Map()
const payments = new Map()

// Schema de validação para cadastro
const registrationSchema = Joi.object({
  // Dados pessoais
  firstName: Joi.string().min(2).max(50).required(),
  lastName: Joi.string().min(2).max(50).required(),
  email: Joi.string().email().required(),
  phone: Joi.string().pattern(/^\(\d{2}\)\s\d{4,5}-\d{4}$/).required(),
  cpf: Joi.string().pattern(/^\d{3}\.\d{3}\.\d{3}-\d{2}$/).required(),
  
  // Dados profissionais
  profession: Joi.string().required(),
  institution: Joi.string().max(200).optional(),
  registration: Joi.string().max(50).optional(),
  experience: Joi.string().optional(),
  
  // Uso pretendido
  intendedUse: Joi.string().required(),
  numberOfUsers: Joi.string().required(),
  
  // Plano selecionado
  selectedPlan: Joi.string().valid('basic', 'premium', 'professional').required(),
  
  // Termos
  termsAccepted: Joi.boolean().valid(true).required(),
  privacyAccepted: Joi.boolean().valid(true).required()
})

/**
 * POST /api/auth/registration/register
 * Criar novo cadastro de usuário
 */
router.post(
  '/register',
  strictRateLimit, // Limite mais restrito para cadastros
  validateInput(registrationSchema, 'body'),
  sanitizeInput,
  asyncHandler(async (req, res) => {
    const registrationData = req.body
    
    // Verificar se email já existe
    const existingRegistration = Array.from(registrations.values())
      .find(reg => reg.email === registrationData.email)
    
    if (existingRegistration) {
      throw createError(409, 'Email já cadastrado no sistema')
    }
    
    // Gerar ID único para o cadastro
    const registrationId = `REG-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    
    // Criar registro de cadastro
    const registration = {
      id: registrationId,
      ...registrationData,
      status: APPROVAL_STATUS.PENDING,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      adminNotes: '',
      paymentReference: null
    }
    
    // Salvar no "banco de dados"
    registrations.set(registrationId, registration)
    
    console.log('📝 Novo cadastro criado:', {
      id: registrationId,
      email: registrationData.email,
      plan: registrationData.selectedPlan
    })
    
    res.status(201).json({
      success: true,
      message: 'Cadastro enviado com sucesso!',
      registrationId,
      status: APPROVAL_STATUS.PENDING,
      nextSteps: [
        'Aguarde análise do cadastro (até 24h)',
        'Você receberá um email com instruções de pagamento',
        'Após pagamento confirmado, sua conta será ativada'
      ]
    })
  })
)

/**
 * POST /api/auth/registration/generate-pix
 * Gerar código PIX para pagamento
 */
router.post(
  '/generate-pix',
  globalRateLimit,
  asyncHandler(async (req, res) => {
    const { registrationId } = req.body
    
    if (!registrationId) {
      throw createError(400, 'ID de cadastro é obrigatório')
    }
    
    const registration = registrations.get(registrationId)
    if (!registration) {
      throw createError(404, 'Cadastro não encontrado')
    }
    
    if (registration.status !== APPROVAL_STATUS.PAYMENT_PENDING) {
      throw createError(400, 'Cadastro não está aprovado para pagamento')
    }
    
    const plan = PRICING_PLANS[registration.selectedPlan]
    if (!plan) {
      throw createError(400, 'Plano inválido')
    }
    
    // Gerar código PIX
    const pixData = generatePixCode(plan.price, registration.selectedPlan, registrationId)
    
    // Salvar referência do pagamento
    const paymentId = `PAY-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    payments.set(paymentId, {
      id: paymentId,
      registrationId,
      amount: plan.price,
      pixCode: pixData.code,
      reference: pixData.reference,
      status: 'pending',
      createdAt: new Date().toISOString(),
      expiresAt: pixData.expiresAt.toISOString()
    })
    
    // Atualizar cadastro com referência do pagamento
    registration.paymentReference = paymentId
    registration.updatedAt = new Date().toISOString()
    registrations.set(registrationId, registration)
    
    console.log('💳 Código PIX gerado:', {
      registrationId,
      paymentId,
      amount: plan.price
    })
    
    res.json({
      success: true,
      pixData: {
        code: pixData.code,
        qrCode: pixData.qrCode,
        amount: plan.price,
        expiresAt: pixData.expiresAt,
        reference: pixData.reference
      },
      paymentId
    })
  })
)

/**
 * POST /api/auth/registration/confirm-payment
 * Confirmar pagamento (simulação - em produção seria webhook do banco)
 */
router.post(
  '/confirm-payment',
  globalRateLimit,
  asyncHandler(async (req, res) => {
    const { paymentId, transactionId } = req.body
    
    if (!paymentId) {
      throw createError(400, 'ID de pagamento é obrigatório')
    }
    
    const payment = payments.get(paymentId)
    if (!payment) {
      throw createError(404, 'Pagamento não encontrado')
    }
    
    if (payment.status !== 'pending') {
      throw createError(400, 'Pagamento já foi processado')
    }
    
    // Verificar se não expirou
    if (new Date() > new Date(payment.expiresAt)) {
      throw createError(400, 'Código PIX expirado')
    }
    
    // Atualizar status do pagamento
    payment.status = 'confirmed'
    payment.transactionId = transactionId || `TXN-${Date.now()}`
    payment.confirmedAt = new Date().toISOString()
    payments.set(paymentId, payment)
    
    // Atualizar status do cadastro
    const registration = registrations.get(payment.registrationId)
    if (registration) {
      registration.status = APPROVAL_STATUS.APPROVED
      registration.updatedAt = new Date().toISOString()
      registrations.set(payment.registrationId, registration)
    }
    
    console.log('✅ Pagamento confirmado:', {
      paymentId,
      registrationId: payment.registrationId,
      amount: payment.amount
    })
    
    res.json({
      success: true,
      message: 'Pagamento confirmado com sucesso!',
      status: APPROVAL_STATUS.APPROVED
    })
  })
)

/**
 * GET /api/auth/registration/status/:registrationId
 * Verificar status do cadastro
 */
router.get(
  '/status/:registrationId',
  globalRateLimit,
  asyncHandler(async (req, res) => {
    const { registrationId } = req.params
    
    const registration = registrations.get(registrationId)
    if (!registration) {
      throw createError(404, 'Cadastro não encontrado')
    }
    
    const response = {
      success: true,
      registration: {
        id: registration.id,
        email: registration.email,
        status: registration.status,
        selectedPlan: registration.selectedPlan,
        createdAt: registration.createdAt,
        updatedAt: registration.updatedAt
      }
    }
    
    // Incluir dados de pagamento se existir
    if (registration.paymentReference) {
      const payment = payments.get(registration.paymentReference)
      if (payment) {
        response.payment = {
          id: payment.id,
          amount: payment.amount,
          status: payment.status,
          expiresAt: payment.expiresAt
        }
      }
    }
    
    res.json(response)
  })
)

/**
 * GET /api/auth/registration/admin/list
 * Listar todos os cadastros (apenas admin)
 */
router.get(
  '/admin/list',
  authenticate,
  requireAdmin,
  asyncHandler(async (req, res) => {
    const { status, page = 1, limit = 20 } = req.query
    
    let registrationList = Array.from(registrations.values())
    
    // Filtrar por status se especificado
    if (status) {
      registrationList = registrationList.filter(reg => reg.status === status)
    }
    
    // Ordenar por data de criação (mais recentes primeiro)
    registrationList.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    
    // Paginação
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + parseInt(limit)
    const paginatedList = registrationList.slice(startIndex, endIndex)
    
    // Incluir dados de pagamento
    const enrichedList = paginatedList.map(registration => {
      const result = { ...registration }
      
      if (registration.paymentReference) {
        const payment = payments.get(registration.paymentReference)
        if (payment) {
          result.payment = payment
        }
      }
      
      return result
    })
    
    res.json({
      success: true,
      registrations: enrichedList,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: registrationList.length,
        totalPages: Math.ceil(registrationList.length / limit)
      },
      summary: {
        pending: registrationList.filter(r => r.status === APPROVAL_STATUS.PENDING).length,
        paymentPending: registrationList.filter(r => r.status === APPROVAL_STATUS.PAYMENT_PENDING).length,
        approved: registrationList.filter(r => r.status === APPROVAL_STATUS.APPROVED).length,
        rejected: registrationList.filter(r => r.status === APPROVAL_STATUS.REJECTED).length
      }
    })
  })
)

/**
 * PUT /api/auth/registration/admin/approve/:registrationId
 * Aprovar cadastro (apenas admin)
 */
router.put(
  '/admin/approve/:registrationId',
  authenticate,
  requireAdmin,
  asyncHandler(async (req, res) => {
    const { registrationId } = req.params
    const { adminNotes = '' } = req.body
    
    const registration = registrations.get(registrationId)
    if (!registration) {
      throw createError(404, 'Cadastro não encontrado')
    }
    
    if (registration.status !== APPROVAL_STATUS.PENDING) {
      throw createError(400, 'Apenas cadastros pendentes podem ser aprovados')
    }
    
    // Atualizar status para aguardando pagamento
    registration.status = APPROVAL_STATUS.PAYMENT_PENDING
    registration.adminNotes = adminNotes
    registration.updatedAt = new Date().toISOString()
    registration.approvedBy = req.user.email
    registration.approvedAt = new Date().toISOString()
    
    registrations.set(registrationId, registration)
    
    console.log('✅ Cadastro aprovado pelo admin:', {
      registrationId,
      email: registration.email,
      approvedBy: req.user.email
    })
    
    res.json({
      success: true,
      message: 'Cadastro aprovado com sucesso!',
      registration: {
        id: registration.id,
        email: registration.email,
        status: registration.status,
        updatedAt: registration.updatedAt
      }
    })
  })
)

/**
 * PUT /api/auth/registration/admin/reject/:registrationId
 * Rejeitar cadastro (apenas admin)
 */
router.put(
  '/admin/reject/:registrationId',
  authenticate,
  requireAdmin,
  asyncHandler(async (req, res) => {
    const { registrationId } = req.params
    const { reason = '', adminNotes = '' } = req.body
    
    const registration = registrations.get(registrationId)
    if (!registration) {
      throw createError(404, 'Cadastro não encontrado')
    }
    
    if (registration.status !== APPROVAL_STATUS.PENDING) {
      throw createError(400, 'Apenas cadastros pendentes podem ser rejeitados')
    }
    
    // Atualizar status para rejeitado
    registration.status = APPROVAL_STATUS.REJECTED
    registration.rejectionReason = reason
    registration.adminNotes = adminNotes
    registration.updatedAt = new Date().toISOString()
    registration.rejectedBy = req.user.email
    registration.rejectedAt = new Date().toISOString()
    
    registrations.set(registrationId, registration)
    
    console.log('❌ Cadastro rejeitado pelo admin:', {
      registrationId,
      email: registration.email,
      reason,
      rejectedBy: req.user.email
    })
    
    res.json({
      success: true,
      message: 'Cadastro rejeitado',
      registration: {
        id: registration.id,
        email: registration.email,
        status: registration.status,
        rejectionReason: reason,
        updatedAt: registration.updatedAt
      }
    })
  })
)

export default router
