/**
 * Rota de autenticação simplificada para o Portal Betina V3
 */

import express from 'express';
import jwt from 'jsonwebtoken';

const router = express.Router();

// Credenciais de usuário simplificadas
const USERS = {
  '<EMAIL>': {
    id: 'admin-001',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    isPremium: true
  },
  '<EMAIL>': {
    id: 'terapeuta-001',
    email: '<EMAIL>', 
    password: 'terapeuta123',
    role: 'therapist',
    name: 'Dr. <PERSON>',
    isPremium: true
  },
  '<EMAIL>': {
    id: 'pai-001',
    email: '<EMAIL>',
    password: 'pai123',
    role: 'parent',
    name: '<PERSON>',
    isPremium: false
  }
};

/**
 * POST /login - Autenticação de usuário
 */
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    console.log('🔐 Tentativa de login:', { email, timestamp: new Date().toISOString() });
    
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email e senha são obrigatórios'
      });
    }
    
    const user = USERS[email];
    if (!user || user.password !== password) {
      console.log('❌ Credenciais inválidas para:', email);
      return res.status(401).json({
        success: false,
        message: 'Credenciais inválidas'
      });
    }
    
    // Gerar JWT
    const token = jwt.sign(
      {
        userId: user.id,
        email: user.email,
        role: user.role,
        isPremium: user.isPremium
      },
      process.env.JWT_SECRET || 'portal_betina_secret_2025',
      { expiresIn: '24h' }
    );
    
    console.log('✅ Login bem-sucedido para:', user.email);
    
    res.json({
      success: true,
      message: 'Login realizado com sucesso',
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          isPremium: user.isPremium
        },
        token: token
      }
    });
    
  } catch (error) {
    console.error('❌ Erro no login:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

/**
 * GET /verify - Verificar token JWT
 */
router.get('/verify', (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Token não fornecido'
      });
    }
    
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'portal_betina_secret_2025');
    
    res.json({
      success: true,
      message: 'Token válido',
      data: decoded
    });
    
  } catch (error) {
    res.status(401).json({
      success: false,
      message: 'Token inválido'
    });
  }
});

/**
 * POST /logout - Logout de usuário (stateless)
 */
router.post('/logout', (req, res) => {
  try {
    // Em um sistema stateless JWT, o logout é feito apenas no cliente
    // O servidor apenas confirma que a operação foi realizada
    console.log('🔓 Logout realizado:', { timestamp: new Date().toISOString() });
    
    res.json({
      success: true,
      message: 'Logout realizado com sucesso'
    });
    
  } catch (error) {
    console.error('❌ Erro no logout:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

export default router;
