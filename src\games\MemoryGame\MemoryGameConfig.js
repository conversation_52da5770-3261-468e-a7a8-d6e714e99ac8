/**
 * 🧠 MEMORY GAME V3 CONFIG - Portal Betina V3
 * Configurações para sistema de 6 atividades diversificadas
 * Localização: src/games/MemoryGame/MemoryGameConfig.js
 */

// Níveis de dificuldade
export const DIFFICULTY_LEVELS = {
  EASY: 'easy',
  MEDIUM: 'medium',
  HARD: 'hard'
};

// 🎯 CONFIGURAÇÕES DAS ATIVIDADES - PADRÃO CONTAGEM NÚMEROS
export const ACTIVITY_CONFIG = {
  pair_matching: {
    name: 'Pares', // SIMPLIFICADO: apenas uma palavra
    icon: '🧩',
    description: 'Encontre cartas idênticas',
    cognitiveLoad: 'medium',
    focus: ['visual_memory', 'pattern_recognition']
  },
  sequence_memory: {
    name: 'Sequência', // SIMPLIFICADO: apenas uma palavra
    icon: '🧠',
    description: 'Memorize e repita sequências',
    cognitiveLoad: 'high',
    focus: ['working_memory', 'sequential_processing']
  },
  spatial_location: {
    name: 'Espacial', // SIMPLIFICADO: apenas uma palavra
    icon: '📍',
    description: 'Lembre-se das posições',
    cognitiveLoad: 'medium',
    focus: ['spatial_memory', 'visual_processing']
  },
  image_reconstruction: {
    name: 'Imagem', // SIMPLIFICADO: apenas uma palavra
    icon: '🖼️',
    description: 'Reconstrua a imagem original',
    cognitiveLoad: 'high',
    focus: ['visual_processing', 'spatial_reasoning']
  },
  number_sequence: {
    name: 'Números', // SIMPLIFICADO: apenas uma palavra
    icon: '🔢',
    description: 'Complete sequências numéricas',
    cognitiveLoad: 'medium',
    focus: ['working_memory', 'logical_reasoning']
  }
};

// Conjuntos de cartas por dificuldade
export const MEMORY_CARDS = {
  [DIFFICULTY_LEVELS.EASY]: [
    { id: 'apple_1', symbol: '🍎', name: 'Maçã', pairId: 'apple', theme: 'mixed_fun', category: 'fruit' },
    { id: 'apple_2', symbol: '🍎', name: 'Maçã', pairId: 'apple', theme: 'mixed_fun', category: 'fruit' },
    { id: 'cat_1', symbol: '🐱', name: 'Gato', pairId: 'cat', theme: 'mixed_fun', category: 'animal' },
    { id: 'cat_2', symbol: '🐱', name: 'Gato', pairId: 'cat', theme: 'mixed_fun', category: 'animal' },
    { id: 'star_1', symbol: '🌟', name: 'Estrela', pairId: 'star', theme: 'mixed_fun', category: 'space' },
    { id: 'star_2', symbol: '🌟', name: 'Estrela', pairId: 'star', theme: 'mixed_fun', category: 'space' },
    { id: 'rocket_1', symbol: '🚀', name: 'Foguete', pairId: 'rocket', theme: 'mixed_fun', category: 'space' },
    { id: 'rocket_2', symbol: '🚀', name: 'Foguete', pairId: 'rocket', theme: 'mixed_fun', category: 'space' }
  ],
  [DIFFICULTY_LEVELS.MEDIUM]: [
    { id: 'apple_1', symbol: '🍎', name: 'Maçã', pairId: 'apple', theme: 'colorful_world', category: 'fruit' },
    { id: 'apple_2', symbol: '🍎', name: 'Maçã', pairId: 'apple', theme: 'colorful_world', category: 'fruit' },
    { id: 'cat_1', symbol: '🐱', name: 'Gato', pairId: 'cat', theme: 'colorful_world', category: 'animal' },
    { id: 'cat_2', symbol: '🐱', name: 'Gato', pairId: 'cat', theme: 'colorful_world', category: 'animal' },
    { id: 'star_1', symbol: '🌟', name: 'Estrela', pairId: 'star', theme: 'colorful_world', category: 'space' },
    { id: 'star_2', symbol: '🌟', name: 'Estrela', pairId: 'star', theme: 'colorful_world', category: 'space' },
    { id: 'car_1', symbol: '🚗', name: 'Carro', pairId: 'car', theme: 'colorful_world', category: 'transport' },
    { id: 'car_2', symbol: '🚗', name: 'Carro', pairId: 'car', theme: 'colorful_world', category: 'transport' },
    { id: 'rainbow_1', symbol: '🌈', name: 'Arco-íris', pairId: 'rainbow', theme: 'colorful_world', category: 'nature' },
    { id: 'rainbow_2', symbol: '🌈', name: 'Arco-íris', pairId: 'rainbow', theme: 'colorful_world', category: 'nature' },
    { id: 'balloon_1', symbol: '🎈', name: 'Balão', pairId: 'balloon', theme: 'colorful_world', category: 'toy' },
    { id: 'balloon_2', symbol: '🎈', name: 'Balão', pairId: 'balloon', theme: 'colorful_world', category: 'toy' }
  ],
  [DIFFICULTY_LEVELS.HARD]: [
    { id: 'trophy_1', symbol: '🏆', name: 'Troféu', pairId: 'trophy', theme: 'champions', category: 'award' },
    { id: 'trophy_2', symbol: '🏆', name: 'Troféu', pairId: 'trophy', theme: 'champions', category: 'award' },
    { id: 'crown_1', symbol: '👑', name: 'Coroa', pairId: 'crown', theme: 'champions', category: 'award' },
    { id: 'crown_2', symbol: '👑', name: 'Coroa', pairId: 'crown', theme: 'champions', category: 'award' },
    { id: 'medal_1', symbol: '🥇', name: 'Medalha', pairId: 'medal', theme: 'champions', category: 'award' },
    { id: 'medal_2', symbol: '🥇', name: 'Medalha', pairId: 'medal', theme: 'champions', category: 'award' },
    { id: 'gem_1', symbol: '💎', name: 'Diamante', pairId: 'gem', theme: 'champions', category: 'treasure' },
    { id: 'gem_2', symbol: '💎', name: 'Diamante', pairId: 'gem', theme: 'champions', category: 'treasure' },
    { id: 'star_2_1', symbol: '⭐', name: 'Estrela Dourada', pairId: 'star_2', theme: 'champions', category: 'award' },
    { id: 'star_2_2', symbol: '⭐', name: 'Estrela Dourada', pairId: 'star_2', theme: 'champions', category: 'award' },
    { id: 'sparkles_1', symbol: '✨', name: 'Brilho', pairId: 'sparkles', theme: 'champions', category: 'magic' },
    { id: 'sparkles_2', symbol: '✨', name: 'Brilho', pairId: 'sparkles', theme: 'champions', category: 'magic' },
    { id: 'fire_1', symbol: '🔥', name: 'Fogo', pairId: 'fire', theme: 'champions', category: 'energy' },
    { id: 'fire_2', symbol: '🔥', name: 'Fogo', pairId: 'fire', theme: 'champions', category: 'energy' },
    { id: 'lightning_1', symbol: '⚡', name: 'Raio', pairId: 'lightning', theme: 'champions', category: 'energy' },
    { id: 'lightning_2', symbol: '⚡', name: 'Raio', pairId: 'lightning', theme: 'champions', category: 'energy' }
  ]
};

// 🎯 CONFIGURAÇÕES ESPECÍFICAS POR ATIVIDADE E DIFICULDADE
export const ACTIVITY_DIFFICULTY_CONFIG = {
  pair_matching: {
    easy: { pairs: 4, timeLimit: null, gridSize: '2x4' },
    medium: { pairs: 6, timeLimit: 180, gridSize: '3x4' },
    hard: { pairs: 8, timeLimit: 240, gridSize: '4x4' }
  },
  sequence_memory: {
    easy: { sequenceLength: 3, showTime: 2000, elements: ['#f44336', '#2196f3', '#4caf50'] },
    medium: { sequenceLength: 4, showTime: 1500, elements: ['#f44336', '#2196f3', '#4caf50', '#ffeb3b'] },
    hard: { sequenceLength: 6, showTime: 1000, elements: ['#f44336', '#2196f3', '#4caf50', '#ffeb3b', '#9c27b0', '#ff9800'] }
  },
  spatial_location: {
    easy: { gridSize: 3, targetCount: 3, showTime: 3000 },
    medium: { gridSize: 4, targetCount: 4, showTime: 2500 },
    hard: { gridSize: 5, targetCount: 6, showTime: 2000 }
  },
  image_reconstruction: {
    easy: { pieces: 4, imageComplexity: 'low', assistanceLevel: 'high' },
    medium: { pieces: 9, imageComplexity: 'medium', assistanceLevel: 'medium' },
    hard: { pieces: 16, imageComplexity: 'high', assistanceLevel: 'low' }
  },
  number_sequence: {
    easy: { sequenceLength: 4, pattern: 'increment', maxNumber: 10 },
    medium: { sequenceLength: 5, pattern: 'mixed', maxNumber: 20 },
    hard: { sequenceLength: 6, pattern: 'complex', maxNumber: 50 }
  }
};

// Configurações dos temas por dificuldade
export const THEME_CONFIG = {
  [DIFFICULTY_LEVELS.EASY]: {
    name: 'Diversão Mista',
    narrative: 'Vamos descobrir coisas legais com Betina!',
    description: 'Encontre os pares de frutas, animais e objetos espaciais.',
    backgroundColor: '#FFF8E1',
    accentColor: '#FF9800',
    activities: ['pair_matching', 'spatial_location', 'image_reconstruction', 'number_sequence']
  },
  [DIFFICULTY_LEVELS.MEDIUM]: {
    name: 'Mundo Colorido',
    narrative: 'Explore o mundo colorido com Betina!',
    description: 'Descubra as cores e formas em uma aventura divertida.',
    backgroundColor: '#E8F5E8',
    accentColor: '#4CAF50',
    activities: ['pair_matching', 'spatial_location', 'image_reconstruction', 'number_sequence']
  },
  [DIFFICULTY_LEVELS.HARD]: {
    name: 'Desafio dos Campeões',
    narrative: 'Mostre que você é um verdadeiro campeão!',
    description: 'Colete troféus, medalhas e tesouros neste desafio final.',
    backgroundColor: '#1A237E',
    accentColor: '#FFD700',
    activities: ['pair_matching', 'spatial_location', 'image_reconstruction', 'number_sequence']
  }
};

// Mensagens de encorajamento temáticas
export const ENCOURAGEMENT_MESSAGES = {
  general: [
    'Muito bem! 🎉',
    'Excelente! ⭐',
    'Parabéns! 🌟',
    'Fantástico! 🎊',
    'Incrível! 💫',
    'Ótimo trabalho! 👏',
    'Você conseguiu! 🏆',
    'Perfeito! ✨'
  ],
  [DIFFICULTY_LEVELS.EASY]: [
    'Que legal! Você encontrou o par! 🍎',
    'Betina adorou sua descoberta! 🐱',
    'As estrelas estão brilhando para você! 🌟',
    'Foguete preparado para decolar! 🚀'
  ],
  [DIFFICULTY_LEVELS.MEDIUM]: [
    'Que cores lindas! 🌈',
    'O mundo ficou mais colorido! 🎈',
    'Betina ama essa combinação! 🚗',
    'Brilhando como uma estrela! ⭐'
  ],
  [DIFFICULTY_LEVELS.HARD]: [
    'Você é um verdadeiro campeão! 🏆',
    'Coroa merecida! 👑',
    'Medalha de ouro conquistada! 🥇',
    'Brilhando como um diamante! 💎'
  ],
  pair_matching: [
    'Par perfeito encontrado! 🧩',
    'Combinação incrível! 🎯',
    'Memória visual fantástica! 👁️'
  ],
  sequence_memory: [
    'Sequência memorizada perfeitamente! 🧠',
    'Sua memória é impressionante! 💭',
    'Ordem perfeita! 📋'
  ],
  spatial_location: [
    'Localização exata! 📍',
    'Memória espacial excelente! 🗺️',
    'Você sabe onde tudo está! 🎯'
  ],
  image_reconstruction: [
    'Imagem reconstruída com maestria! 🖼️',
    'Artista da reconstrução! 🎨',
    'Visão espacial incrível! 👁️'
  ],
  number_sequence: [
    'Sequência numérica perfeita! 🔢',
    'Lógica matemática excelente! 🧮',
    'Padrão numérico dominado! 📊'
  ]
};

// Configurações gerais do jogo
export const GAME_CONFIG = {
  name: 'MemoryGame',
  displayName: 'Jogo da Memória V3',
  version: '3.0.0',
  initialDifficulty: DIFFICULTY_LEVELS.EASY,
  activityRotation: ['pair_matching', 'spatial_location', 'image_reconstruction', 'number_sequence'],
  activitiesPerSession: 1, // ALTERADO: Uma atividade por vez, controle do usuário
  userControlledActivities: true, // NOVO: Usuário escolhe quando avançar
  activitySettings: {
    roundsPerActivity: {
      EASY: 4,    // 4 rodadas no fácil
      MEDIUM: 5,  // 5 rodadas no médio
      HARD: 7     // 7 rodadas no difícil
    },
    minRoundsBeforeSwitch: 4, // Mínimo de rodadas antes de permitir troca
    maxActivityRounds: 7,     // Máximo de rodadas por atividade
    userControlledActivities: true // Usuário escolhe as atividades
  },
  scoring: {
    basePoints: 10,
    activityCompletion: 50,
    speedBonus: 20,
    accuracyBonus: 30,
    perfectRound: 100
  },
  timing: {
    cardFlipDuration: 1000,
    sequenceShowTime: 2000,
    betweenActivities: 3000,
    encouragementDuration: 2000
  },
  maxFlippedCards: 2,
  maxLives: 3
};

// Configurações de animação
export const ANIMATION_CONFIG = {
  cardFlip: {
    duration: 0.3,
    ease: 'easeInOut'
  },
  match: {
    type: 'spring',
    stiffness: 400,
    damping: 20
  },
  sequenceReveal: {
    type: 'spring',
    stiffness: 300,
    damping: 15,
    duration: 0.4
  },
  colorButtonPress: {
    type: 'spring',
    stiffness: 500,
    damping: 10,
    duration: 0.2
  },
  activityTransition: {
    type: 'spring',
    stiffness: 300,
    damping: 25,
    duration: 0.5
  },
  encouragement: {
    type: 'spring',
    stiffness: 600,
    damping: 30
  },
  completion: {
    type: 'spring',
    stiffness: 300,
    damping: 25
  },
  sequenceDisplay: {
    duration: 0.2,
    stagger: 0.1
  }
};

export default {
  DIFFICULTY_LEVELS,
  ACTIVITY_CONFIG,
  MEMORY_CARDS,
  ACTIVITY_DIFFICULTY_CONFIG,
  THEME_CONFIG,
  ENCOURAGEMENT_MESSAGES,
  GAME_CONFIG,
  ANIMATION_CONFIG
};