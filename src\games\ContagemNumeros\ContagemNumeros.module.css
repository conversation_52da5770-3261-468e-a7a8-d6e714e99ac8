/**
 * @file ContagemNumeros.module.css
 * @description Estilos modulares para o Jogo de Contagem de Números
 * @version 3.1.0
 */

/* Variáveis CSS para consistência e reutilização */
:root {
  --card-background: rgba(255, 255, 255, 0.1);
  --card-border: 1px solid rgba(255, 255, 255, 0.2);
  --card-blur: blur(10px);
  --success-bg: rgba(76, 175, 80, 0.3);
  --success-border: rgba(76, 175, 80, 0.5);
  --error-bg: rgba(244, 67, 54, 0.3);
  --error-border: rgba(244, 67, 54, 0.5);
  --primary-font: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --game-background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-color: #667eea;
  --primary-hover: #5a6fd8;
}

/* Container principal - PADRÃO LETTERRECOGNITION */
.contagemNumerosGame {
  width: 100%;
  height: 100vh;
  background: var(--game-background);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  box-sizing: border-box;
}

/* Conteúdo do jogo - PADRÃO LETTERRECOGNITION */
.gameContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Área do jogo - PADRÃO LETTERRECOGNITION */
.gameArea {
  flex: 1;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

/* Header do jogo - PADRÃO LETTERRECOGNITION EXATO */
.gameHeader {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem 3rem 1rem 1rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  position: relative;
  min-height: 70px;
}

.gameTitle {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: white;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.headerTtsButton {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 0.75rem;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
  min-width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.headerTtsButton:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.05);
}

.headerTtsButton.ttsActive {
  background: rgba(76, 175, 80, 0.3);
  border-color: #4CAF50;
  box-shadow: 0 0 15px rgba(76, 175, 80, 0.4);
}

/* Estatísticas do jogo - PADRÃO LETTERRECOGNITION */
.gameStats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.statCard {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.statCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4CAF50, #2196F3, #FF9800, #E91E63);
  opacity: 0.7;
}

.statValue {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: white;
}

.statLabel {
  font-size: 0.8rem;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.8);
}

/* Menu de atividades - PADRÃO LETTERRECOGNITION */
.activityMenu {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  justify-content: center;
  flex-wrap: wrap;
}

.activityButton {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.activityButton:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.activityButton.active {
  background: rgba(76, 175, 80, 0.3);
  border-color: rgba(76, 175, 80, 0.6);
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
}

/* Área do jogo - PADRÃO LETTERRECOGNITION EXATO */
.gameArea {
  flex: 1;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

/* Atividade de som - PADRÃO LETTERRECOGNITION */
.soundActivity {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: 2rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 16px;
  backdrop-filter: var(--card-blur);
  margin-bottom: 2rem;
}

.soundActivity h3 {
  color: white;
  font-size: 2rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
  line-height: 1.3;
}

.activityTip {
  color: rgba(255, 255, 255, 0.95);
  font-size: 1.4rem;
  margin-bottom: 1.5rem;
  line-height: 1.5;
  font-weight: 500;
  max-width: 800px;
}

.soundIndicator {
  font-size: 3rem;
  margin-bottom: 1rem;
  animation: bounce 2s infinite;
}

/* Grid de letras/números - PADRÃO LETTERRECOGNITION */
.lettersGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;
}

/* Botões de resposta - PADRÃO LETTERRECOGNITION */
.answerButton {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: var(--card-blur);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  position: relative;
  overflow: hidden;
}

.answerButton:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.answerButton:active {
  transform: translateY(0);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.optionNumber {
  font-size: 2rem;
  font-weight: bold;
  color: white;
  margin-bottom: 0.5rem;
}

/* Estados dos botões - PADRÃO LETTERRECOGNITION */
.answerButton.correct {
  background: var(--success-bg) !important;
  border: 2px solid var(--success-border) !important;
  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.4);
  animation: successPulse 0.6s ease-out;
}

.answerButton.incorrect {
  background: var(--error-bg) !important;
  border: 2px solid var(--error-border) !important;
  box-shadow: 0 4px 20px rgba(244, 67, 54, 0.4);
  animation: errorShake 0.6s ease-out;
}

.answerButton.selected {
  background: rgba(255, 193, 7, 0.3) !important;
  border: 2px solid #FFC107 !important;
  box-shadow: 0 4px 20px rgba(255, 193, 7, 0.4);
  transform: scale(1.05);
}

/* Controles do jogo - PADRÃO LETTERRECOGNITION */
.gameControls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.controlButton {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.controlButton:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

/* Animações - PADRÃO LETTERRECOGNITION */
@keyframes successPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Display dos objetos para contar - ESTÁVEL V4 */
.objectsDisplay {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin: 2rem 0;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  border: 2px dashed rgba(255, 255, 255, 0.3);
  min-height: 120px;
  position: relative; /* NUNCA absolute para estabilidade */
  overflow: visible; /* Garantir visibilidade */
}

.countingObject {
  font-size: 3rem;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: fadeInScale 0.6s ease-out forwards;
  opacity: 1; /* SEMPRE visível - V4 */
  transform: scale(1); /* Estado estável - V4 */
  transition: transform 0.3s ease;
  position: static; /* NUNCA absolute - V4 */
}

.countingObject:hover {
  transform: scale(1.05); /* Hover mais sutil */
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* ========================================= */
/* 🎯 ESTILOS V4 - COMPONENTES ESTÁVEIS */
/* ========================================= */

/* Botão TTS no header - PADRÃO LETTERRECOGNITION EXATO */
.headerTtsButton {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  color: white;
  z-index: 10;
}

.headerTtsButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

.headerTtsButton:active {
  transform: scale(0.95);
}

/* Estados do toggle TTS - PADRÃO LETTERRECOGNITION */
.headerTtsButton.ttsActive {
  background: var(--success-bg) !important;
  border-color: var(--success-border) !important;
}

/* Indicador ativo no menu */
.activeIndicator {
  color: #4CAF50;
  font-size: 0.8rem;
  margin-left: 0.5rem;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Display de soma simples */
.additionDisplay {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  font-size: 2.5rem;
  font-weight: bold;
  color: white;
}

.additionNumber {
  background: rgba(76, 175, 80, 0.3);
  padding: 1rem 1.5rem;
  border-radius: 12px;
  border: 2px solid rgba(76, 175, 80, 0.5);
  min-width: 80px;
  text-align: center;
}

.additionOperator {
  color: rgba(255, 255, 255, 0.8);
  font-size: 3rem;
}

.additionResult {
  background: rgba(255, 193, 7, 0.3);
  padding: 1rem 1.5rem;
  border-radius: 12px;
  border: 2px solid rgba(255, 193, 7, 0.5);
  min-width: 80px;
  text-align: center;
}

/* Número alvo para reconhecimento */
.targetNumber {
  font-size: 4rem;
  font-weight: bold;
  color: white;
  background: rgba(33, 150, 243, 0.3);
  padding: 2rem;
  border-radius: 20px;
  border: 3px solid rgba(33, 150, 243, 0.5);
  text-align: center;
  min-width: 120px;
}

/* Comparação de grupos */
.comparisonGroups {
  display: flex;
  gap: 2rem;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.comparisonGroup {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
  text-align: center;
}

.comparisonGroup:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.05);
}

.groupLabel {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1rem;
  font-weight: bold;
}

.groupObjects {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
}

.groupObject {
  font-size: 2rem;
}

.vsIndicator {
  font-size: 1.5rem;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 8px;
}

/* Feedback overlay */
.feedbackOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.feedbackContent {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 16px;
  text-align: center;
  backdrop-filter: blur(10px);
}

.feedbackMessage {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
}

.feedbackOverlay.success .feedbackContent {
  border: 3px solid rgba(76, 175, 80, 0.8);
  background: rgba(76, 175, 80, 0.2);
}

.feedbackOverlay.error .feedbackContent {
  border: 3px solid rgba(244, 67, 54, 0.8);
  background: rgba(244, 67, 54, 0.2);
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

/* =====================================================
   🎯 ESTILOS PARA RECONHECIMENTO NUMÉRICO MELHORADO V2
   ===================================================== */

/* Grid para objetos de contagem */
.countingObjectsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
  gap: 1rem;
  max-width: 400px;
  margin: 0 auto;
  padding: 1rem;
}

/* Número por extenso */
.writtenNumberDisplay {
  text-align: center;
  padding: 2rem;
}

.writtenNumberText {
  font-size: 3rem;
  font-weight: bold;
  color: #FFD700;
  background: rgba(255, 215, 0, 0.2);
  padding: 1rem 2rem;
  border-radius: 15px;
  border: 3px solid rgba(255, 215, 0, 0.5);
  font-style: italic;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Sequência numérica */
.sequenceDisplay {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  font-size: 3rem;
  font-weight: bold;
}

.sequenceNumber {
  background: rgba(76, 175, 80, 0.3);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 15px;
  border: 3px solid rgba(76, 175, 80, 0.5);
  min-width: 80px;
  text-align: center;
}

.sequenceArrow {
  color: rgba(255, 255, 255, 0.8);
  font-size: 2rem;
}

.sequencePlaceholder {
  background: rgba(255, 193, 7, 0.3);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 15px;
  border: 3px solid rgba(255, 193, 7, 0.5);
  min-width: 80px;
  text-align: center;
}

/* Comparação de números */
.comparisonDisplay {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  font-size: 3rem;
  font-weight: bold;
}

.comparisonNumber {
  background: rgba(33, 150, 243, 0.3);
  color: white;
  padding: 1.5rem;
  border-radius: 50%;
  border: 3px solid rgba(33, 150, 243, 0.5);
  min-width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.comparisonVs {
  color: rgba(255, 255, 255, 0.8);
  font-size: 2rem;
  font-weight: normal;
}

/* Padrões visuais */
.patternDisplay {
  text-align: center;
  padding: 2rem;
}

.dotsPattern {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  max-width: 300px;
  margin: 0 auto;
}

.dot {
  font-size: 3rem;
  color: #FF6B6B;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  animation: bounce 2s infinite;
}

.dot:nth-child(even) {
  animation-delay: 0.2s;
}

.dicePattern {
  text-align: center;
}

.diceEmoji {
  font-size: 6rem;
  filter: drop-shadow(4px 4px 8px rgba(0, 0, 0, 0.3));
  animation: pulse 2s infinite;
}

.fingersPattern {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

.finger {
  font-size: 4rem;
  animation: wave 1s infinite;
  animation-delay: calc(var(--i) * 0.1s);
}

.finger:nth-child(1) { --i: 0; }
.finger:nth-child(2) { --i: 1; }
.finger:nth-child(3) { --i: 2; }
.finger:nth-child(4) { --i: 3; }
.finger:nth-child(5) { --i: 4; }

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes wave {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(10deg); }
  75% { transform: rotate(-10deg); }
}

.gameTitle {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: white;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.activitySubtitle {
  font-size: 0.7rem;
  opacity: 0.8;
  margin-top: 0.25rem;
  background: var(--card-background);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  border: var(--card-border);
}

/* Área da pergunta */
.questionArea {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  text-align: center;
}

.questionTitle {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 2rem;
  color: white;
}

.objectsDisplay {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
  margin: 2rem 0;
  min-height: 150px;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  border: 2px dashed rgba(255, 255, 255, 0.3);
}

.countingObject {
  font-size: 3rem;
  transition: all 0.3s ease;
  animation: objectAppear 0.5s ease-out;
  cursor: default;
  user-select: none;
}

.countingObject:hover {
  transform: scale(1.1);
}

@keyframes objectAppear {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(-10deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* Opções de resposta */
.answerOptions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  max-width: 600px;
  margin: 0 auto;
}

.answerButton {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 1.5rem;
  color: white;
  font-size: 2rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.answerButton:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.25);
}

.answerButton.correct {
  border-color: var(--success-border);
  background: var(--success-bg);
  animation: correctPulse 0.6s ease-in-out;
}

.answerButton.incorrect {
  border-color: var(--error-border);
  background: var(--error-bg);
  animation: incorrectShake 0.6s ease-in-out;
}

.answerButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

@keyframes correctPulse {
  0%, 100% { transform: translateY(-5px) scale(1.05); }
  50% { transform: translateY(-5px) scale(1.2); }
}

@keyframes incorrectShake {
  0%, 100% { transform: translateY(-5px) translateX(0); }
  25% { transform: translateY(-5px) translateX(-10px); }
  75% { transform: translateY(-5px) translateX(10px); }
}

/* Controles do jogo */
.gameControls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.controlButton {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 600;
}

.controlButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.nextButton {
  background: var(--success-bg);
  border-color: var(--success-border);
}

.nextButton:hover {
  background: rgba(76, 175, 80, 0.4);
}

/* Feedback */
.feedbackMessage {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 1.5rem 2.5rem;
  border-radius: 16px;
  font-size: 1.3rem;
  font-weight: 700;
  z-index: 1000;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: messageSlide 3s ease-in-out;
}

.feedbackMessage.success {
  background: var(--success-bg);
  color: white;
}

.feedbackMessage.error {
  background: var(--error-bg);
  color: white;
}

@keyframes messageSlide {
  0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
  15% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  85% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
}

/* Botões TTS */
.headerTtsButton {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  color: white;
  z-index: 10;
}

.headerTtsButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

.headerTtsButton:active {
  transform: scale(0.95);
}

.ttsActive {
  background: var(--success-bg) !important;
  border-color: var(--success-border) !important;
}

.ttsInactive {
  background: var(--error-bg) !important;
  border-color: var(--error-border) !important;
}

.repeatButton {
  background: rgba(74, 144, 226, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  font-weight: 600;
  margin-left: 1rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.repeatButton:hover {
  background: rgba(74, 144, 226, 1);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.4);
}

.repeatButton:active {
  transform: scale(0.95);
}

.ttsIndicator {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(74, 144, 226, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 6px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  z-index: 5;
  pointer-events: none;
  transition: all 0.2s ease;
}

.answerButton:hover .ttsIndicator {
  background: rgba(74, 144, 226, 1);
  transform: scale(1.1);
}

/* Menu de atividades */
.activityMenu {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

.activityButton {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.activityButton:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.activityButton.active {
  background: var(--success-bg) !important;
  border: 2px solid var(--success-border) !important;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

/* Atividade de Som */
.soundActivity {
  text-align: center;
}

.soundIndicator {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: soundPulse 2s ease-in-out infinite;
}

@keyframes soundPulse {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.1); opacity: 1; }
}

.soundButton {
  background: rgba(74, 144, 226, 0.3);
  border: 2px solid rgba(74, 144, 226, 0.5);
  border-radius: 12px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  margin: 1rem;
}

.soundButton:hover {
  background: rgba(74, 144, 226, 0.5);
  transform: scale(1.05);
}

/* Atividade de Estimativa */
.estimationDisplay {
  position: relative;
}

.estimationObjects {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
  opacity: 0.9;
}

.estimationObject {
  font-size: 1.5rem;
  transform: rotate(var(--rotation));
  transition: opacity 0.3s ease;
}

.estimationTip {
  text-align: center;
  margin-top: 1rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  background: rgba(255, 193, 7, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border-left: 4px solid #FFC107;
}

/* Atividade de Sequência */
.sequenceDisplay {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  flex-wrap: wrap;
}

.sequenceNumber {
  background: var(--card-background);
  padding: 1rem;
  border-radius: 12px;
  min-width: 60px;
  text-align: center;
  color: white;
  font-weight: bold;
  transition: all 0.3s ease;
}

.sequenceNumber:hover {
  transform: scale(1.05);
}

.sequenceArrow {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.5rem;
}

.sequenceMissing {
  background: rgba(255, 255, 80, 0.3) !important;
  border: 2px dashed rgba(255, 255, 255, 0.5);
  animation: missingPulse 2s ease-in-out infinite;
}

@keyframes missingPulse {
  0%, 100% { box-shadow: 0 0 0 rgba(255, 255, 80, 0.3); }
  50% { box-shadow: 0 0 20px rgba(255, 255, 80, 0.6); }
}

/* Atividade de Comparação */
.comparisonDisplay {
  display: flex;
  gap: 3rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.comparisonGroup {
  text-align: center;
  background: var(--card-background);
  padding: 1.5rem;
  border-radius: 16px;
  transition: all 0.3s ease;
}

.comparisonGroup:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-5px);
}

.comparisonObjects {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
  max-width: 150px;
  margin-bottom: 1rem;
}

.comparisonNumber {
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
  background: var(--card-background);
  padding: 0.5rem 1rem;
  border-radius: 8px;
}

/* Atividade de Padrões */
.patternDisplay {
  text-align: center;
}

.patternDescription {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1rem;
  background: rgba(156, 39, 176, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border-left: 4px solid #9C27B0;
}

.patternSequence {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  flex-wrap: wrap;
}

.patternNumber {
  background: linear-gradient(135deg, rgba(156, 39, 176, 0.3), rgba(156, 39, 176, 0.1));
  padding: 1rem;
  border-radius: 12px;
  min-width: 60px;
  text-align: center;
  color: white;
  font-weight: bold;
  border: 1px solid rgba(156, 39, 176, 0.4);
  transition: all 0.3s ease;
}

.patternNumber:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(156, 39, 176, 0.3);
}

/* ========================================= */
/* 🎯 RESPONSIVIDADE V4 - MOBILE FIRST */
/* ========================================= */

/* Tablet e telas médias */
@media (max-width: 768px) {
  .contagemNumerosGame {
    padding: 0.5rem;
  }

  .gameHeader {
    padding: 0.75rem 2.5rem 0.75rem 0.75rem;
    min-height: 60px;
  }

  .gameTitle {
    font-size: 1.4rem;
  }

  .activitySubtitle {
    font-size: 0.65rem;
    padding: 0.2rem 0.5rem;
  }

  .gameContent {
    padding: 0.5rem;
  }

  .questionArea {
    padding: 1.5rem;
    border-radius: 16px;
  }

  .questionTitle {
    font-size: 1.2rem;
  }

  .objectsDisplay {
    margin: 1rem 0;
    padding: 1rem;
    gap: 0.75rem;
    min-height: 100px;
  }

  .countingObject {
    font-size: 2.5rem;
    padding: 0.75rem;
  }

  .answerOptions {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
  }

  .answerButton {
    padding: 1rem;
    font-size: 1rem;
    min-height: 60px;
  }

  .optionNumber {
    font-size: 1.5rem;
  }

  .gameStats {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 0.5rem;
  }

  .statCard {
    padding: 0.75rem;
  }

  /* V4 - Componentes específicos */
  .additionDisplay {
    gap: 1rem;
    font-size: 2rem;
  }

  .additionNumber, .additionResult {
    padding: 0.75rem 1rem;
    min-width: 60px;
    font-size: 1.8rem;
  }

  .additionOperator {
    font-size: 2.5rem;
  }

  .targetNumber {
    font-size: 3rem;
    padding: 1.5rem;
    min-width: 100px;
  }

  .comparisonGroups {
    gap: 1rem;
  }

  .comparisonGroup {
    padding: 1rem;
    min-width: 120px;
  }

  .groupObject {
    font-size: 1.5rem;
  }

  .ttsButton {
    padding: 0.4rem 0.8rem;
    font-size: 1rem;
  }
}

/* =====================================================
   🎯 ESTILOS PARA ATIVIDADES ESTRUTURAIS REDESENHADAS V3
   ===================================================== */

/* Estimativa de quantidade */
.estimationHint {
  font-size: 0.9rem;
  color: #FFD700;
  font-style: italic;
  margin-top: 0.5rem;
}

/* Composição numérica */
.compositionTarget {
  font-size: 1.5rem;
  font-weight: 700;
  color: #4CAF50;
  margin: 1rem 0;
  padding: 1rem;
  background: rgba(76, 175, 80, 0.2);
  border: 2px solid rgba(76, 175, 80, 0.5);
  border-radius: 12px;
  text-align: center;
}

.decompositionOptions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 2rem 0;
}

.decompositionButton {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1.5rem;
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.decompositionButton:hover {
  transform: translateY(-2px);
  background: rgba(156, 39, 176, 0.3);
  border-color: rgba(156, 39, 176, 0.6);
}

/* Comparação de magnitude */
.comparisonGroups {
  display: flex;
  gap: 2rem;
  justify-content: center;
  flex-wrap: wrap;
  margin: 2rem 0;
}

.comparisonGroup {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  min-width: 200px;
  transition: all 0.3s ease;
}

.comparisonGroup:hover {
  transform: translateY(-4px);
  background: rgba(255, 255, 255, 0.1);
}

.groupLabel {
  font-size: 1.1rem;
  font-weight: 600;
  color: #FFD700;
  margin-bottom: 1rem;
}

.groupObjects {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
  margin: 1.5rem 0;
  min-height: 100px;
  align-items: center;
}

.groupObject {
  font-size: 2rem;
  animation: fadeInScale 0.5s ease-out;
}

.groupButton {
  background: var(--primary-color);
  border: none;
  border-radius: 8px;
  padding: 0.8rem 1.5rem;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.groupButton:hover {
  background: var(--primary-hover);
  transform: scale(1.05);
}

/* Sequências numéricas */
.sequenceDescription {
  font-size: 0.9rem;
  color: #81C784;
  font-style: italic;
  margin-top: 0.5rem;
}

.sequenceDisplay {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin: 2rem 0;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 16px;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.sequenceNumber {
  font-size: 3rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  padding: 1rem;
  background: rgba(33, 150, 243, 0.2);
  border: 2px solid rgba(33, 150, 243, 0.4);
  border-radius: 12px;
  min-width: 80px;
  text-align: center;
  transition: all 0.3s ease;
}

.sequenceNumber:hover {
  transform: scale(1.05);
  background: rgba(33, 150, 243, 0.3);
}

/* Animações específicas */
@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsividade para atividades estruturais */
@media (max-width: 768px) {
  .comparisonGroups {
    flex-direction: column;
    align-items: center;
  }

  .sequenceDisplay {
    gap: 0.5rem;
    padding: 1rem;
  }

  .sequenceNumber {
    font-size: 2rem;
    min-width: 60px;
    padding: 0.5rem;
  }

  .decompositionOptions {
    gap: 0.5rem;
  }
}

/* Mobile pequeno - V4 */
@media (max-width: 480px) {
  .contagemNumerosGame {
    padding: 0.25rem;
  }

  .gameHeader {
    padding: 0.5rem;
    min-height: 50px;
  }

  .gameTitle {
    font-size: 1.2rem;
  }

  .activitySubtitle {
    font-size: 0.6rem;
  }

  .questionArea {
    padding: 0.75rem;
  }

  .questionTitle {
    font-size: 1rem;
  }

  .objectsDisplay {
    margin: 0.5rem 0;
    padding: 0.75rem;
    gap: 0.5rem;
    min-height: 80px;
  }

  .countingObject {
    font-size: 2rem;
    padding: 0.5rem;
  }

  .answerOptions {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }

  .answerButton {
    padding: 0.75rem;
    font-size: 0.9rem;
    min-height: 50px;
  }

  .optionNumber {
    font-size: 1.2rem;
  }

  .gameStats {
    grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
    gap: 0.25rem;
  }

  .statCard {
    padding: 0.5rem;
  }

  .statLabel {
    font-size: 0.7rem;
  }

  .statValue {
    font-size: 1.2rem;
  }

  /* V4 - Mobile específico */
  .additionDisplay {
    gap: 0.5rem;
    font-size: 1.5rem;
    flex-direction: column;
  }

  .additionNumber, .additionResult {
    padding: 0.5rem;
    min-width: 50px;
    font-size: 1.5rem;
  }

  .additionOperator {
    font-size: 2rem;
  }

  .targetNumber {
    font-size: 2.5rem;
    padding: 1rem;
    min-width: 80px;
  }

  .comparisonGroups {
    flex-direction: column;
    gap: 1rem;
  }

  .comparisonGroup {
    padding: 0.75rem;
    min-width: 100px;
  }

  .groupLabel {
    font-size: 0.8rem;
  }

  .groupObject {
    font-size: 1.2rem;
  }

  .vsIndicator {
    font-size: 1.2rem;
    padding: 0.25rem 0.5rem;
  }

  .ttsButton {
    padding: 0.3rem 0.6rem;
    font-size: 0.9rem;
  }

  .feedbackContent {
    padding: 1rem;
    margin: 1rem;
  }

  .feedbackMessage {
    font-size: 1.2rem;
  }

  .questionArea {
    padding: 0.75rem;
  }

  .questionTitle {
    font-size: 1rem;
  }

  .answerGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .answerButton {
    padding: 1rem;
    font-size: 1.2rem;
  }
  
  .sequenceDisplay,
  .patternSequence {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .sequenceArrow {
    transform: rotate(90deg);
  }
}

/* Suporte a alto contraste */
[data-theme="high-contrast"] {
  --card-background: #000;
  --card-border: 1px solid #fff;
  --success-bg: #28a745;
  --success-border: #1e7e34;
  --error-bg: #dc3545;
  --error-border: #bd2130;
}

/* Suporte a movimento reduzido */
.reduced-motion {
  .answerButton, .controlButton, .countingObject, .feedbackMessage, .soundIndicator, .sequenceMissing {
    animation: none !important;
    transition: none !important;
  }
}

/* 🎯 ESTILOS PARA COMPARAÇÃO DE QUANTIDADE V2 */

/* Indicador de tipo de desafio */
.challengeTypeIndicator {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.challengeBadge {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 0.5rem 1rem;
  color: white;
  font-weight: bold;
  font-size: 0.9rem;
  backdrop-filter: blur(10px);
}

/* Grupos de comparação aprimorados */
.comparisonGroups {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  margin: 2rem 0;
  flex-wrap: wrap;
}

.comparisonGroup {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 1.5rem;
  min-width: 200px;
  max-width: 300px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(15px);
}

.comparisonGroup:hover {
  transform: translateY(-5px) scale(1.02);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Cabeçalho dos grupos */
.groupHeader {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 1rem;
  gap: 0.5rem;
}

.groupLabel {
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
  text-align: center;
  padding: 0.25rem 0.75rem;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 15px;
}

.groupCategory {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  font-style: italic;
}

.groupCount {
  font-size: 1.5rem;
  font-weight: bold;
  color: #FFD700;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #FFD700;
}

/* Objetos dos grupos */
.groupObjects {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.5rem;
  min-height: 100px;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  border: 1px dashed rgba(255, 255, 255, 0.2);
}

.groupObject {
  font-size: 2rem;
  padding: 0.25rem;
  border-radius: 10px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.groupObject:hover {
  transform: scale(1.2);
  filter: brightness(1.2);
}

/* Indicador VS melhorado */
.vsIndicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #FF6B6B, #4ECDC4);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  color: white;
  font-weight: bold;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.vsText {
  font-size: 1.2rem;
  font-weight: bold;
}

.vsSubtext {
  font-size: 0.7rem;
  opacity: 0.8;
}

/* Botões de igualdade */
.equalityButtons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.equalityButton {
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  color: white;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  min-width: 180px;
}

.equalButton {
  border-color: #4CAF50;
  background: rgba(76, 175, 80, 0.2);
}

.equalButton:hover {
  background: rgba(76, 175, 80, 0.4);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
}

.differentButton {
  border-color: #F44336;
  background: rgba(244, 67, 54, 0.2);
}

.differentButton:hover {
  background: rgba(244, 67, 54, 0.4);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(244, 67, 54, 0.3);
}

/* Entrada numérica para diferença */
.differenceInput {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 2rem;
  gap: 1rem;
}

.differencePrompt {
  color: white;
  font-size: 1.2rem;
  font-weight: bold;
  text-align: center;
  margin: 0;
}

.numberButtons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.5rem;
}

.numberButton {
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  color: white;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.numberButton:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Responsividade para comparação */
@media (max-width: 768px) {
  .comparisonGroups {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .comparisonGroup {
    min-width: 280px;
    max-width: 350px;
  }
  
  .vsIndicator {
    width: 60px;
    height: 60px;
    order: 2;
  }
  
  .groupLeft {
    order: 1;
  }
  
  .groupRight {
    order: 3;
  }
  
  .equalityButtons {
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
  }
  
  .equalityButton {
    min-width: 200px;
  }
  
  .numberButtons {
    max-width: 280px;
  }
}