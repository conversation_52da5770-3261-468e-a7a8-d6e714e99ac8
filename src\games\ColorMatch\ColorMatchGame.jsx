/**
 * 🎨 COLOR MATCH V3 - <PERSON>OGO DE CORES COM MÚLTIPLAS ATIVIDADES
 * Portal Betina V3 - Jogo educativo com 6 atividades diversificadas
 */

import React, { useState, useEffect, useCallback, useRef, useContext } from 'react';
import { SystemContext } from '../../components/context/SystemContext.jsx';
import { useAccessibilityContext } from '../../components/context/AccessibilityContext';
import { v4 as uuidv4 } from 'uuid';
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic.js';
import { useTherapeuticOrchestrator } from '../../hooks/useTherapeuticOrchestrator.js';
import { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js';
import { ColorMatchCollectorsHub } from './collectors/index.js';
import { ColorMatchConfig } from './ColorMatchConfig.js';
import { ColorMatchMetrics } from './ColorMatchMetrics.js';
import styles from './ColorMatch.module.css';

// 🎯 SISTEMA DE ATIVIDADES EXPANDIDO V3 - COLOR MATCH
const ACTIVITY_TYPES = {
  SPEED_CHALLENGE: {
    id: 'speed_challenge',
    name: 'Desafio de Velocidade',
    icon: '⚡',
    description: 'Identifique cores rapidamente',
    component: 'SpeedChallengeActivity'
  },
  COLOR_MEMORY: {
    id: 'color_memory',
    name: 'Memória de Cores',
    icon: '🧠',
    description: 'Lembre-se da sequência de cores',
    component: 'ColorMemoryActivity'
  },
  SEQUENCE_COLORS: {
    id: 'sequence_colors',
    name: 'Sequência de Cores',
    icon: '📝',
    description: 'Complete sequências coloridas',
    component: 'SequenceColorsActivity'
  }
};

// 🎯 FUNÇÕES DE GERAÇÃO DE ATIVIDADES INLINE
const generateFindTheColor = (config) => {
  const colors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF'];
  const colorNames = ['Vermelho', 'Verde', 'Azul', 'Amarelo', 'Magenta', 'Ciano'];
  const targetIndex = Math.floor(Math.random() * colors.length);
  const targetColor = colors[targetIndex];
  const colorName = colorNames[targetIndex];
  
  // Criar opções de cores diferentes
  const wrongColors = colors.filter(color => color !== targetColor);
  const shuffledWrong = wrongColors.sort(() => Math.random() - 0.5).slice(0, 3);
  const allOptions = [targetColor, ...shuffledWrong].sort(() => Math.random() - 0.5);
  
  return {
    targetColor,
    targetColorName: colorName,
    colorName,
    correctAnswer: targetColor,
    options: allOptions,
    instruction: `Clique na cor ${colorName}`,
    type: 'color_matching'
  };
};

const generateNameTheColor = (config) => {
  // Cores mais distintas e nomes mais claros
  const colorData = [
    { color: '#FF0000', name: 'Vermelho' },
    { color: '#00FF00', name: 'Verde' },
    { color: '#0000FF', name: 'Azul' },
    { color: '#FFFF00', name: 'Amarelo' },
    { color: '#FF00FF', name: 'Rosa' },
    { color: '#00FFFF', name: 'Ciano' },
    { color: '#FFA500', name: 'Laranja' },
    { color: '#800080', name: 'Roxo' },
    { color: '#008000', name: 'Verde Escuro' },
    { color: '#FFB6C1', name: 'Rosa Claro' },
    { color: '#8B4513', name: 'Marrom' },
    { color: '#000000', name: 'Preto' }
  ];
  
  const targetIndex = Math.floor(Math.random() * colorData.length);
  const target = colorData[targetIndex];
  
  // Criar opções diferentes para tornar única
  const wrongOptions = colorData.filter(item => item.name !== target.name);
  const shuffledWrong = wrongOptions.sort(() => Math.random() - 0.5).slice(0, 3);
  const allOptions = [target.name, ...shuffledWrong.map(item => item.name)].sort(() => Math.random() - 0.5);
  
  return {
    targetColor: target.color,
    colorName: target.name,
    correctAnswer: target.name,
    options: allOptions,
    instruction: 'Qual é o nome desta cor?',
    type: 'name_challenge'
  };
};

const generateMatchTheName = (config) => {
  const colors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#800080', '#FFA500'];
  const colorNames = ['Vermelho', 'Verde', 'Azul', 'Amarelo', 'Roxo', 'Laranja'];
  const targetIndex = Math.floor(Math.random() * colors.length);
  
  // Criar múltiplas cores para combinação
  const selectedColors = [];
  const selectedNames = [];
  for (let i = 0; i < 3; i++) {
    const index = (targetIndex + i) % colors.length;
    selectedColors.push(colors[index]);
    selectedNames.push(colorNames[index]);
  }
  
  return {
    colors: selectedColors,
    names: selectedNames,
    correctAnswer: selectedNames[0],
    options: selectedNames,
    instruction: 'Combine cada cor com seu nome correto',
    type: 'color_name_matching'
  };
};

const generateMemoryMatch = (config) => {
  const colors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF'];
  const sequenceLength = Math.min(Math.floor(Math.random() * 2) + 3, 5); // Max 5 cores
  const sequence = [];
  
  for (let i = 0; i < sequenceLength; i++) {
    sequence.push(colors[Math.floor(Math.random() * colors.length)]);
  }

  return {
    sequence,
    userSequence: [],
    showSequence: true,
    sequenceComplete: false,
    gamePhase: 'showing', // 'showing', 'memorizing', 'reproducing', 'finished'
    currentStep: 0,
    instruction: `Memorize a sequência de ${sequenceLength} cores`,
    type: 'memory_sequence',
    displayTime: 2000, // 2 segundos para mostrar cada cor
    pauseTime: 500    // 0.5 segundo entre cores
  };
};

const generateSequenceRepeat = (config) => {
  const colors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00'];
  const pattern = [colors[0], colors[1], colors[0]];
  const nextColor = colors[1];
  
  return {
    pattern,
    correctAnswer: nextColor,
    options: colors,
    instruction: 'Continue o padrão de cores'
  };
};

const generateColorMixing = (config) => {
  const gradients = [
    'linear-gradient(90deg, #FF0000, #FFFF00)',
    'linear-gradient(90deg, #00FF00, #0000FF)',
    'linear-gradient(90deg, #FF00FF, #00FFFF)'
  ];
  const names = ['Vermelho para Amarelo', 'Verde para Azul', 'Magenta para Ciano'];
  const targetIndex = Math.floor(Math.random() * gradients.length);
  
  return {
    gradient: gradients[targetIndex],
    correctAnswer: names[targetIndex],
    options: names,
    instruction: 'Que transição de cores é esta?'
  };
};

// Geração de dados para reconhecimento de padrões
const generatePatternRecognitionData = (config) => {
  const colors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00'];
  const pattern = [colors[0], colors[1], colors[2]];
  const repeat = [colors[0], colors[1]];
  const correctNext = colors[2];
  
  return {
    pattern,
    repeat,
    correctAnswer: 'ABC',
    options: ['ABC', 'ABB', 'AAB', 'BAC'],
    instruction: 'Qual padrão se repete?'
  };
};

// Geração de dados para gradiente de cores
const generateColorGradientData = (config) => {
  const baseColors = [
    { color: '#FF0000', name: 'Vermelho' },
    { color: '#00FF00', name: 'Verde' },
    { color: '#0000FF', name: 'Azul' }
  ];
  const base = baseColors[Math.floor(Math.random() * baseColors.length)];
  
  // Gerar tons diferentes
  const colors = [
    base.color,
    base.color + '99',
    base.color + '66',
    base.color + '33'
  ];
  
  return {
    colors,
    correctAnswer: '1,2,3,4',
    options: ['1,2,3,4', '4,3,2,1', '2,1,4,3', '3,4,1,2'],
    instruction: `Ordene os tons de ${base.name} do mais escuro para o mais claro`
  };
};

// Geração de dados para sequência temporal
const generateSequenceData = (config) => {
  const colors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00'];
  const sequence = [colors[0], colors[1], colors[2]];
  
  return {
    sequence,
    showSequence: true,
    correctAnswer: colors[0],
    options: colors,
    instruction: 'Reproduza a sequência: clique na primeira cor mostrada'
  };
};

// 🎯 FUNÇÃO DE VERIFICAÇÃO DE RESPOSTA
const checkAnswer = (activityId, answer, activityData) => {
  return answer === activityData.correctAnswer;
};

function ColorMatchGame({ onBack }) {
  const { user, ttsEnabled = true } = useContext(SystemContext);
  const { settings } = useAccessibilityContext();
  const metricsRef = useRef(new ColorMatchMetrics());
  const sessionIdRef = useRef(uuidv4());
  const [collectorsHub] = useState(() => new ColorMatchCollectorsHub());
  
  // 🎮 HOOKS DE LÓGICA UNIFICADA - DEVE VIR PRIMEIRO PARA FORNECER sessionId
  const { startUnifiedSession, endUnifiedSession, recordInteraction, updateMetrics, sessionId, isSessionActive } = useUnifiedGameLogic('colormatch');
  
  const {
    initializeSession: initMultisensory,
    recordInteraction: recordMultisensoryInteraction,
    finalizeSession: finalizeMultisensory,
    updateData: updateMultisensoryData,
    multisensoryData,
    isInitialized: multisensoryInitialized,
    multisensoryIntegration
  } = useMultisensoryIntegration(sessionId, {
    gameType: 'colormatch',
    sensorTypes: {
      visual: true,
      haptic: true,
      tts: ttsEnabled,
      gestural: true,
      biometric: true
    },
    adaptiveMode: true,
    learningStyle: user?.profile?.learningStyle || 'visual'
  });

  const shouldUseTherapeutic = user?.id && user.id !== 'anonymous' && user.id !== '';
  const therapeuticOrchestrator = useTherapeuticOrchestrator({
    userId: shouldUseTherapeutic ? user.id : null
  });

  // =====================================================
  // 🔊 SISTEMA DE TEXT-TO-SPEECH (TTS) PADRONIZADO V3
  // =====================================================
  
  const [ttsActive, setTtsActive] = useState(() => {
    const saved = localStorage.getItem('colorMatch_ttsActive');
    return saved !== null ? JSON.parse(saved) : ttsEnabled;
  });

  const toggleTTS = useCallback(() => {
    setTtsActive(prev => {
      const newState = !prev;
      localStorage.setItem('colorMatch_ttsActive', JSON.stringify(newState));
      if (!newState && 'speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
      return newState;
    });
  }, []);

  const speak = useCallback((text, options = {}) => {
    if (!ttsActive || !('speechSynthesis' in window)) return;
    window.speechSynthesis.cancel();
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'pt-BR';
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;
    window.speechSynthesis.speak(utterance);
  }, [ttsActive]);

  // 🎯 ESTADOS PRINCIPAIS DO JOGO V3 COM SISTEMA DE ATIVIDADES
  const [showStartScreen, setShowStartScreen] = useState(true);
  const [gameStarted, setGameStarted] = useState(false);
  const [cognitiveAnalysisVisible, setCognitiveAnalysisVisible] = useState(false);
  const [analysisResults, setAnalysisResults] = useState(null);
  const [attemptCount, setAttemptCount] = useState(0);

  // 🎯 ESTADO EXPANDIDO COM SISTEMA DE ATIVIDADES V3
  const [gameState, setGameState] = useState({
    score: 0,
    round: 1,
    targetColor: null,
    selectedAnswer: null,
    showFeedback: false,
    accuracy: 100,
    totalRounds: 10,
    difficulty: 'easy',
    roundStartTime: null,
    
    // 🎯 Sistema de atividades
    currentActivity: ACTIVITY_TYPES.SPEED_CHALLENGE.id,
    activityCycle: [
      ACTIVITY_TYPES.SPEED_CHALLENGE.id,
      ACTIVITY_TYPES.COLOR_MEMORY.id,
      ACTIVITY_TYPES.SEQUENCE_COLORS.id
    ],
    activityIndex: 0,
    roundsPerActivity: 10,
    activityRoundCount: 1,
    minRoundsPerActivity: 4,
    maxRoundsPerActivity: 7,
    canSwitchActivity: true, // 🔥 Começar permitindo a primeira escolha livre
    isFirstActivityChoice: true, // 🔥 Flag para permitir primeira escolha
    
    // 🎯 Dados específicos de atividades
    activityData: {
      basicMatching: {
        currentQuestion: null,
        colors: [],
        options: []
      },
      speedChallenge: {
        targetColor: null,
        timeLimit: 5000,
        attempts: 0
      },
      colorMemory: {
        sequence: [],
        userSequence: [],
        showSequence: false,
        gamePhase: 'showing',
        currentStep: 0,
        sequenceComplete: false,
        success: false
      },
      sequenceColors: {
        pattern: [],
        userProgress: []
      }
    },
    
    // 🎯 Métricas comportamentais avançadas
    behavioralMetrics: {
      activityPreferences: {},
      responsePatterns: [],
      adaptiveAdjustments: 0,
      engagementLevel: 1.0,
      difficultyProgression: [],
      errorPatterns: {},
      recoveryRate: 1.0,
      attentionSpan: 0,
      cognitiveLoad: 0.5
    },
    
    // 📊 Estatísticas detalhadas por atividade
    activityStats: {
      speed_challenge: { attempts: 0, correct: 0, averageTime: 0 },
      color_memory: { attempts: 0, correct: 0, averageTime: 0 },
      sequence_colors: { attempts: 0, correct: 0, averageTime: 0 }
    }
  });

  // 🧠 Estados para análise cognitiva avançada
  const [gameStartTime, setGameStartTime] = useState(null);
  const [attemptStartTime, setAttemptStartTime] = useState(null);
  const [gameAttempts, setGameAttempts] = useState([]);
  const [cognitiveAnalysis, setCognitiveAnalysis] = useState(null);
  const [analysisInProgress, setAnalysisInProgress] = useState(false);

  // Conectar métricas ao backend após inicialização
  useEffect(() => {
    if (metricsRef.current && recordInteraction && updateMetrics) {
      metricsRef.current.connectToBackend({ recordInteraction, updateMetrics });
    }
  }, [recordInteraction, updateMetrics]);

  // 🔧 FUNÇÃO UTILITÁRIA - Embaralhar arrays
  const shuffleArray = useCallback((array) => {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  }, []);

  // 🧠 FUNÇÃO DE ANÁLISE COGNITIVA AVANÇADA
  const runCognitiveAnalysis = useCallback(async (attempts) => {
    if (analysisInProgress || attempts.length < 3) return;
    setAnalysisInProgress(true);
    try {
      const gameData = {
        sessionId: sessionId || `session_${Date.now()}`,
        attempts,
        difficulty: gameState.difficulty,
        gameMode: 'standard',
        sessionDuration: Date.now() - gameStartTime,
        totalScore: gameState.score,
        currentLevel: gameState.round
      };
      const analysis = await collectorsHub.runCompleteAnalysis(gameData);
      setCognitiveAnalysis(analysis);
      if (updateMetrics && analysis && !analysis.error) {
        await updateMetrics({
          type: 'cognitive_analysis',
          gameId: 'colormatch',
          analysis: analysis.integratedAnalysis,
          synthesisMetrics: analysis.synthesisMetrics,
          developmentProfile: analysis.developmentProfile
        });
      }
    } catch (error) {
      console.error('❌ Erro na análise cognitiva:', error);
    } finally {
      setAnalysisInProgress(false);
    }
  }, [analysisInProgress, sessionId, gameState, gameStartTime, collectorsHub, updateMetrics]);

  useEffect(() => {
    return () => {
      if (gameAttempts.length >= 3 && !analysisInProgress) {
        runCognitiveAnalysis(gameAttempts);
      }
    };
  }, [gameAttempts, analysisInProgress, runCognitiveAnalysis]);

  // 🧠 useEffect para gerenciar sequência de memória de cores
  useEffect(() => {
    const colorMemoryData = gameState.activityData?.colormemory;
    if (colorMemoryData && gameState.currentActivity === ACTIVITY_TYPES.COLOR_MEMORY.id) {
      if (colorMemoryData.gamePhase === 'showing' && colorMemoryData.showSequence) {
        let currentColorIndex = 0;
        
        const showNextColor = () => {
          if (currentColorIndex < colorMemoryData.sequence.length) {
            // Atualizar estado para mostrar a cor atual
            setGameState(prev => ({
              ...prev,
              activityData: {
                ...prev.activityData,
                colormemory: {
                  ...prev.activityData.colormemory,
                  currentStep: currentColorIndex,
                  gamePhase: 'showing'
                }
              }
            }));
            
            currentColorIndex++;
            
            // Agendar próxima cor ou finalizar sequência
            setTimeout(() => {
              if (currentColorIndex >= colorMemoryData.sequence.length) {
                // Sequência completa, iniciar fase de reprodução
                setTimeout(() => {
                  setGameState(prev => ({
                    ...prev,
                    activityData: {
                      ...prev.activityData,
                      colormemory: {
                        ...prev.activityData.colormemory,
                        showSequence: false,
                        gamePhase: 'reproducing',
                        currentStep: 0
                      }
                    }
                  }));
                }, 1000); // 1 segundo de pausa antes de esconder
              } else {
                showNextColor();
              }
            }, (colorMemoryData.displayTime || 2000) + (colorMemoryData.pauseTime || 500));
          }
        };
        
        // Iniciar a sequência
        showNextColor();
      }
    }
  }, [gameState.activityData?.colormemory?.gamePhase, gameState.activityData?.colormemory?.showSequence, gameState.currentActivity]);

  // 🎯 FUNÇÕES DE GERAÇÃO DE ATIVIDADES V3
  const generateActivityContent = useCallback((activityId, difficulty) => {
    const config = ColorMatchConfig.difficulties[difficulty] || ColorMatchConfig.difficulties.easy;
    switch (activityId) {
      case ACTIVITY_TYPES.SPEED_CHALLENGE.id:
        // Sempre usar o desafio "Qual é o nome desta cor?"
        return generateNameTheColor(config);
      case ACTIVITY_TYPES.COLOR_MEMORY.id:
        return generateMemoryMatch(config);
      case ACTIVITY_TYPES.SEQUENCE_COLORS.id:
        const colors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00'];
        const patternTypes = [
          { pattern: [colors[0], colors[1], colors[2]], next: colors[0] },
          { pattern: [colors[0], colors[1]], next: colors[0] },
          { pattern: [colors[0], colors[1], colors[0]], next: colors[1] }
        ];
        const selectedPattern = patternTypes[Math.floor(Math.random() * patternTypes.length)];
        
        return {
          pattern: selectedPattern.pattern,
          correctAnswer: selectedPattern.next,
          options: colors,
          instruction: 'Continue o padrão de cores',
          type: 'sequence_colors'
        };
      case 'pattern_recognition':
        return generatePatternRecognitionData(config);
      case 'color_gradient':
        return generateColorGradientData(config);
      case 'sequence':
        return generateSequenceData(config);
      default:
        return generateNameTheColor(config);
    }
  }, [shuffleArray]);

  const startGame = useCallback((difficulty) => {
    setShowStartScreen(false);
    setGameStarted(true);
    setGameStartTime(Date.now());
    
    // Obter configurações específicas da dificuldade
    const config = ColorMatchConfig.difficulties[difficulty] || ColorMatchConfig.difficulties.easy;
    
    const initialActivity = ACTIVITY_TYPES.SPEED_CHALLENGE.id;
    const activityContent = generateActivityContent(initialActivity, difficulty);
    
    setGameState(prev => ({
      ...prev,
      difficulty,
      totalRounds: config.totalRounds || 5,
      minRoundsPerActivity: config.minRoundsPerActivity || 4,
      maxRoundsPerActivity: config.maxRoundsPerActivity || 7,
      roundStartTime: Date.now(),
      currentActivity: initialActivity,
      activityData: {
        ...prev.activityData,
        [initialActivity.replace('_', '')]: activityContent
      }
    }));
    
    metricsRef.current.startSession(sessionIdRef.current, user?.id || 'anonymous', difficulty);
    startUnifiedSession(difficulty);
    
    setTimeout(() => {
      const activityName = ACTIVITY_TYPES.SPEED_CHALLENGE.name;
      speak(`Bem-vindo ao Color Match! Vamos começar com ${activityName}. ${activityContent.instruction}`);
    }, 1000);
  }, [generateActivityContent, speak, startUnifiedSession, user?.id]);

  const handleAnswer = useCallback((answer) => {
    setGameState(prev => {
      const currentActivityData = prev.activityData[prev.currentActivity.replace('_', '')];
      const isCorrect = checkAnswer(prev.currentActivity, answer, currentActivityData);
      const responseTime = Date.now() - prev.roundStartTime;
      
      const attemptData = {
        answer,
        isCorrect,
        responseTime,
        difficulty: prev.difficulty,
        round: prev.round,
        activity: prev.currentActivity
      };
      
      setGameAttempts(prevAttempts => [...prevAttempts, attemptData]);
      
      // 📊 REGISTRAR MÉTRICAS BÁSICAS
      if (metricsRef.current) {
        metricsRef.current.recordAnswer(isCorrect, responseTime, {
          correctAnswer: currentActivityData.correctAnswer,
          selectedAnswer: answer,
          activity: prev.currentActivity,
          difficulty: prev.difficulty,
          round: prev.round
        });
      }

      // 🎯 EXECUTAR TODOS OS COLETORES ESPECIALIZADOS
      if (collectorsHub) {
        const gameData = {
          action: 'color_selection',
          answer,
          isCorrect,
          responseTime,
          difficulty: prev.difficulty,
          round: prev.round,
          activity: prev.currentActivity,
          correctAnswer: currentActivityData.correctAnswer,
          selectedAnswer: answer,
          timestamp: Date.now(),
          sessionId: sessionId
        };

        // Processar com todos os coletores em paralelo
        collectorsHub.processInteraction(gameData).then(collectorResults => {
          console.log('🎨 ColorMatch - Coletores executados:', {
            collectorsCount: Object.keys(collectorResults?.collectionResults || {}).length,
            results: collectorResults
          });
        }).catch(error => {
          console.error('❌ Erro nos coletores ColorMatch:', error);
        });
      }

      // 🌐 REGISTRAR INTERAÇÃO MULTISSENSORIAL
      if (recordMultisensoryInteraction) {
        recordMultisensoryInteraction('user_answer', {
          answer,
          isCorrect,
          responseTime,
          activity: prev.currentActivity,
          difficulty: prev.difficulty,
          round: prev.round
        });
      }
      
      return {
        ...prev,
        selectedAnswer: answer,
        showFeedback: true,
        score: isCorrect ? prev.score + 10 : prev.score,
        activityStats: {
          ...prev.activityStats,
          [prev.currentActivity]: {
            ...prev.activityStats[prev.currentActivity],
            attempts: prev.activityStats[prev.currentActivity].attempts + 1,
            correct: isCorrect ? prev.activityStats[prev.currentActivity].correct + 1 : prev.activityStats[prev.currentActivity].correct
          }
        }
      };
    });
  }, []);

  const generateNewRound = useCallback(() => {
    setGameState(prev => {
      const newRound = prev.round + 1;
      const newActivityRoundCount = prev.activityRoundCount + 1;
      const newActivityContent = generateActivityContent(prev.currentActivity, prev.difficulty);
      
      return {
        ...prev,
        round: newRound,
        activityRoundCount: newActivityRoundCount,
        canSwitchActivity: newActivityRoundCount >= prev.minRoundsPerActivity,
        roundStartTime: Date.now(),
        showFeedback: false,
        selectedAnswer: null,
        activityData: {
          ...prev.activityData,
          [prev.currentActivity.replace('_', '')]: newActivityContent
        }
      };
    });
  }, [generateActivityContent]);

  const switchActivity = useCallback((activityId) => {
    setGameState(prev => {
      // Se for a mesma atividade, não faz nada
      if (prev.currentActivity === activityId) {
        return prev;
      }
      
      const newActivityContent = generateActivityContent(activityId, prev.difficulty);
      const newActivityIndex = prev.activityCycle.indexOf(activityId);
      
      if (recordMultisensoryInteraction) {
        recordMultisensoryInteraction('activity_switch', {
          from: prev.currentActivity,
          to: activityId,
          manual: true,
          round: prev.round
        });
      }
      
      return {
        ...prev,
        currentActivity: activityId,
        activityIndex: newActivityIndex !== -1 ? newActivityIndex : 0,
        activityRoundCount: 1, // 🔥 Resetar contador para nova atividade
        canSwitchActivity: true, // 🔥 Manter permissão sempre ativa para permitir voltar
        isFirstActivityChoice: false, // 🔥 Não é mais a primeira escolha
        roundStartTime: Date.now(),
        showFeedback: false,
        selectedAnswer: null,
        activityData: {
          ...prev.activityData,
          [activityId.replace('_', '')]: newActivityContent
        }
      };
    });
  }, [generateActivityContent, multisensoryIntegration]);

  const rotateToNextActivity = useCallback(() => {
    setGameState(prev => {
      const newActivityIndex = (prev.activityIndex + 1) % prev.activityCycle.length;
      const newCurrentActivity = prev.activityCycle[newActivityIndex];
      const newActivityContent = generateActivityContent(newCurrentActivity, prev.difficulty);
      
      if (recordMultisensoryInteraction) {
        recordMultisensoryInteraction('activity_rotation', {
          from: prev.currentActivity,
          to: newCurrentActivity,
          automatic: true,
          round: prev.round
        });
      }
      
      return {
        ...prev,
        activityIndex: newActivityIndex,
        currentActivity: newCurrentActivity,
        activityRoundCount: 0,
        roundStartTime: Date.now(),
        showFeedback: false,
        selectedAnswer: null,
        activityData: {
          ...prev.activityData,
          [newCurrentActivity.replace('_', '')]: newActivityContent
        }
      };
    });
  }, [generateActivityContent, multisensoryIntegration]);

  const explainGame = useCallback(() => {
    const currentActivityInfo = ACTIVITY_TYPES[gameState.currentActivity.toUpperCase().replace('_', '_')];
    if (currentActivityInfo) {
      speak(`Atividade atual: ${currentActivityInfo.name}. ${currentActivityInfo.description}`, { rate: 0.8 });
    }
  }, [gameState.currentActivity, speak]);

  const getAccuracy = useCallback(() => {
    const totalAttempts = Object.values(gameState.activityStats).reduce((sum, stat) => sum + stat.attempts, 0);
    const totalCorrect = Object.values(gameState.activityStats).reduce((sum, stat) => sum + stat.correct, 0);
    return totalAttempts > 0 ? Math.round((totalCorrect / totalAttempts) * 100) : 100;
  }, [gameState.activityStats]);

  // Renderizar conteúdo da atividade atual
  const renderActivityContent = () => {
    const currentActivityData = gameState.activityData[gameState.currentActivity.replace('_', '')];
    if (!currentActivityData) return null;

    switch (gameState.currentActivity) {
      case ACTIVITY_TYPES.SPEED_CHALLENGE.id:
        return renderSpeedChallengeActivity(currentActivityData);
      case ACTIVITY_TYPES.COLOR_MEMORY.id:
        return renderColorMemoryActivity(currentActivityData);
      case ACTIVITY_TYPES.SEQUENCE_COLORS.id:
        return renderSequenceColorsActivity(currentActivityData);
      default:
        return renderBasicMatchingActivity(currentActivityData);
    }
  };

  // Renderizar atividade de combinação básica
  const renderBasicMatchingActivity = (data) => (
    <div className={styles.questionArea}>
      <div className={styles.questionHeader}>
        <h2 className={styles.questionTitle}>{data.instruction}</h2>
      </div>
      
      <div className={styles.objectsDisplay}>
        <div 
          className={styles.countingObject}
          style={{ 
            backgroundColor: data.targetColor,
            width: '120px',
            height: '120px',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto',
            border: '3px solid rgba(255,255,255,0.3)',
            boxShadow: '0 4px 20px rgba(0,0,0,0.2)'
          }}
        />
      </div>

      <div className={styles.answerOptions}>
        {data.options.map((option) => (
          <button
            key={option}
            className={styles.answerButton}
            onClick={() => handleAnswer(option)}
            aria-label={`Escolher ${option}`}
          >
            <span className={styles.optionNumber}>{option}</span>
          </button>
        ))}
      </div>
    </div>
  );

  // 🎨 DESAFIO DE VELOCIDADE - Layout padrão LetterRecognition
  const renderSpeedChallengeActivity = (data) => (
    <div className={styles.questionArea}>
      <div className={styles.questionHeader}>
        <h2 className={styles.questionTitle}>⚡ Qual é o nome desta cor?</h2>
      </div>
      
      <div className={styles.objectsDisplay}>
        <div 
          className={styles.countingObject}
          style={{ 
            backgroundColor: data.targetColor,
            width: '200px',
            height: '200px',
            borderRadius: '20px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto',
            border: '4px solid rgba(255,255,255,0.5)',
            boxShadow: '0 8px 32px rgba(0,0,0,0.3)',
            animation: 'pulse 2s infinite',
            position: 'relative'
          }}
        >
          <div style={{
            position: 'absolute',
            bottom: '-40px',
            left: '50%',
            transform: 'translateX(-50%)',
            fontSize: '1.2rem',
            fontWeight: 'bold',
            color: 'rgba(255,255,255,0.9)',
            textShadow: '0 2px 4px rgba(0,0,0,0.5)'
          }}>
          </div>
        </div>
      </div>

      <div className={styles.answerOptions}>
        {data.options.map((option) => (
          <button
            key={option}
            className={styles.answerButton}
            onClick={() => handleAnswer(option)}
            aria-label={`Escolher ${option}`}
            style={{
              fontSize: '1.1rem',
              fontWeight: 'bold',
              padding: '1rem 2rem',
              minHeight: '80px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <span className={styles.optionText}>{option}</span>
          </button>
        ))}
      </div>

      <div style={{
        textAlign: 'center',
        marginTop: '1rem',
        padding: '1rem',
        backgroundColor: 'rgba(0,0,0,0.3)',
        borderRadius: '8px',
        color: 'rgba(255,255,255,0.8)',
        fontSize: '0.9rem'
      }}>
        <div><strong>⏱️ Desafio de Velocidade:</strong></div>
        <div>Identifique o nome da cor o mais rápido possível!</div>
      </div>
    </div>
  );

  // Renderizar atividade de memória de cores
  const renderColorMemoryActivity = (data) => {
    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle} style={{ fontSize: '2rem' }}>🧠 Memória de Cores</h2>
          <p className={styles.questionSubtitle}>
            {data.gamePhase === 'showing' ? '' :
             data.gamePhase === 'reproducing' ? '' :
             'Concluído!'}
          </p>
        </div>
        
        <div className={styles.objectsDisplay}>
          {/* Mostrar sequência durante fase de memorização */}
          {data.gamePhase === 'showing' && (
            <div style={{
              display: 'flex',
              gap: '1rem',
              justifyContent: 'center',
              alignItems: 'center',
              padding: '2rem',
              backgroundColor: 'rgba(0,0,0,0.3)',
              borderRadius: '12px',
              border: '2px solid rgba(33, 150, 243, 0.5)'
            }}>
              {data.sequence.map((color, index) => (
                <div
                  key={index}
                  style={{ 
                    backgroundColor: index === data.currentStep ? color : 'rgba(255,255,255,0.1)',
                    width: '80px',
                    height: '80px',
                    borderRadius: '50%',
                    border: index === data.currentStep ? '4px solid #FFD700' : '2px solid rgba(255,255,255,0.3)',
                    boxShadow: index === data.currentStep ? '0 0 20px rgba(255, 215, 0, 0.8)' : 'none',
                    transform: index === data.currentStep ? 'scale(1.2)' : 'scale(1)',
                    transition: 'all 0.3s ease',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontWeight: 'bold',
                    color: 'white',
                    fontSize: '1.2rem'
                  }}
                >
                  {index + 1}
                </div>
              ))}
            </div>
          )}
          
          {/* Mostrar progresso durante fase de reprodução */}
          {data.gamePhase === 'reproducing' && (
            <div style={{
              display: 'flex',
              gap: '1rem',
              justifyContent: 'center',
              alignItems: 'center',
              padding: '2rem',
              backgroundColor: 'rgba(0,0,0,0.3)',
              borderRadius: '12px',
              border: '2px solid rgba(76, 175, 80, 0.5)',
              marginBottom: '2rem'
            }}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '1.1rem', fontWeight: 'bold', marginBottom: '1rem' }}>
                  Progresso: {data.userSequence.length} / {data.sequence.length}
                </div>
                <div style={{ display: 'flex', gap: '0.5rem' }}>
                  {data.sequence.map((_, index) => (
                    <div
                      key={index}
                      style={{
                        width: '40px',
                        height: '40px',
                        borderRadius: '50%',
                        backgroundColor: index < data.userSequence.length ? 
                                       'rgba(76, 175, 80, 0.8)' : 'rgba(255,255,255,0.2)',
                        border: '2px solid rgba(255,255,255,0.3)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '1rem',
                        fontWeight: 'bold',
                        color: 'white'
                      }}
                    >
                      {index < data.userSequence.length ? '✓' : index + 1}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
          
          {/* Mostrar resultado final */}
          {data.gamePhase === 'finished' && (
            <div style={{
              textAlign: 'center',
              padding: '2rem',
              backgroundColor: data.success ? 'rgba(76, 175, 80, 0.2)' : 'rgba(244, 67, 54, 0.2)',
              borderRadius: '12px',
              border: `2px solid ${data.success ? 'rgba(76, 175, 80, 0.5)' : 'rgba(244, 67, 54, 0.5)'}`
            }}>
              <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>
                {data.success ? '🎉' : '😔'}
              </div>
              <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>
                {data.success ? 'Perfeito!' : 'Tente novamente!'}
              </div>
            </div>
          )}
        </div>

        {/* Opções de cores para reprodução */}
        {data.gamePhase === 'reproducing' && (
          <div className={styles.answerOptions}>
            {['#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF'].map((color) => (
              <button
                key={color}
                className={styles.answerButton}
                onClick={() => {
                  const newUserSequence = [...data.userSequence, color];
                  const isCorrect = newUserSequence.every((userColor, index) => 
                    userColor === data.sequence[index]
                  );
                  
                  if (newUserSequence.length === data.sequence.length) {
                    // Sequência completa
                    const allCorrect = isCorrect;
                    handleAnswer(allCorrect ? 'correct' : 'incorrect');
                    
                    setGameState(prev => ({
                      ...prev,
                      activityData: {
                        ...prev.activityData,
                        colormemory: {
                          ...prev.activityData.colormemory,
                          userSequence: newUserSequence,
                          gamePhase: 'finished',
                          success: allCorrect
                        }
                      }
                    }));
                  } else {
                    // Continuar sequência
                    setGameState(prev => ({
                      ...prev,
                      activityData: {
                        ...prev.activityData,
                        colormemory: {
                          ...prev.activityData.colormemory,
                          userSequence: newUserSequence
                        }
                      }
                    }));
                  }
                }}
                disabled={data.userSequence.length >= data.sequence.length}
                aria-label={`Escolher cor`}
              >
                <div 
                  style={{
                    width: '60px',
                    height: '60px',
                    borderRadius: '50%',
                    backgroundColor: color,
                    margin: '0 auto',
                    border: '2px solid rgba(255,255,255,0.3)',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.3)'
                  }}
                />
              </button>
            ))}
          </div>
        )}
      </div>
    );
  };

  // Renderizar atividade de sequência de cores
  const renderSequenceColorsActivity = (data) => (
    <div className={styles.questionArea}>
      <div className={styles.questionHeader}>
        <h2 className={styles.questionTitle}>{data.instruction}</h2>
        <div style={{ fontSize: '0.9rem', color: 'rgba(255, 255, 255, 0.8)', marginTop: '0.5rem' }}>
          Padrão: Complete a sequência
        </div>
      </div>
      
      <div className={styles.objectsDisplay}>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', justifyContent: 'center' }}>
          {data.pattern.map((color, index) => (
            <React.Fragment key={index}>
              <div style={{ 
                backgroundColor: color,
                width: '60px',
                height: '60px',
                borderRadius: '50%',
                border: '2px solid rgba(255,255,255,0.3)'
              }} />
              {index < data.pattern.length - 1 && <span style={{ color: 'white' }}>→</span>}
            </React.Fragment>
          ))}
          <span style={{ color: 'white' }}>→</span>
          <div style={{ 
            width: '60px',
            height: '60px',
            borderRadius: '50%',
            border: '2px dashed rgba(255, 255, 255, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontSize: '1.5rem',
            fontWeight: 'bold'
          }}>
            ?
          </div>
        </div>
      </div>

      <div className={styles.answerOptions}>
        {data.options.map((option) => (
          <button
            key={option}
            className={styles.answerButton}
            onClick={() => handleAnswer(option)}
            aria-label={`Continuar com ${option}`}
          >
            <div 
              style={{
                width: '60px',
                height: '60px',
                borderRadius: '50%',
                backgroundColor: option,
                margin: '0 auto',
                border: '2px solid rgba(255,255,255,0.3)'
              }}
            />
          </button>
        ))}
      </div>
    </div>
  );

  // Renderizar atividade de reconhecimento de padrões
  const renderPatternRecognitionActivity = (data) => (
    <div className={styles.questionArea}>
      <div className={styles.questionHeader}>
        <h2 className={styles.questionTitle}>{data.instruction}</h2>
        <div style={{ fontSize: '0.9rem', color: 'rgba(255, 255, 255, 0.8)', marginTop: '0.5rem' }}>
          Identifique o padrão de repetição
        </div>
      </div>
      
      <div className={styles.objectsDisplay}>
        <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center', justifyContent: 'center', flexWrap: 'wrap' }}>
          {data.pattern.map((color, index) => (
            <div
              key={index}
              style={{ 
                backgroundColor: color,
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                border: '2px solid rgba(255,255,255,0.3)',
                animation: `shimmer 2s ease-in-out infinite`,
                animationDelay: `${index * 0.1}s`
              }}
            />
          ))}
          <span style={{ color: 'white', fontSize: '1.2rem', margin: '0 0.5rem' }}>|</span>
          {data.repeat.map((color, index) => (
            <div
              key={`repeat-${index}`}
              style={{ 
                backgroundColor: color,
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                border: '2px solid rgba(255,255,255,0.3)',
                opacity: 0.7
              }}
            />
          ))}
        </div>
      </div>

      <div className={styles.answerOptions}>
        {data.options.map((option) => (
          <button
            key={option}
            className={styles.answerButton}
            onClick={() => handleAnswer(option)}
            aria-label={`Escolher padrão ${option}`}
          >
            <span className={styles.optionNumber}>{option}</span>
          </button>
        ))}
      </div>
    </div>
  );

  // Renderizar atividade de gradiente de cores
  const renderColorGradientActivity = (data) => (
    <div className={styles.questionArea}>
      <div className={styles.questionHeader}>
        <h2 className={styles.questionTitle}>{data.instruction}</h2>
        <div style={{ fontSize: '0.9rem', color: 'rgba(255, 255, 255, 0.8)', marginTop: '0.5rem' }}>
          Ordenar do mais claro ao mais escuro
        </div>
      </div>
      
      <div className={styles.objectsDisplay}>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', justifyContent: 'center', flexWrap: 'wrap' }}>
          {data.colors.map((color, index) => (
            <div
              key={index}
              style={{ 
                backgroundColor: color,
                width: '60px',
                height: '60px',
                borderRadius: '50%',
                border: '2px solid rgba(255,255,255,0.3)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontWeight: 'bold',
                fontSize: '1.2rem',
                textShadow: '0 0 10px rgba(0,0,0,0.8)',
                boxShadow: '0 4px 15px rgba(0,0,0,0.3)'
              }}
            >
              {index + 1}
            </div>
          ))}
        </div>
      </div>

      <div className={styles.answerOptions}>
        {data.options.map((option) => (
          <button
            key={option}
            className={styles.answerButton}
            onClick={() => handleAnswer(option)}
            aria-label={`Ordenação ${option}`}
          >
            <span className={styles.optionNumber}>{option}</span>
          </button>
        ))}
      </div>
    </div>
  );

  // Renderizar atividade de sequência temporal
  const renderSequenceActivity = (data) => (
    <div className={styles.questionArea}>
      <div className={styles.questionHeader}>
        <h2 className={styles.questionTitle}>{data.instruction}</h2>
        <div style={{ fontSize: '0.9rem', color: 'rgba(255, 255, 255, 0.8)', marginTop: '0.5rem' }}>
          Memorize a sequência e reproduza
        </div>
      </div>
      
      <div className={styles.objectsDisplay}>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', justifyContent: 'center', flexDirection: 'column' }}>
          <div style={{ fontSize: '1.1rem', color: 'rgba(255, 255, 255, 0.9)', marginBottom: '1rem' }}>
            Sequência Mostrada:
          </div>
          <div style={{ display: 'flex', gap: '0.5rem' }}>
            {data.sequence.map((color, index) => (
              <div
                key={index}
                style={{ 
                  backgroundColor: color,
                  width: '50px',
                  height: '50px',
                  borderRadius: '50%',
                  border: '2px solid rgba(255,255,255,0.3)',
                  animation: data.showSequence ? `pulse 1s ease-in-out infinite` : 'none',
                  animationDelay: `${index * 0.2}s`,
                  opacity: data.showSequence ? 1 : 0.3
                }}
              />
            ))}
          </div>
        </div>
      </div>

      <div className={styles.answerOptions}>
        {data.options.map((option) => (
          <button
            key={option}
            className={styles.answerButton}
            onClick={() => handleAnswer(option)}
            aria-label={`Reproduzir cor ${option}`}
          >
            <div 
              style={{
                width: '50px',
                height: '50px',
                borderRadius: '50%',
                backgroundColor: option,
                margin: '0 auto',
                border: '2px solid rgba(255,255,255,0.3)'
              }}
            />
          </button>
        ))}
      </div>
    </div>
  );

  return (
    <div 
      className={`${styles.colorMatchGame} ${settings.reducedMotion ? 'reduced-motion' : ''} ${settings.highContrast ? 'high-contrast' : ''}`}
      data-font-size={settings.fontSize}
      data-theme={settings.colorScheme}
      style={{
        fontSize: settings.fontSize === 'small' ? '0.875rem' : 
                 settings.fontSize === 'large' ? '1.25rem' : '1rem'
      }}
    >
      {showStartScreen ? (
        <GameStartScreen
          gameTitle="Color Match"
          gameDescription="Desenvolva sua percepção visual e reconhecimento de cores"
          gameIcon="🎨"
          difficulties={[
            { id: 'easy', name: 'Fácil', description: 'Cores básicas\nIdeal para iniciantes', icon: '😊' },
            { id: 'medium', name: 'Médio', description: 'Mais cores e tons\nDesafio equilibrado', icon: '🎯' },
            { id: 'hard', name: 'Avançado', description: 'Tons sutis e gradientes\nPara especialistas', icon: '🚀' }
          ]}
          onStart={startGame}
          onBack={onBack}
        />
      ) : (
        <div className={styles.gameContent}>
          {/* Header do jogo - padrão LetterRecognition */}
          <div className={styles.gameHeader}>
            <h1 className={styles.gameTitle}>
              🎨 Color Match V3
              <div style={{ fontSize: '0.7rem', opacity: 0.8, marginTop: '0.25rem' }}>
                {ACTIVITY_TYPES[gameState.currentActivity.toUpperCase()]?.name || 'Desafio de Velocidade'}
              </div>
            </h1>
            <button
              className={`${styles.headerTtsButton} ${ttsActive ? styles.ttsActive : ''}`}
              onClick={toggleTTS}
              title={ttsActive ? 'Desativar TTS' : 'Ativar TTS'}
              aria-label={ttsActive ? 'Desativar TTS' : 'Ativar TTS'}
            >
              {ttsActive ? '🔊' : '🔇'}
            </button>
          </div>

          {/* Header com estatísticas - padrão LetterRecognition */}
          <div className={styles.gameStats}>
            <div className={styles.statCard}>
              <div className={styles.statValue}>{gameState.score}</div>
              <div className={styles.statLabel}>Pontos</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statValue}>{gameState.round}</div>
              <div className={styles.statLabel}>Rodada</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statValue}>{gameState.accuracy}%</div>
              <div className={styles.statLabel}>Precisão</div>
            </div>
          </div>

          {/* Menu de atividades - padrão LetterRecognition */}
          <div className={styles.activityMenu}>
            {Object.values(ACTIVITY_TYPES).map((activity) => (
              <button
                key={activity.id}
                className={`${styles.activityButton} ${
                  gameState.currentActivity === activity.id ? styles.active : ''
                }`}
                onClick={() => switchActivity(activity.id)}
              >
                <span>{activity.icon}</span>
                <span>{activity.name}</span>
              </button>
            ))}
          </div>

          {/* Renderização da atividade atual */}
          {renderActivityContent()}

          {/* Controles do jogo - padrão LetterRecognition */}
          <div className={styles.gameControls}>
            <button className={styles.controlButton} onClick={explainGame}>
              🔊 Explicar
            </button>
            <button className={styles.controlButton} onClick={generateNewRound}>
              🔄 Nova Rodada
            </button>
            <button className={styles.controlButton} onClick={onBack}>
              ⬅️ Voltar
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default ColorMatchGame;
